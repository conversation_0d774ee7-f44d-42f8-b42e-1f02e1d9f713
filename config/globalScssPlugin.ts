/** webpack插件方式配置全局variables.scss文件 */
const path = require('path');

module.exports = ({ onGetWebpackConfig }) => {
  onGetWebpackConfig((config) => {
    config.module
      .rule('scss-module')
      .test(/\.(sa|sc|c)ss$/)
      .use('css-loader')
      .loader('css-loader')
      .tap((options) => {
        return {
          ...options,
          alias: {
            css: path.join(__dirname, 'src/css'),
          },
        };
      });
  });
};
