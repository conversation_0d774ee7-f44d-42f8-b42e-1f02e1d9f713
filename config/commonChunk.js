module.exports = ({ onGetWebpackConfig }) => {
  onGetWebpackConfig((chainConfig) => {
    chainConfig.optimization.splitChunks({
      cacheGroups: {
        vendor: {
          test: (args) => {
            const reg = /(react-intl|highlight.js|ice|js-sls-logger|alife|clipboard.js|@alife\/set-spm|clipboard|classnames)/;
            if (args.resource) {
              const matched = /node_modules/.test(args.resource) && reg.test(args.resource);
              if (matched) {
                return true;
              }
            }
          },
          name: 'vendor',
          chunks: 'initial',
          minChunks: 1,
        },
      },
    });
  });
};

