# 帮助中心前端项目

## 技术架构

### 主要技术栈
- **框架**: [Ice.js](https://github.com/alibaba/ice)
- **UI 组件库**: [@alifd/next](https://fusion.design/)
- **状态管理**: React 内置状态管理
- **路由**: Ice.js 提供的路由功能
- **国际化**: [react-intl](https://formatjs.io/docs/react-intl/)
- **构建工具**: Ice.js 提供的构建工具
- **样式管理**: [styled-components](https://styled-components.com/)

### 项目结构
```
src/
├── app.tsx
├── global.scss
├── publicPath.ts
├── routes.ts
├── store.ts
├── typings.d.ts
├── components/
├── constants/
├── hooks/
├── help-fe-common/
├── models/
├── pages/
├── services/
├── solution/
└── utils/
```

## 如何上手开发

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm start
```

### 构建项目
```bash
npm run build
```

### 代码规范检查
```bash
npm run lint
```

## 陈旧的技术选型及优化建议

1. **React 版本**:
   - 当前使用的是 React 16.4.1，建议升级到最新的稳定版本（如 React 18.x）。

2. **Moment.js**:
   - 建议迁移到 `date-fns` 或 `dayjs`。

3. **CSS-in-JS 解决方案**:
   - 可以考虑 `emotion` 或 `linaria`。

4. **国际化解决方案**:
   - 可以考虑 `i18next` 结合 `react-i18next`。

5. **构建工具**:
   - 如果需要更多的自定义构建选项，可以考虑迁移到 `webpack` 或 `vite`。
