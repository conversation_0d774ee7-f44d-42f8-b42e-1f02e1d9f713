{"router": {"lazy": true}, "entry": {"index": "src/app.tsx"}, "plugins": [["build-plugin-fusion", {"themePackage": "@alife/theme-52210"}], ["build-plugin-moment-locales", {"locales": ["zh-cn"]}], "./build.plugin.js", "@ali/build-plugin-ice-def"], "compileDependencies": ["js-sls-logger", "@alife/set-spm", "react-intl"], "proxy": {"/mock": {"enable": true, "target": "https://oneapi.alibaba-inc.com/mock/help-portal"}, "/api/v2": {"enable": true, "target": "https://oneapi.alibaba-inc.com/mock/help-portal"}, "/help/json": {"enable": true, "target": "https://oneapi.alibaba-inc.com/mock/help-portal"}, "/json": {"enable": true, "target": "https://help.aliyun.com"}}, "devServer": {"historyApiFallback": {"disableDotRule": true}}}