const webpack = require('webpack');
const fs = require('fs');

const DEBUG_WEBPACK = false;
// 输出webpack配置json文件，检查构建是否符合预期
const debugWebpackConfig = (filename, config) => {
  if (!DEBUG_WEBPACK) return;

  // 输入到本地文件，方便查看rules的配置信息
  const fd = fs.openSync(filename, 'w');

  fs.writeSync(fd, config.toString(config.module.toConfig().rules));
  fs.closeSync(fd);
};

const executeCommonConfig = (config) => {
  const cache = {
    type: 'filesystem',
    compression: 'gzip',
    buildDependencies: {
      config: ['./build.plugin.js'],
    },
  };
  const snapshot = {
    buildDependencies: {
      // 同时设置时间和哈希
      // 如果时间不同还会进一步匹配哈希
      hash: true,
      timestamp: true,
    },
    module: {
      hash: true,
      timestamp: true,
    },
    resolve: {
      hash: true,
      timestamp: true,
    },
    resolveBuildDependencies: {
      hash: true,
      timestamp: true,
    },
  };
  config.cache(cache);
  config.snapshot = snapshot;

  config.resolve.set('fallback', {
    querystring: false,
  });

  config.resolve.extensions.add('.less');
};

// 分离公共模块
const splitChunks = (config) => {
  config.optimization.splitChunks({
    cacheGroups: {
      vendor: {
        test: (args) => {
          const reg = /(react-intl|highlight.js|ice|js-sls-logger|alife|clipboard.js|@alife\/set-spm|clipboard|classnames)/;
          if (args.resource) {
            const matched = /node_modules/.test(args.resource) && reg.test(args.resource);
            if (matched) {
              return true;
            }
          }
        },
        name: 'vendor',
        chunks: 'initial',
        minChunks: 1,
      },
    },
  });
};

const webConfig = (config) => {
  config.name('web');

  config.externals({
    react: 'React',
    'react-dom': 'ReactDOM',
    lodash: '_',
  });

  executeCommonConfig(config);

  splitChunks(config);

  debugWebpackConfig('test-web.json', config);
};

const ssrConfig = (config) => {
  config.name('ssr');

  // config.externals({
  //   react: 'React',
  //   'react-dom': 'ReactDOM',
  //   lodash: '_',
  // });
  executeCommonConfig(config);

  debugWebpackConfig('test-ssr.json', config);
};

module.exports = (props) => {
  const { onGetWebpackConfig } = props;
  onGetWebpackConfig('web', webConfig);
  onGetWebpackConfig('ssr', ssrConfig);
};
