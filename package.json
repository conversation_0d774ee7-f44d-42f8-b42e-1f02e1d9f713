{"name": "doc-portal-fe", "version": "0.1.0", "description": "帮助中心前端项目", "dependencies": {"@ali/aes-tracker": "^3.3.9", "@ali/aes-tracker-plugin-api": "^3.1.3", "@ali/aes-tracker-plugin-autolog": "^3.0.13", "@ali/aes-tracker-plugin-blank": "^3.0.1", "@ali/aes-tracker-plugin-event": "^3.0.0", "@ali/aes-tracker-plugin-eventTiming": "^3.0.0", "@ali/aes-tracker-plugin-jserror": "^3.0.3", "@ali/aes-tracker-plugin-longtask": "^3.0.1", "@ali/aes-tracker-plugin-perf": "^3.1.0", "@ali/aes-tracker-plugin-pv": "^3.0.6", "@ali/aes-tracker-plugin-resourceError": "^3.0.4", "@ali/cloudshell-js-sdk": "^0.0.4", "@ali/homon-page-delivery": "^1.0.2", "@ali/rachel-player": "^0.1.0", "@alicloud/alfa-react": "^1.5.16", "@alifd/next": "^1.26.36", "@alife/set-spm": "^0.1.7", "@alife/theme-99450": "^0.1.0", "alife-logger": "^1.8.30", "classnames": "^2.3.1", "clipboard": "^2.0.8", "dayjs": "^1.11.10", "highlight.js": "^11.9.0", "html2canvas": "^1.4.1", "http-proxy-middleware": "^2.0.6", "js-sls-logger": "^2.0.0-beta.2", "katex": "^0.16.9", "moment": "^2.30.1", "qrcode.react": "^1.0.1", "query-string": "^7.1.0", "react": "^16.4.1", "react-app-polyfill": "^2.0.0", "react-dom": "^16.4.1", "react-error-boundary": "^4.0.13", "react-intl": "^5.20.4", "react-transition-group": "^4.4.5", "seamless-scroll-polyfill": "^2.3.4", "styled-components": "^5.3.11", "tui-image-editor": "^3.15.3", "url-parse": "^1.5.3", "util": "^0.12.5"}, "devDependencies": {"@ali/build-plugin-ice-def": "^0.1.0", "@ali/build-plugin-ice-spm": "^1.0.5", "@iceworks/spec": "^1.0.0", "build-plugin-fusion": "^0.1.0", "build-plugin-moment-locales": "^0.1.0", "eslint": "^6.8.0", "ice.js": "^2.0.0", "stylelint": "^13.2.0"}, "scripts": {"start": "IS_CSR=true icejs start --mode local --config build-csr.json", "server": "NODE_OPTIONS=--max_old_space_size=8192  icejs start --mode local", "build": "icejs build", "build:local": "NODE_OPTIONS=--max_old_space_size=8192 icejs build --analyzer --mode daily", "lint": "npm run eslint && npm run stylelint", "eslint": "eslint --cache --ext .js,.jsx,.ts,.tsx ./", "eslint:fix": "npm run eslint -- --fix", "stylelint": "stylelint \"**/*.{css,scss,less}\""}, "repository": {"type": "git", "url": "http://gitlab.alibaba-inc.com/aliyun-help/help-portal-fe.git"}, "private": true}