import { APP_MODE } from 'ice';

declare let __webpack_public_path__;
declare let window: any;

if (APP_MODE === 'local' && typeof __webpack_public_path__ !== 'undefined') {
  __webpack_public_path__ = 'https://localhost:3333/';
}
const resourceBaseUrl: string = window?.resourceBaseUrl;
if (typeof window === 'object' && resourceBaseUrl && typeof __webpack_public_path__ !== 'undefined') {
  __webpack_public_path__ = resourceBaseUrl;
}

