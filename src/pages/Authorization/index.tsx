import React, { FunctionComponent, useEffect } from 'react';
import { getSearchParams } from 'ice';
import Loading from '@/help-fe-common/components/common/Loading';

const Authorization: FunctionComponent = () => {
  const search = getSearchParams();

  useEffect(() => {
    const { opener } = window;
    if (opener && search?.response) {
      opener.postMessage(search, '/');
    }
    window.close();
  }, []);

  return <Loading loading style={{ minHeight: '100vh' }} />;
};

export default Authorization;
