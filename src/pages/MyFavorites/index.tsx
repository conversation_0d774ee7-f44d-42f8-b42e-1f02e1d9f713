import React, { useEffect, useState } from 'react';
import { useRequest } from 'ice';
import useFavoritesInfo from '@/hooks/useFavoritesInfo';
import Loading from '@/help-fe-common/components/common/Loading';
import services from '@/services/favorite';
import { FormattedMessage } from 'react-intl';
import Pagination from '@/help-fe-common/components/common/Pagination';
import CollectionCard from './Card';
import useChangeTitle from '@/help-fe-common/hooks/useChangeTitle';
import { fastLogin } from '@/help-fe-common/utils/global/user';

import styles from './index.module.scss';

const Index = () => {
  const { data, request, loading } = useFavoritesInfo({});
  const { request: cancelCollect } = useRequest(services.cancelMyFavorites);
  const [totalPage, setTotalPage] = useState(1);
  const [favoritesData, setFavoritesData] = useState<any>(null);

  useEffect(() => {
    request().then((res) => {
      const pageInfoData = res?.data?.pageInfo?.data;
      if (pageInfoData) {
        setFavoritesData(res?.data?.pageInfo?.data);
      }
    });
  }, [location?.pathname]);

  useEffect(() => {
    if (data?.success === true) {
      const { totalCount, pageSize } = data?.data?.pageInfo;
      if (!isNaN(totalCount / pageSize)) {
        setTotalPage(Math.ceil(totalCount / pageSize));
      }
    } else if (data?.success === false && data?.code === 401) {
      fastLogin(window.location.href);
    }
  }, [data]);

  useChangeTitle({ id: 'help.collection.myCollection' });

  const onChangePageNum = (num) => {
    document.body.scrollIntoView();
    request('', num).then((res) => {
      setFavoritesData(res?.data?.pageInfo?.data);
    });
  };

  const onCancelCollect = (nodeId, id, index) => {
    cancelCollect({ id: nodeId, myFavoriteId: id });
    favoritesData.splice(index, 1);
    if (data?.data?.pageInfo?.totalCount) {
      const totalCount = Number(data?.data?.pageInfo?.totalCount);
      data.data.pageInfo.totalCount = totalCount - 1;
    }
  };

  return (
    <>
      <div className={styles.content}>
        <h1 className={styles.title}><FormattedMessage id="help.collection.myCollection" />({data?.data?.pageInfo?.totalCount || 0})</h1>
        <Loading loading={loading} style={{ position: 'relative', minHeight: '100vh', zIndex: 1 }} />
        {
          !loading && favoritesData ?
            <>
              <ul>
                {
                  favoritesData.map((item, index) => {
                    return <CollectionCard key={index} data={item} dataIndex={index} onCancelCollect={onCancelCollect} />;
                  })
                }
              </ul>
              {
                data?.data?.pageInfo?.totalCount > 0 &&
                <Pagination
                  totalPage={totalPage}
                  totalCount={data?.data?.pageInfo?.totalCount}
                  pageNum={data?.data?.pageInfo?.pageNum}
                  currentSize={data?.data?.pageInfo?.data?.length || 0}
                  onChange={(val) => onChangePageNum(val)}
                />
              }
            </> :
            null
        }
      </div>
    </>);
};

Index.pageConfig = {
  spm: '11174399',
};

export default Index;
