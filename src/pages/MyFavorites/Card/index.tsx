import React, { useState } from 'react';
import { FormattedMessage } from 'react-intl';
import classnames from 'classnames';
import styles from './index.module.scss';

export default ({ data, onCancelCollect, dataIndex }) => {
  const [currentItem, setCurrentItem] = useState(false);

  // 根据id拼接字符串
  const getUrl = () => {
    return `/document_detail/${data?.nodeId}.html`;
  };
  const lastCrumbIndex = data?.directoryPath?.length - 1;
  return (
    <li className={styles.cardContainer}>
      <div className={styles.leftSection}>
        <h1><a href={getUrl()} target="_blank" className={styles.commonAStyle} rel="noreferrer">{data?.title}</a></h1>
        <p className={styles.description}>
          <a href={getUrl()} target="_blank" rel="noreferrer">{data?.contentHtml}</a>
        </p>
        <div className={styles.breadcrumb} data-spm-click="gostr=/aliyun;locaid=crumb">
          <a
            href="/"
            target="_blank"
            className={styles.crumb}
          >
            <FormattedMessage id="help.doc.crumb.homePage" />
          </a>
          {
            data?.directoryPath?.map((item, index) => (
              <span key={index}>
                <span className={styles.arrow}>&gt;</span>
                {index < lastCrumbIndex ?
                  <a
                    href={item?.url}
                    className={styles.crumb}
                  >
                    {item.title}
                  </a> :
                  <span>{item.title}</span>
                }
              </span>
            ))
          }
        </div>
      </div>
      <div className={styles.rightSection}>
        <div
          className={classnames(styles.collectCell, currentItem ? styles.cancel : styles.done)}
          onMouseEnter={() => setCurrentItem(true)}
          onMouseLeave={() => setCurrentItem(false)}
          onClick={() => onCancelCollect(data?.nodeId, data?.id, dataIndex)}
        >
          <FormattedMessage
            id={classnames(currentItem ?
              'help.collection.cancelCollected' : `${data?.status === 2 ? 'help.collection.collected' : 'help.collection.failure'}`)}
          />
        </div>
      </div>

    </li>
  );
};
