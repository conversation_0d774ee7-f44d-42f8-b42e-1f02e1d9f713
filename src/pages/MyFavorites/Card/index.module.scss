.cardContainer {
  border-bottom: 1px solid #ececec;
  padding-bottom: 30px;
  position: relative;
  display: flex;
  flex-direction: row;

  .leftSection {
    flex: 1;
    padding-right: 40px;

    h1 {
      margin-top: 30px;
      font-size: 24px;
      border-bottom: 1px dashed #d7d8d9;
      padding-bottom: 20px;
      margin-bottom: 30px;
      color: #373d41;
    }

    .commonAStyle {
      color: #373d41;
    }

    .commonAStyle:hover {
      text-decoration-line: underline;
    }

    .description {
      margin-top: 16px;
      font-size: 14px;
      color: #73777a;

      >a {
        color: #73777a;
      }

      >a:hover {
        text-decoration-line: underline;
      }
    }

    .breadcrumb {
      text-decoration: none;
      display: flex;
      margin-top: 15px;
      color: #d7d8d9;
      flex-wrap: wrap;
    }
  }

  .rightSection {
    flex: 0 0 160px;
    border-left: 1px dashed #d7d8d9;
    position: relative;

    .collectCell {
      width: 80px;
      height: 80px;
      line-height: 80px;
      text-align: center;
      border-radius: 50%;
      display: inline-block;
      position: absolute;
      transform: translate(50%, 50%);
      cursor: pointer;
      font-size: 14px;
    }

    .done {
      color: #32aa46;
      background-color: #ebf7ed;
    }

    .cancel {
      color: #73777a;
      background-color: #f4f4f4;
    }
  }

  @media screen and (max-width: 500px) {
    .leftSection {
      padding-right: 0;
    }

    .rightSection {
      display: none;
    }
  }
}