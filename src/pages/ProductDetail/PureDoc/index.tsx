import React, { useEffect } from 'react';
import { handleGlobalStyle } from '@/help-fe-common/utils/global/style/handleStyle';
import Detail from '../Detail';

const pureModeClassList = ['.main-layout', '.help-body-head', '.aliyun-docs-menu', '.aliyun-docs-side',
  '.aliyun-docs-feedback', '.aliyun-docs-pagination', '.aliyun-docs-recommend', '.aliyun-docs-view-header'];

const iframeWhiteHost = ['bailian.console.aliyun.com'];

const PureDoc = ({ docInfo }) => {
  // iframe监听事件，用于传递当前页面的高度
  const listenerIframeMessage = () => {
    window.addEventListener('message', (e: any) => {
      if (!iframeWhiteHost?.some((host) => e.origin?.indexOf(host) > -1) || !e.origin) return;
      // 事件名：getIframeHeight，用于百炼文档获取iframe高度
      if (e.data === 'getIframeHeight' && e.source) {
        try {
          e.source.postMessage({ height: document.body.scrollHeight }, e.origin);
        } catch (error) {
          console.error('Failed to post message:', error);
        }
      }
      //
    }, false);
  };

  useEffect(() => {
    if (docInfo) {
      // 隐藏dom节点
      handleGlobalStyle(pureModeClassList, false);
      // 特殊样式处理
      const className = '.markdown-body';
      document.querySelector(className)?.setAttribute('style', 'margin:0');
      // 监听iframe消息
      listenerIframeMessage();
    }
  }, [docInfo]);

  return (
    <section
      className="aliyun-docs-content help-pure-doc-container"
    >
      <Detail data={docInfo} />
    </section>
  );
};
export default PureDoc;
