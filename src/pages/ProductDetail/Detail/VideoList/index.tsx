import React, { useState, useEffect } from 'react';
import { useHistory } from 'ice';
import { aLinkTrigger } from '@/help-fe-common/utils/global/url/aLinkTrigger';
import { generateUrl } from '@/help-fe-common/utils/global/url/generateUrl';
import useDocumentDetail from '@/help-fe-common/hooks/useDocumentDetail';
import Pagination from '@/help-fe-common/components/common/Pagination';
import CoverImage from '@/help-fe-common/components/pc/CoverImage';
import styles from './index.module.scss';

export default ({ data }) => {
  const [videoData, setVideoData] = useState<any>(null);
  const [totalPage, setTotalPage] = useState(0);
  const { request: getDocumentDetail } = useDocumentDetail({});
  const history = useHistory();

  const changePage = (pageNum) => {
    getDocumentDetail({ nodeId: data?.nodeId, pageNum, pageSize: 20 }).then((res) => {
      if (res?.data?.subNodePage) {
        setVideoData(res?.data?.subNodePage);
      }
      document.documentElement.scrollTop = 64;
    });
  };

  useEffect(() => {
    setVideoData(data?.subNodePage);
    setTotalPage(Math.ceil(data?.subNodePage?.totalCount / data?.subNodePage?.pageSize));
  }, [data]);

  return (
    <div className={styles.videoList}>
      {
        videoData?.data?.map((item, index) => (
          <div key={index} className={styles.videoItem}>
            <a href={generateUrl(item)} onClick={(e) => aLinkTrigger(e, history, { from: 'detail' })} className={styles.videoItemPoster}>
              <CoverImage title={item.title} />
            </a>
          </div>
        ))
      }
      {
        totalPage > 1 &&
        <div className={styles.pagination}>
          <Pagination
            totalPage={totalPage}
            totalCount={videoData?.totalCount}
            pageNum={videoData?.pageNum}
            pageSize={videoData?.pageSize}
            currentSize={videoData?.data?.length}
            onChange={(num) => { changePage(num); }}
          />
        </div>
      }
    </div>
  );
};
