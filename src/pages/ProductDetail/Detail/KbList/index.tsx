import React, { useState, useEffect } from 'react';
import { useHistory } from 'ice';
import useDocumentDetail from '@/help-fe-common/hooks/useDocumentDetail';
import { aLinkTrigger } from '@/help-fe-common/utils/global/url/aLinkTrigger';
import { generateUrl } from '@/help-fe-common/utils/global/url/generateUrl';
import Pagination from '@/help-fe-common/components/common/Pagination';

import styles from './index.module.scss';

export default ({ data }) => {
  const [kbData, setKbData] = useState<any>(null);
  const [totalPage, setTotalPage] = useState(0);
  const { request: getDocumentDetail } = useDocumentDetail({});
  const history = useHistory();

  const changePage = (pageNum) => {
    getDocumentDetail({ nodeId: data?.nodeId, pageNum, pageSize: 20 }).then((res) => {
      if (res?.data?.subNodePage) {
        setKbData(res.data?.subNodePage);
      }
      document.documentElement.scrollTop = 64;
    });
  };

  useEffect(() => {
    setKbData(data?.subNodePage);
    setTotalPage(Math.ceil(data?.subNodePage?.totalCount / data?.subNodePage?.pageSize));
  }, [data]);

  return (
    <div className={styles.list}>
      <ul className={styles.clearfix}>
        {
          kbData?.data.map((item, index) => (
            <li key={index} className={styles.listItem}>
              <a href={generateUrl(item)} onClick={(e) => aLinkTrigger(e, history, { from: 'detail' })}>
                <span className={styles.circle} />
                {item.title}
              </a>
            </li>
          ))
        }
      </ul>
      {
        totalPage > 1 &&
        <div className={styles.pagination}>
          <Pagination
            totalPage={totalPage}
            totalCount={kbData?.totalCount}
            pageNum={kbData?.pageNum}
            pageSize={kbData?.pageSize}
            currentSize={kbData?.data?.length}
            onChange={(num) => { changePage(num); }}
          />
        </div>
      }
    </div>
  );
};
