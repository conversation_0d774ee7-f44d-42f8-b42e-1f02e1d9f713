.list {
  display: block;
  width: 100%;
  margin-top: 25px;
  margin-bottom: 35px;

  .clearfix::before {
    content: "";
    display: table;
  }

  .clearfix::after {
    visibility: hidden;
    display: block;
    height: 0;
    font-size: 0;
    content: " ";
    clear: both;
  }

  .listItem {
    list-style: none;
    font-size: 14px;
    margin: 20px 0;
    height: 17px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 17px;
    margin-bottom: 12px;
    padding-right: 30px;

    a {
      color: #373d41;
      transition: inherit;

      .circle {
        display: inline-block;
        margin-right: 6px;
        position: relative;
        top: -3px;
        background: #373d41;
        width: 4px;
        height: 4px;
      }
    }

    a:hover {
      color: #1366ec;
    }
  }

  @media screen and (max-width: 1100px) {
    .listItem {
      float: none;
      width: 100%;
    }
  }

  @media screen and (min-width: 1100px) {
    .listItem {
      float: left;
      width: 50%;
    }
  }

  .pagination {
    width: 100%;
    min-width: 600px;
    margin: 0 auto;
  }
}