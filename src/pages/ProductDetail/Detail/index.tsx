import React, { useEffect, useMemo, useState } from 'react';
import { useHistory } from 'ice';
import VideoList from './VideoList';
import KbList from './KbList';
import NextPage from '@/help-fe-common/components/common/ProductDetail/NextPage';
import { generateUrl } from '@/help-fe-common/utils/global/url/generateUrl';
import { NODE_TYPE, SHOW_TYPE } from '@/help-fe-common/constants/detail';
import './index.scss';

export default ({ data }) => {
  const [state, setState] = useState(false);
  const history = useHistory();

  const hashListener = (attempts = 0) => {
    const maxAttempts = 10; // 尝试次数上限为10次，因为每次间隔为1s
    const id = history?.location?.hash.slice(1);
    const element = document.getElementById(id);
    let hashChangeTimer;

    if (hashChangeTimer) clearTimeout(hashChangeTimer);

    // 如果元素存在且不是 MAIN 标签，则滚动到元素
    if (element && element.tagName !== 'MAIN') {
      setTimeout(() => {
        element.scrollIntoView();
      }, 100);
      return;
    }

    // 如果尝试次数超过最大值，则停止
    if (attempts < maxAttempts) {
      hashChangeTimer = setTimeout(() => {
        hashListener(attempts + 1);
      }, 1000);
    }
  };

  useEffect(() => {
    // 每次跳转页面都要remove，解决img没有close跳转页面导致无法scroll的问题
    document.getElementsByTagName('body')?.[0]?.removeAttribute('style');
    if (state && history?.location?.hash) {
      hashListener(0);
    }
  }, [data?.nodeId]);

  const selectDetail = (nodeType, showType) => {
    if (nodeType === NODE_TYPE.DIRECTORY && showType === SHOW_TYPE.KB_LIST) {
      return (
        <KbList data={data} />
      );
    } else if (nodeType === NODE_TYPE.DIRECTORY && showType === SHOW_TYPE.VIDEO_LIST) {
      return (
        <VideoList data={data} />
      );
    } else if (data?.subNodePage?.data?.length) {
      return (
        <div className="markdown-body">
          <ul>
            {
                (data?.subNodePage?.data || [])?.map((item, index) => (
                  <li key={index} >
                    <a href={generateUrl(item)}>{item?.docTitle || item?.title}</a>
                  </li>
                ))
              }
          </ul>
        </div>
      );
    } else {
      setState(true);
      return (<div className="markdown-body" dangerouslySetInnerHTML={{ __html: data?.content }} />);
    }
  };

  const detailData = useMemo(() => selectDetail(data?.nodeType, data?.showType), [data]);

  return (
    <div className="pc-markdown-container unionContainer" id="pc-markdown-container">
      {detailData}
      {data?.nodeId && <NextPage />}
    </div>
  );
};
