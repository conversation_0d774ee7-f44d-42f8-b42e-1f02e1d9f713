.paragraph {
  color: #585858;
  font-size: 15px;
}

.table-icon {
  font-family: "help-iconfont" !important;
  font-size: 16px;
  font-style: normal;
  color: #666;
  -webkit-font-smoothing: antialiased;
  margin-right: 10px;
}

.code-scroll-thumb {
  overflow-x: auto;
  scrollbar-width: 6px; // firefox滚动条隐藏

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #d8d8d8;
    border-radius: 3px;
  }
}

.step-ol {
  padding: 0 0 0 45px;
  background-color: unset;
}

.step2-ol {
  padding: 26px 24px 23px 45px;
  background-color: #FBFBFB;
  margin-left: -25px;
}

.step-li {
  margin: 6px 0;
  padding: 0;
  border: none;
}

.step1-li {
  margin: 0;
  padding: 24px 0 30px 0;
  border-bottom: 1px solid #f3f3f3;

  &:first-child {
    padding-top: 0;
  }

  &:last-child {
    padding-bottom: 0;
    border-bottom: none;
  }
}

.step2-li {
  margin: 0;
  border-bottom: 1px solid #f3f3f3;
  padding: 24px 0 30px 0;

  &:first-child {
    padding-top: 0;
  }

  &:last-child {
    padding-bottom: 0;
    border-bottom: none;
  }
}



// 教程时间、pipcode等设置隐藏
.time,
.steptime,
.pipcode {
  display: none;
}

// 教程内容自定义样式
.help-tutorial-container {
  .pc-markdown-container {
    .markdown-body {

      margin: 0;
      padding-top: 20px;
      background-color: #fff;

      ///////////////////// 步骤 ////////////////////////
      .section.manual,
      .section.onestop {
        display: none;

        // 各步骤样式
        >.intro,
        >.step,
        >.sum {
          display: none;

          >h3,
          >section:first-child>h3 {

            &:first-child {
              display: none;
            }
          }
        }

        // 教程简介
        >.intro {
          >.section {
            flex: 1;
          }

          /** intro教程简介中表格 */
          >table {
            width: 454px;
            max-width: 40%;
            height: 286px;
            margin: 0;
            margin-left: 100px;
            border: 1px solid #dfdfdf;
            border-top: 4px solid #1366EC;
            background-image: url(https://img.alicdn.com/imgextra/i4/O1CN01WK74vu1pEH3zen17q_!!6000000005328-2-tps-908-572.png);
            background-repeat: no-repeat;
            background-position: bottom right;
            background-size: cover;

            colgroup {

              // 设置第一列宽度为表格50%
              &:first-child,
              &:nth-child(2) {
                width: 50% !important;
              }
            }

            tr,
            td {
              border: none;
              vertical-align: baseline;
              background: none !important; // 教程简介中表格背景色处理
              padding: 12px;

              ul {
                margin: 0;
                padding-left: 0;
                overflow: hidden;
              }
            }

            tr {

              // 第一行td单元格
              &:first-child {
                td {
                  padding-top: 30px;

                  &:first-child>p::before {
                    // 难度
                    content: "\e7d4";
                  }
                }
              }

              //第二行td单元格
              &:nth-child(2) {
                td:first-child>p::before {
                  // 时间
                  content: "\e7d5";
                }
              }

              //第三行td单元格
              &:nth-child(3) {
                td:first-child>p::before {
                  // 技术
                  content: "\e7d2";
                }
              }

              // 最后一行td单元格
              &:last-child {
                td {
                  padding-bottom: 20px;

                  &:first-child>p::before {
                    // 费用
                    content: "\e7d3";
                  }
                }
              }

              td {

                // 表格中文字14px
                p,
                span {
                  font-size: 14px;
                  line-height: 20px;
                }

                // 第一列td单元格
                &:first-child {
                  padding-left: 30px;
                  display: flex;
                  align-items: center;

                  p {
                    color: #181818;

                    &::before {
                      @extend .table-icon;
                    }
                  }
                }

                // 最后一列td单元格
                &:last-child {
                  padding-right: 30px;
                }
              }
            }
          }
        }

        //总结
        >.sum {
          >.test {
            .explain {
              visibility: hidden;
            }

            .rightExplain {
              visibility: visible;
              color: #63BA4D;
            }

            .wrongExplain {
              visibility: visible;
              color: #F15533;
            }

            section {
              margin-bottom: 26px;
            }

            ul {
              margin: 10px 0;
              padding-left: 0;

              li {
                cursor: pointer;
                display: flex;
                align-items: flex-start;

                &:hover {
                  color: #1366EC;
                }

                input,
                label {
                  cursor: pointer;
                  margin-left: 10px;
                }

                input {
                  margin-top: 8px;
                }
              }
            }

            .help-tutorial-test-forbidden {
              cursor: not-allowed;
              pointer-events: none;
            }
          }
        }

        .help-tutorial-step-show {
          display: block;
        }

        .help-tutorial-step-flex {
          display: flex;
        }

        .help-tutorial-step-hide {
          display: none;
        }
      }

      ///////////////////// 区块/段落 ////////////////////////
      .section {
        margin: 0;

        &.collapse {
          margin: 5px 0;
        }

        // section下多段落设置margin-bottom
        >p {
          margin-bottom: 16px;
        }
      }

      p,
      span {
        line-height: 24px;
        @extend .paragraph;
      }

      p:empty {
        display: none
      }

      p.time>.ph,
      .ph.time,
      p.steptime>.ph,
      .ph.steptime,
      .ph.url {
        display: none;
      }

      .help-tutorial-flex {
        display: flex;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        >section.section {
          &:first-child {
            margin-right: 50px;
            flex: 1;
          }

          &:last-child {
            width: calc(50% - 50px);
          }
        }

      }

      // 响应式配置
      @media screen and (max-width:1055px) {

        .section.manual,
        .section.onestop {
          .help-tutorial-flex {
            display: block;

            >section.section {

              .help-code-block,
              .help-tutorial-img-container {
                margin: 6px 0;
              }

              &:last-child {
                width: unset
              }
            }
          }

          .help-tutorial-step-flex {
            display: block;
          }

          .intro {
            >table {
              margin-left: 0;
              max-width: 100%;
            }
          }
        }
      }

      ///////////////////// 列表 ////////////////////////
      ul,
      ol {
        @extend .paragraph;
        margin: 16px 0;

        &:last-child {
          margin-bottom: 0;
        }
      }

      ol {
        >li {
          @extend .step1-li;

          // 二级设置子步骤样式
          ol {
            @extend .step2-ol;

            >li {
              @extend .step2-li;

              // 三级ol、；o不设置特殊padding
              ol {
                @extend .step-ol;

                >li {
                  @extend .step-li;
                }
              }
            }
          }
        }
      }

      ///////////////////// 标题 ////////////////////////
      h1,
      h2 {
        display: none;
      }

      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin: 0;
        padding: 0;
        color: #373d41;
        font-weight: bold;
      }

      h1 {
        margin-top: 26px;
        margin-bottom: 12px;
        font-size: 26px;

        &.title {
          // 预览的标题是直接在内容中的，所以去除顶部的默认margin
          margin-top: 0;
        }
      }

      h2 {
        margin-top: 26px;
        margin-bottom: 12px;
        font-size: 22px;
      }

      h3 {
        margin-top: 26px;
        margin-bottom: 12px;
        font-size: 20px;
      }

      h4 {
        margin-top: 26px;
        margin-bottom: 12px;
        font-size: 18px;
      }

      h5 {
        margin-top: 20px;
        margin-bottom: 10px;
        font-size: 16px;
      }

      h6 {
        margin-top: 16px;
        margin-bottom: 8px;
        font-size: 14px;
      }

      ///////////////////// 代码块 ////////////////////////
      .help-code-block {
        padding: 8px;
        margin: 16px 0;
        background-color: #F6F6F6;
        border: 1px solid #EBEBEB;

        &:first-child {
          margin-top: 0;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }

      pre.codeblock {
        margin: 0;
        @extend .code-scroll-thumb;

        >code {
          @extend .code-scroll-thumb;
        }
      }

      ///////////////////// tab ////////////////////////
      .tabbed-content-box {
        border: unset;

        .tab-box {
          height: 32px;
          border: unset;

          .tab-item-container {
            .tab-item {
              margin-left: 0;
              margin-right: 20px;
              padding-bottom: 5px;
              font-size: 14px;
              color: #1366EC;
              line-height: 17px;
            }
          }

          .tab-box-button {
            display: none;
          }
        }

        section.section {
          padding: 16px 0;
        }
      }

      ///////////////////// 表格 ////////////////////////
      table {
        margin: 16px 0;

        &:last-child {
          margin-bottom: 0;
        }

        tr {
          &:first-child {
            td {
              background-color: #f6f6f6 !important;
            }
          }
        }

        td {
          &:first-child {
            border-left: 1px solid #dfdfdf;
          }

          &:last-child {
            border-right: 1px solid #dfdfdf;
          }
        }

        p {
          margin: 0;
        }

        tr td ol {
          margin-left: 0;
        }
      }

      ///////////////////// 图片 ////////////////////////

      .help-tutorial-img-container {
        width: 100%;
        padding: 8px;
        margin: 16px 0;
        background-color: #F6F6F6;
        border: 1px solid #EBEBEB;
        display: flex;
        align-items: center;
        justify-content: center;

        &:first-child {
          margin-top: 0;
        }

        &:last-child {
          margin-bottom: 0;
        }

        >.image.break {
          max-width: 100%;
          margin: unset;
          box-shadow: unset;
        }
      }

      ///////////////////// note ////////////////////////
      .note {
        padding: 8px 12px;
      }

      .note-fastpath,
      .note-remember,
      .note-note,
      .note-tip,
      .note-attention,
      .note-restriction,
      .note-important,
      .note-notice,
      .note-warning,
      .note-trouble,
      .note-caution,
      .note-danger {
        background-color: #FAFAFA;
        border: 1px solid #EDEDED;
      }
    }
  }
}