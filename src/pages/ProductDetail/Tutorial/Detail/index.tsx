/* eslint-disable react/no-danger */
import React from 'react';
import classNames from 'classnames';
import { FormattedMessage } from 'react-intl';
import { IStep } from '@/utils/helpDoc/tutorialProcessor/tutorial';
import styles from './index.module.scss';
import './index.scss';

interface IProps {
  content: string; // html转义的string
  stepData: IStep;
}
const Detail = ({ content, stepData }: IProps) => {
  return (
    <div className={classNames(styles.container, 'pc-markdown-container unionContainer')}>
      {
        Boolean(stepData?.title) &&
        <div className={styles.stepHead}>
          <div className={styles.stepTitle}>
            {stepData?.title}
          </div>
          {
            stepData?.stepTime ?
              <div className={styles.stepTime}>
                <FormattedMessage id="help.tutorial.stepTime" />
                {stepData?.stepTime}
                <FormattedMessage id="help.tutorial.timeUnit" />
              </div>
              :
              null
          }
        </div>
      }
      <div className="markdown-body" dangerouslySetInnerHTML={{ __html: content }} />
    </div>
  );
};

export default Detail;
