import React from 'react';
import classNames from 'classnames';
import { FormattedMessage } from 'react-intl';
import { tutorialTypeMap, tutorialHeadText } from '@/constants/tutorial';
import { DocDetailTypeEnum } from '@/help-fe-common/constants/detail';
import { ITutorial } from '@/utils/helpDoc/tutorialProcessor/tutorial';
import { tryClick } from '@/utils/helpDoc/tutorialProcessor/tryClick';
import styles from './index.module.scss';

interface IProps {
  docData: any;
  tutorialType: DocDetailTypeEnum;
  tutorialData: ITutorial;
  currentLabel: string;
  onChangeMode: Function;
}
const TopHead = ({ docData, tutorialData, tutorialType, currentLabel, onChangeMode }: IProps) => {
  return (
    <div className={classNames('center-container', styles.container)}>
      <div className={styles.title} title={docData?.title}>
        {docData?.title}
      </div>
      <div className={styles.right}>
        <div className={styles.toolbar}>
          {
            tutorialTypeMap?.[tutorialType]?.filter((item) => tutorialData?.[item?.label]?.category)?.length > 1 &&
            tutorialTypeMap?.[tutorialType]?.map((item) => (
              <button
                key={item?.label}
                className={classNames(styles.switchButton, item?.label === currentLabel ? styles.selected : '')}
                onClick={() => onChangeMode(item?.label)}
              >
                {item?.category}
              </button>
            ))
          }
        </div>
        <div className={styles.info}>
          {
            Boolean(tutorialData?.[currentLabel]?.totalTime) &&
            <div className={styles.time}>
              <FormattedMessage id="help.tutorial.totalTime" />
              {tutorialData?.[currentLabel]?.totalTime}
              <FormattedMessage id="help.tutorial.timeUnit" />
            </div>
          }
          {
           (tutorialType === DocDetailTypeEnum.TUTORIAL || Boolean(tutorialData?.[currentLabel]?.url)) &&
           <div className={styles.tryButton} onClick={(e) => { tryClick(tutorialData?.[currentLabel]?.url, tutorialType); }}>
             <FormattedMessage id={tutorialHeadText[tutorialType].buttonText} />
           </div>
          }
        </div>
      </div>
    </div>
  );
};
export default TopHead;
