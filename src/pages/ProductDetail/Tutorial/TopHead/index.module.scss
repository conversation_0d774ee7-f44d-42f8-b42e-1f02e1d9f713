.container {
  height: 54px;
  color: #555;
  margin-bottom: -1px;
  display: flex;

  .title {
    padding: 15px 0 17px 0;
    margin-right: 100px;
    font-weight: 500;
    font-size: 16px;
    color: #181818;
  }

  .right {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;

    .toolbar {
      display: flex;
      align-items: center;

      .switchButton {
        padding: 15px 16px;
        font-size: 14px;
        text-align: center;
        line-height: 22px;
        margin-bottom: -2px;
        cursor: pointer;
        user-select: none;
        background: none;
        border: none;
        border-bottom: 2px solid transparent;
        -webkit-tap-highlight-color: transparent;
      }

      .selected {
        color: #1366EC;
        border-color: #1366EC;
      }
    }

    .info {
      padding: 12px 0;
      display: flex;

      .time {
        font-size: 14px;
        padding: 5px 0;
        margin-right: 10px;
        white-space: nowrap;
      }

      .tryButton {
        height: 28px;
        width: 80px;
        line-height: 28px;
        background: #ff6a00;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        text-align: center;
        color: #fff;

        &:hover {
          background-color: #ff791a;
        }
      }
    }
  }
}

@media screen and (max-width:800px) {
  .container {
    .title {
      max-width: 300px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-right: 0;
    }

    .right {
      .toolbar {
        .switchButton {
          padding: 15px 10px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .info {
        .time {
          min-width: 70px;
        }
      }
    }
  }
}

@media screen and (max-width:500px) {
  .container {
    height: auto;
    display: block;

    .title {
      max-width: 100%;
      padding: 10px 24px;
      margin-right: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      text-align: center;
    }

    .right {
      .toolbar {
        width: 100%;

        .switchButton {
          padding: 5px 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 50%;
        }
      }

      .info {
        display: none;
      }
    }
  }
}