.container {
  width: 100%;
  height: 82px;
  display: flex;
  position: relative;

  .mainContainer {
    height: 82px;
    width: 100%;
    display: flex;
    align-items: center;
    border-top: 1px solid #e5e5e5;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none; // firefox滚动条隐藏

    &::-webkit-scrollbar {
      display: none;
    }

    .stepCell {
      height: 100%;
      flex: 1;
      min-width: 154px;
      background-color: #f1f1f1;
      font-size: 14px;
      color: #595959;
      border-top: 4px solid transparent;
      border-left: 1px solid #e5e5e5;
      border-bottom: 1px solid #e5e5e5;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      &:last-child {
        border-right: 1px solid #e5e5e5;
      }

      .icon {
        height: 20px;
        width: 20px;
        color: #999;
        border: 1px solid #999;
        border-radius: 100%;
        text-align: center;
      }

      i {
        font-size: 20px;
      }

      .title {
        max-width: 112px;
        line-height: 22px;
        margin-top: 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .selected {
      color: #1366EC;
      background-color: #fff;
      border-bottom: none;
      border-left: none;
      border-top-color: #1366EC;

      .icon {
        background-color: #1366EC;
        border: 1px solid #1366EC;
        color: #fff;
      }

    }
  }

  .pre,
  .next {
    width: 20px;
    height: 100%;
    background-color: #FFFFFF;
    border: 1px solid #E5E5E5;
    color: #181818;
    cursor: pointer;
    display: flex;
    align-items: center;
    position: absolute;

    &:hover {
      color: #FF6A00;
      box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.10);
    }
  }

  .pre {
    left: 0;
    border-left: none;

    i {
      transform: rotateY(180deg);
    }
  }

  .next {
    right: 0;
    border-right: none;
  }
}

@media screen and (max-width:500px) {
  .container {
    height: 62px;

    .mainContainer {
      height: 62px;

      .stepCell {
        min-width: 134px;

        i {
          font-size: 14px;
        }

        p {
          font-size: 13px;
        }

        .title {
          margin-top: 4px;
        }

        .icon {
          height: 13px;
          width: 13px;
          font-size: 12px;
        }
      }
    }

    .pre,
    .next {
      display: none;
    }
  }
}