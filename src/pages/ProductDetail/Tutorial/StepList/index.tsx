import React, { useMemo, useState, useRef, useEffect, useCallback } from 'react';
import { debounce } from 'lodash';
import { IStep } from '@/utils/helpDoc/tutorialProcessor/tutorial';
import { stepIcon } from '@/constants/tutorial';
import styles from './index.module.scss';
import classNames from 'classnames';

interface IProps {
  stepData: IStep[]; // 全部步骤
  finishedStepData: IStep[]; // 已完成的步骤
  currentStepIndex: number; // 当前步骤序号
  onChangeStep: Function; // 步骤更新方法
}

const StepList = ({ stepData, finishedStepData, currentStepIndex, onChangeStep }: IProps) => {
  const ref = useRef<any>();
  const [showPreButton, setShowPreButton] = useState(false);
  const [showNextButton, setShowNextButton] = useState(false);

  const stepIndexArr = useMemo(() => {
    return stepData?.filter((stepItem) => stepItem?.label === 'step');
  }, [stepData]);

  /**
   * stepList滚动逻辑
   * @param isForward 向前/向后移动
   * @param unit 每次移动的单元格数量
   */
  const onStepForwardClick = (isForward: boolean, unit: number) => {
    const containerRef = ref?.current;
    const scrollLeft = unit * 154 || 154;
    // 一次滚动以三个步骤为单位，移动462px
    if (isForward) {
      containerRef?.scrollBy({ left: -scrollLeft, top: 0, behavior: 'smooth' });
    } else {
      setShowPreButton(true);
      containerRef?.scrollBy({ left: scrollLeft, top: 0, behavior: 'smooth' });
    }
  };

  /**
   * stepList横向滚动事件
   */
  const onLeftScroll = debounce(() => {
    const containerRef = ref?.current;
    const { clientWidth, scrollWidth, scrollLeft } = containerRef;
    setShowPreButton(scrollLeft !== 0);
    setShowNextButton(Number(clientWidth) + Number(scrollLeft) !== scrollWidth);
  }, 100);

  /**
   * 监听window窗口变化
   */
  const onResize = useCallback(() => {
    const containerWidth = ref?.current?.clientWidth;
    const minWidth = stepData?.length * 154; // 容器允许最小宽度
    if (containerWidth < minWidth) {
      setShowNextButton(true);
    } else {
      setShowNextButton(false);
      setShowPreButton(false);
    }
  }, [stepData?.length]);

  const debounceResize = debounce(onResize, 100);

  /**
   * 保持当前步骤在可视窗口
   */
  useEffect(() => {
    if (currentStepIndex === undefined) return;
    try {
      const containerRef = ref?.current;
      const stepNodeList = containerRef?.querySelectorAll('div.step-cell');
      const { left: currentLeft, right: currentRight } = stepNodeList?.[currentStepIndex]?.getBoundingClientRect();
      const { clientWidth } = containerRef;
      if (currentLeft < 0) {
        containerRef?.scrollBy({ left: currentLeft, top: 0, behavior: 'smooth' });
      } else if (currentRight > clientWidth) {
        containerRef?.scrollBy({ left: currentRight - clientWidth, top: 0, behavior: 'smooth' });
      }
    } catch (error) {
      //
    }
  }, [currentStepIndex]);

  useEffect(() => {
    if (!ref?.current || !stepData?.length) return;
    window.addEventListener('resize', debounceResize);
    return () => {
      window.removeEventListener('resize', debounceResize);
    };
  }, [debounceResize, stepData?.length]);

  /**
   * 初始化时判断是否需要滚动按钮
   */
  useEffect(() => {
    onResize();
  }, [onResize]);

  return (
    <div className={styles.container}>
      {
        showPreButton &&
        <div className={styles.pre} onClick={() => { onStepForwardClick(true, 3); }}>
          <i className="help-iconfont help-icon-right-arrow" />
        </div>
      }
      <div className={styles.mainContainer} ref={ref} onScroll={onLeftScroll}>
        {
          stepData?.length > 0 && stepData?.map((stepItem: IStep, index) => (
            <div
              className={classNames('step-cell', styles.stepCell,
                stepItem?.id === stepData?.[currentStepIndex]?.id ? styles.selected : '')}
              key={stepItem?.id}
              onClick={() => { onChangeStep(index); }}
            >
              {
                finishedStepData?.includes(stepItem) ?
                  <i className="help-iconfont help-icon-success" style={{ color: '#1366EC' }} /> :
                  <>
                    {
                      stepItem?.label === 'step' ?
                        <span className={styles.icon}>{Number(stepIndexArr.findIndex((item) => item?.id === stepItem?.id)) + 1}</span>
                        :
                        <i className={classNames('help-iconfont', stepIcon?.[stepItem?.label]?.icon)} />
                    }
                  </>
              }
              <p className={styles.title} title={stepItem?.title}>{stepItem?.title}</p>
            </div>
          ))
        }
      </div>
      {
        showNextButton &&
        <div className={styles.next} onClick={() => { onStepForwardClick(false, 3); }}>
          <i className="help-iconfont help-icon-right-arrow" />
        </div>
      }
    </div>
  );
};

export default StepList;
