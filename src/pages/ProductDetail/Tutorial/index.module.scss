.container {
  width: 100%;
  background-color: #f2f2f2;

  .head {
    border-bottom: 1px solid #e5e5e5;
    background-color: #fff;
    padding: 0 24px;
  }

  .mainContainer {
    padding: 24px;
  }

  .bottom {
    width: 100%;
    height: 100px;
  }
}

@media screen and (max-width:1055px) {
  .container {
    background-color: #fafafa;

    .head {
      padding: 0;
    }

    .mainContainer {
      padding: 0;
      padding-top: 10px;
    }
  }
}