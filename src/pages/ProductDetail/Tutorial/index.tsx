import React, { useCallback, useEffect, useState, useMemo } from 'react';
import { useLocation } from 'ice';
import TopHead from './TopHead';
import StepList from './StepList';
import Detail from './Detail';
import Footer from './Footer';
import classNames from 'classnames';
import { stepLabel, tutorialClassList, tutorialTypeMap } from '@/constants/tutorial';
import { DocDetailTypeEnum } from '@/help-fe-common/constants/detail';
import { ITutorial } from '@/utils/helpDoc/tutorialProcessor/tutorial';
import { handleGlobalStyle, removeNavLoaderNode } from '@/help-fe-common/utils/global/style/handleStyle';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import { parseQuery } from '@/help-fe-common/utils/global/url/parseQuery';
import styles from './index.module.scss';

export interface IProps {
  docData: any;
  tutorialType: DocDetailTypeEnum;
  tutorialData: ITutorial;
}

const Tutorial = ({ docData, tutorialType, tutorialData }: IProps) => {
  const location = useLocation();
  const globalClassList = ['.help-body-head', '.aliyun-docs-menu', '.main-layout', '.ace-homepage-2020-hmod-footer'];

  const queryMode = useMemo(() => {
    // 业务逻辑：教程优先展示一键配置版onestop，解决方案优先展示一键部署版onestop
    const defaultMode = tutorialTypeMap?.[tutorialType]?.[0].label;
    const urlParam = parseQuery(location?.search);
    return urlParam?.tab as string || defaultMode || 'onestop';
  }, [tutorialType]);

  const [currentMode, setCurrentMode] = useState(queryMode);

  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [finishedStepData, setFinishedStepData] = useState<any>([]);

  /**
   * 初始化教程内容样式
   */
  const initTutorialContainerStyle = useCallback(() => {
    handleGlobalStyle(globalClassList, false);
    const rootNode = document.querySelector('.markdown-body') as HTMLElement;
    // :scope用于限制第一层子元素
    const targetClass = stepLabel?.map((stepItem) => `:scope>.section.${stepItem?.label}`)?.join(',');
    tutorialClassList?.forEach((item) => {
      const targetNode = rootNode?.querySelector(item?.className);
      if (item?.label === currentMode) {
        targetNode?.setAttribute('style', 'display:block');
      } else {
        targetNode?.setAttribute('style', 'display:none');
      }
      const stepNodeList = targetNode?.querySelectorAll(targetClass);
      stepNodeList?.forEach((stepNode) => {
        const id = stepNode?.getAttribute('id');
        // 当前stepId 与教程中步骤id相同，设置display: block
        if (id === tutorialData?.[currentMode]?.steps[currentStepIndex]?.id) {
          const stepClass = stepNode?.getAttribute('outputclass') || '';
          const tableNode = stepNode?.querySelector('table');
          // 设置display:flex（左右布局）条件：当前步骤为教程简介intro，步骤子元素长度小于3（包含一个标题h标签），包含table标签
          if (stepClass === 'intro' && stepNode?.children?.length <= 3 && tableNode) {
            stepNode?.classList?.remove('help-tutorial-step-hide');
            stepNode?.classList?.remove('help-tutorial-step-show');
            stepNode?.classList?.add('help-tutorial-step-flex');
          } else {
            stepNode?.classList?.remove('help-tutorial-step-hide');
            stepNode?.classList?.remove('help-tutorial-step-flex');
            stepNode?.classList?.add('help-tutorial-step-show');
          }
        } else {
          stepNode?.classList?.remove('help-tutorial-step-flex');
          stepNode?.classList?.remove('help-tutorial-step-show');
          stepNode?.classList?.add('help-tutorial-step-hide');
        }
      });
    });
  }, [currentMode, currentStepIndex, tutorialData]);

  const changeMode = (mode) => {
    setCurrentMode(mode);
    setCurrentStepIndex(0);
    setFinishedStepData([]);
  };

  const changeStep = (val) => {
    setCurrentStepIndex(val);
    document.body.scrollIntoView();
  };

  useEffect(() => {
    const currentStep = tutorialData?.[currentMode]?.steps?.[currentStepIndex];
    if (currentMode && currentStep && !finishedStepData?.includes(currentStep)) {
      setFinishedStepData([...finishedStepData, currentStep]);
    }
  }, [currentStepIndex, currentMode, tutorialData, finishedStepData]);

  useEffect(() => {
    if (!tutorialData?.[currentMode]) {
      setCurrentMode('manual');
    }
  }, [currentMode, tutorialData]);

  useEffect(() => {
    if (docData?.content) {
      initTutorialContainerStyle();
    }
  }, [initTutorialContainerStyle, docData?.content]);

  // 教程文档埋点，统计每个步骤pv、步骤序号、总步骤数，教程模式（一键配置、手动配置），教程完成pv
  useEffect(() => {
    if (currentMode && currentStepIndex !== undefined && docData?.nodeId) {
      const params = { docId: docData?.nodeId, totalStepNum: tutorialData?.[currentMode]?.steps?.length || 0 };
      sendSlsLog({
        page: 'tutorialDetail',
        section: 'step',
        action: 'sectionView',
        userParams1: currentMode,
        userParams2: currentStepIndex + 1,
        ...params,
      });
    }
  }, [currentStepIndex, currentMode, docData?.nodeId, tutorialData]);

  // 统计教程完成pv
  useEffect(() => {
    if (finishedStepData?.length === 0) return;
    const totalLen = tutorialData?.[currentMode]?.steps?.length;
    if (finishedStepData?.length === totalLen) {
      sendSlsLog({
        page: 'tutorialDetail',
        section: '',
        action: 'finished',
        docId: docData?.nodeId,
        userParams1: currentMode,
        userParams2: currentStepIndex + 1,
      });
    }
  }, [finishedStepData?.length]);

  // 统计单个教程文档pv
  useEffect(() => {
    if (docData?.nodeId) {
      sendSlsLog({ page: 'tutorialDetail', section: '', action: 'pv', docId: docData?.nodeId });
    }
  }, [docData?.nodeId]);

  useEffect(() => {
    const navFooterClass = '.ace-homepage-2020-hmod-footer';
    removeNavLoaderNode(navFooterClass, 0);
    return () => {
      handleGlobalStyle(globalClassList, true);
    };
  }, []);

  return (
    <div className={classNames('help-body-full-column', 'help-tutorial-container', styles.container)}>
      <div className={styles.head}>
        <TopHead
          docData={docData}
          tutorialType={tutorialType}
          tutorialData={tutorialData}
          currentLabel={currentMode}
          onChangeMode={changeMode}
        />
      </div>
      <div className={styles.mainContainer}>
        <div className="center-container">
          {
            tutorialData?.[currentMode]?.steps?.length > 0 &&
            <StepList
              stepData={tutorialData?.[currentMode]?.steps}
              finishedStepData={finishedStepData}
              currentStepIndex={currentStepIndex}
              onChangeStep={changeStep}
            />
          }
          <Detail content={docData?.content} stepData={tutorialData?.[currentMode]?.steps?.[currentStepIndex]} />
        </div>
      </div>
      {
        tutorialData?.[currentMode]?.steps?.length > 0 &&
        <div className={styles.bottom}>
          <Footer
            stepData={tutorialData?.[currentMode]?.steps}
            finishedStepData={finishedStepData}
            currentStepIndex={currentStepIndex}
            tutorialType={tutorialType}
            solutionUrl={tutorialData?.[currentMode]?.url}
            onChangeStep={changeStep}
          />
        </div>
      }
    </div>
  );
};

export default Tutorial;
