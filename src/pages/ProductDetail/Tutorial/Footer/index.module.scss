.container {
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .progressContainer {
    display: flex;
    align-items: end;
    margin-top: 10px;

    .progressBar {
      position: relative;

      .successLogo {
        width: 36px;
        height: 36px;
        position: absolute;
        top: -40px;
        background-position: center;
        background-size: contain;
        background-repeat: no-repeat;
      }

      .stageContainer {
        height: 8px;
        width: 200px;
        background-color: #efefef;
        border-radius: 8px;
        position: relative;

        .stage {
          height: 8px;
          border-radius: 4px;
          position: absolute;
        }
      }
    }

    .progressTip {
      font-weight: 400;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.45);
      max-height: 100px;
      margin-left: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      word-wrap: break-word;

      :global {
        .help-tutorial-footer-progress {
          font-size: 21px;
          color: #2164FB;
          line-height: 22px;

          .progress-mark {
            font-size: 14px;
          }
        }
      }
    }
  }

  .buttonContainer {
    display: flex;
    align-items: center;
    margin: 0 40px 0 10px;

    button {
      user-select: none;
      background: none;
      border: none;
      cursor: pointer;
      height: 40px;
      width: 140px;
      font-size: 14px;
      background-color: #fff;
    }

    .preButton {
      color: #3D495C;
      border: 1px solid #9CA4AF;
    }

    .nextButton {
      color: #fff;
      background-color: #ff6a00;
      margin-left: 10px;

      &:hover {
        background-color: #ff791a;
      }
    }
  }
}

:global {
  .help-tutorial-footer {
    height: 100px;
    width: 100%;
    box-sizing: border-box;
    padding: 0 24px;
    background: #fff;
    box-shadow: 0 -15px 35px 0 rgba(0, 0, 0, 0.05);
    position: fixed;
    bottom: 0;
    z-index: 200;
  }
}

@media screen and (max-width:500px) {
  .container {
    height: 36px;

    .progressContainer {
      display: none;
    }

    .buttonContainer {
      width: 100%;
      margin-left: 0;

      button {
        width: 100%;
        height: 36px;
        font-weight: 500;
        font-size: 14px;
        color: #FFFFFF;
      }

      .preButton {
        margin-right: 0;
        margin-bottom: 0;
        border-color: #ff6a00;
        color: #ff6a00;
      }

      .nextButton,
      .tryButton {
        width: 100%;
        margin-left: 0;
      }
    }
  }

  :global {
    .help-tutorial-footer {
      height: 36px;
      padding: 0;
    }
  }
}