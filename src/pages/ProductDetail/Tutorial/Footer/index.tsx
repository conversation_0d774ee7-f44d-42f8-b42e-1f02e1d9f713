/* eslint-disable react/no-danger */
import React, { useState, useEffect, useCallback } from 'react';
import classNames from 'classnames';
import { FormattedMessage, useIntl } from 'react-intl';
import { tutorialFooterText } from '@/constants/tutorial';
import { DocDetailTypeEnum } from '@/help-fe-common/constants/detail';
import { IStep } from '@/utils/helpDoc/tutorialProcessor/tutorial';
import { tryClick } from '@/utils/helpDoc/tutorialProcessor/tryClick';
import styles from './index.module.scss';

interface IProps {
  stepData: IStep[]; // 全部步骤
  finishedStepData: IStep[]; // 已完成的步骤
  currentStepIndex: number; // 当前步骤序号
  tutorialType: DocDetailTypeEnum; // 教程类型：教程、解决方案
  solutionUrl?: string; // 解决方案详情页链接
  onChangeStep: Function; // 步骤更新方法
}

const Footer = ({ stepData, finishedStepData, currentStepIndex, tutorialType, solutionUrl, onChangeStep }: IProps) => {
  const intl = useIntl();
  const [progressData, setProgressData] = useState<any>({});
  const [progressList, setProgressList] = useState<any>([]);

  /**
   * 计算步骤条属性
   * @param {number} currentLen 当前步骤长度
   * @param {number} totalLen 全部步骤长度
   * @return
   */
  const computeProgress = useCallback((currentLen: number, totalLen: number) => {
    const percent = Math.ceil((currentLen / totalLen) * 100);
    const percentTip = `<span class='help-tutorial-footer-progress'>${percent}<span class='progress-mark'>%</span></span>`;
    // 当前进度应当展示的颜色长度
    const getPercent = (value) => {
      return Math.ceil((value / totalLen) * 100);
    };
    const totalProgressList = [
      {
        tip: {
          [DocDetailTypeEnum.TUTORIAL]:
            intl.formatMessage({ id: 'help.tutorial.stage1' }, { percent: percentTip }),
          [DocDetailTypeEnum.SOLUTION]:
            intl.formatMessage({ id: 'help.tutorial.percent' }, { percent: percentTip }),
        },
        width: `${getPercent(1)}%`,
        color: '#1F2226',
        zIndex: 100,
        logo: 'https://img.alicdn.com/imgextra/i2/O1CN01t2Gsk81jEpqtAsuva_!!6000000004517-2-tps-76-72.png',
        validate: (currentStepLen) => { return currentStepLen === 1; },
      },
      {
        tip: {
          [DocDetailTypeEnum.TUTORIAL]:
            intl.formatMessage({ id: 'help.tutorial.stage2' }, { percent: percentTip }),
          [DocDetailTypeEnum.SOLUTION]:
            intl.formatMessage({ id: 'help.tutorial.percent' }, { percent: percentTip }),
        },
        width: `${getPercent(Math.min(currentLen, totalLen - 2))}%`,
        color: '#356DF3',
        zIndex: 99,
        logo: 'https://img.alicdn.com/imgextra/i2/O1CN01t2Gsk81jEpqtAsuva_!!6000000004517-2-tps-76-72.png',
        validate: (currentStepLen, totalStepLen) => { return currentStepLen >= 2 && currentStepLen <= totalStepLen - 2; },
      },
      {
        tip: {
          [DocDetailTypeEnum.TUTORIAL]:
            intl.formatMessage({ id: 'help.tutorial.stage3' }, { percent: percentTip }),
          [DocDetailTypeEnum.SOLUTION]:
            intl.formatMessage({ id: 'help.tutorial.percent' }, { percent: percentTip }),
        },
        width: `${getPercent(Math.min(currentLen, totalLen - 1))}%`,
        color: '#A4ADC1',
        zIndex: 98,
        logo: 'https://img.alicdn.com/imgextra/i2/O1CN01t2Gsk81jEpqtAsuva_!!6000000004517-2-tps-76-72.png',
        validate: (currentStepLen, totalStepLen) => { return currentStepLen === totalStepLen - 1; },
      },
      {
        tip: {
          [DocDetailTypeEnum.TUTORIAL]:
            intl.formatMessage({ id: 'help.tutorial.stage4' }, { percent: percentTip }),
          [DocDetailTypeEnum.SOLUTION]:
            intl.formatMessage({ id: 'help.tutorial.percent' }, { percent: percentTip }),
        },
        width: `${getPercent(totalLen)}%`,
        color: '#FF6A00',
        zIndex: 97,
        validate: (currentStepLen, totalStepLen) => { return currentStepLen === totalStepLen; },
        logo: 'https://img.alicdn.com/imgextra/i2/O1CN01ZNbxvi1Tt6pSFNwU3_!!6000000002439-2-tps-58-72.png',
        link: 'https://www.aliyun.com/activity/product-free/freetier',
      },
    ];
    const progressIndex = totalProgressList?.findIndex((progressItem) => {
      return progressItem?.validate(currentLen, totalLen);
    });
    return {
      currentProgressList: totalProgressList?.slice(0, progressIndex + 1),
      currentProgressData: totalProgressList[progressIndex],
    };
  }, [intl]);

  useEffect(() => {
    if (finishedStepData && stepData) {
      const currentLen = finishedStepData.length;
      const totalLen = stepData.length;
      const currentProgress = computeProgress(currentLen, totalLen);
      const { currentProgressList, currentProgressData } = currentProgress;
      currentProgressData && setProgressData(currentProgressData);
      currentProgressList?.length && setProgressList(currentProgressList);
    }
  }, [computeProgress, finishedStepData, stepData]);

  return (
    <div className={classNames('help-tutorial-footer')}>
      <div className={classNames('center-container', styles.container)}>
        <div className={styles.progressContainer}>
          <div className={styles.progressBar}>
            {
              progressData?.logo &&
              <div
                className={styles.successLogo}
                style={{ backgroundImage: `url(${progressData?.logo})`, left: `calc(${progressData?.width} - 20px)` }}
              />
            }
            <div className={styles.stageContainer}>
              {
                progressList?.map((item) => (
                  <div
                    className={styles.stage}
                    key={item?.tip}
                    style={item ? { backgroundColor: item?.color, width: item?.width, zIndex: item?.zIndex } : {}}
                  />
                ))
              }
            </div>
          </div>
          <p
            className={styles.progressTip}
            dangerouslySetInnerHTML={
              { __html: `${intl.formatMessage({ id: tutorialFooterText[tutorialType]?.text }) + progressData?.tip?.[tutorialType]}` || '' }
            }
          />
        </div>
        <div className={styles.buttonContainer}>
          {
            currentStepIndex !== 0 &&
            <button className={styles.preButton} onClick={() => { onChangeStep(Number(currentStepIndex) - 1); }}>
              <FormattedMessage id="help.tutorial.pre" />
            </button>
          }
          {
            currentStepIndex !== (stepData?.length - 1) ?
              <button className={styles.nextButton} onClick={() => { onChangeStep(Number(currentStepIndex) + 1); }}>
                <FormattedMessage id="help.tutorial.next" />
              </button> :
              <>
                {
                  tutorialFooterText[tutorialType]?.buttonText &&
                  <button
                    className={styles.nextButton}
                    onClick={(e) => {
                      tryClick(solutionUrl, tutorialType);
                    }}
                  >
                    <FormattedMessage id={tutorialFooterText[tutorialType]?.buttonText} />
                  </button>
                }
              </>
          }
        </div>
      </div>
    </div >
  );
};

export default Footer;
