import React, { useState, useEffect } from 'react';
import classnames from 'classnames';
import { FormattedMessage } from 'react-intl';
import { useRequest, useHistory } from 'ice';
import services from '@/services/favorite';
import { loginDetector } from '@/help-fe-common/utils/global/user';
import { aLinkTrigger } from '@/help-fe-common/utils/global/url/aLinkTrigger';
import styles from './index.module.scss';

const Contact = ({ docInfo, userVO, productNodeVO }) => {
  const history = useHistory();
  const [collect, setCollect] = useState(!!userVO?.myFavoriteId);
  const [showPrompt, setShowPrompt] = useState(false);

  const { data: CollectData, request: saveCollect } = useRequest(services.saveMyFavorites);
  const { request: cancelCollect } = useRequest(services.cancelMyFavorites);

  const favoritesBtnClick = () => {
    if (!collect) {
      saveCollect({ nodeId: docInfo?.nodeId })
        .then((data) => {
          if (data?.success) {
            setCollect(true);
            setShowPrompt(true);
          }
        });
    } else {
      cancelCollect({ id: docInfo?.nodeId, myFavoriteId: CollectData?.data?.favoriteId || userVO?.myFavoriteId })
        .then((data) => {
          if (data?.success) {
            setCollect(false);
            setShowPrompt(true);
          }
        });
    }
  };

  useEffect(() => {
    setCollect(!!userVO?.myFavoriteId);
    if (CollectData?.data?.favoriteId) {
      CollectData.data.favoriteId = '';
    }
  }, [userVO]);

  // 1.5秒后提示自动消失
  useEffect(() => {
    showPrompt && setTimeout(() => {
      setShowPrompt(false);
    }, 1500);
  }, [showPrompt]);

  return (
    <>
      <div className={styles.contactButton}>
        <FormattedMessage id={collect ? 'help.doc.cancelCollect' : 'help.doc.saveCollect'}>
          {(txt) => (
            <span
              className={styles.favoritesBtn}
              title={`${txt}`}
              onClick={() => { loginDetector(favoritesBtnClick); }}
              data-spm-click="gostr=/aliyun;locaid=collect"
            >
              <i className={classnames('help-iconfont', 'help-icon-like', collect ? styles.action : '')} />
            </span>
          )}
        </FormattedMessage>
        <a href="/my_favorites.html" onClick={(e) => aLinkTrigger(e, history)}>
          <FormattedMessage id="help.collection.myCollection" />
        </a>
        {
          showPrompt &&
          <div className={styles.prompt}>
            <FormattedMessage id={collect ? 'help.collection.collectPrompt' : 'help.collection.cancelCollected'} />
          </div>
        }
      </div>
    </>
  );
};

export default Contact;
