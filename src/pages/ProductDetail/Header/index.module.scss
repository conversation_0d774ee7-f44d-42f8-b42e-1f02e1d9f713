@import '../../../help-fe-common/styles/variables.scss';

.topBar {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
}

.banner {
  margin-top: 20px;
}

.splitBorder {
  position: relative;

  &:after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    margin-top: 3px;
    display: inline-block;
    width: 1px;
    height: 8px;
    background: #999;
  }
}

.title {
  h1 {
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 700;
    font-size: 34px;
    color: #181818;
    letter-spacing: 0.4px;
    line-height: 44px;
    min-height: 44px;
    word-break: break-word;
    display: flex;
    align-items: center;
  }
}

.actionBar {
  height: 24px;
  margin-top: 6px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;

  .left {
    min-width: 150px;
    flex: 1;
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .updateTime {
      color: $text-gray-color;
    }

    .showAuthor {
      margin-right: 10px;
      height: auto;
      line-height: 14px;
    }

    a {
      color: $text-link-color;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .right {
    display: flex;
    align-items: center;
    position: relative;
    flex-wrap: wrap;
    gap: 12px;

    .linkButton {
      height: 20px;
      display: flex;
      align-items: center;
    }

    a,
    span {
      color: $text-gray-color;

      &:hover {
        color: $theme-color;
      }
    }
  }
}

// mobile端
@media only screen and (max-width: 1055px) {
  .actionBar {
    flex-direction: column;
    align-items: flex-start;
    height: auto;
    gap: 10px;

    .right {
      justify-content: space-between;
      width: 100%;

      .linkButton {
        margin-left: -10px;
      }

      a {
        color: $theme-color;
      }
    }
  }
}