import React from 'react';
import { FormattedMessage } from 'react-intl';
import styles from './index.module.scss';

interface IProps {
  urlObj: Record<string, any>;
}

const OuterUrl = ({ urlObj }: IProps) => {
  const { productUrl } = urlObj;

  return (
    <>
      {
        productUrl ?
          <div className={styles.outUrl}>
            <a href={productUrl} target="_blank" rel="noreferrer">
              <FormattedMessage id="help.doc.productPage" />
            </a>
          </div> :
          null
      }
    </>
  );
};

export default OuterUrl;
