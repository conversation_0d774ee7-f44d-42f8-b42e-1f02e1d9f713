
import React from 'react';
import { FormattedMessage } from 'react-intl';
import { generateZbSubmitUrl, onSubmitBug } from '@/utils/helpDoc/zbHelper';
import { loginDetector } from '@/help-fe-common/utils/global/user';
import { TaskAvailabilityStatusEnum } from '@/help-fe-common/constants';
import { NODE_TYPE } from '@/help-fe-common/constants/detail';
import styles from './index.module.scss';

interface IProps{
  docInfo: any;
}


const SubmitButton = ({ docInfo }: IProps) => {
  const zbSubmitBugUrl = generateZbSubmitUrl(docInfo?.nodeId);

  return (
    <>
      {
        (docInfo?.nodeType === NODE_TYPE.DOC || docInfo?.nodeType === NODE_TYPE.KB)
        && TaskAvailabilityStatusEnum[1] === docInfo?.taskStatus ?
          <div className={styles.submitBugBox}>
            <a
              className={styles.submitBug}
              href={zbSubmitBugUrl}
              target="_blank"
              rel="noreferrer noopener"
              onClick={(e) => {
                e.preventDefault();
                loginDetector(() => { onSubmitBug(zbSubmitBugUrl); });
              }}
            >
              <FormattedMessage id="help.doc.submitBug" />
            </a>
          </div> : null
      }
    </>
  );
};
export default SubmitButton;
