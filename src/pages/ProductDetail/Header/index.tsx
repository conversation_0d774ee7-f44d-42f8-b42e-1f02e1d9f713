import React from 'react';
import { FormattedMessage } from 'react-intl';
import BreadCrumb from '@/help-fe-common/components/common/BreadCrumb';
import { toHour } from '@/help-fe-common/utils/global/date';
import styles from './index.module.scss';
import ShowAuthor from '@/help-fe-common/components/common/ShowAuthor';
import DeployButton from '@/components/QuickDeploy/DeployButton';
import SubmitButton from './SubmitButton';
import Contact from './Contact';
import OuterUrl from './OuterUrl';
import { isServer } from '@/help-fe-common/utils/node/getContext';

export default ({ breadcrumb, docInfo, dynamicDocInfo }) => {
  const { productNodeVO, productUrl } = docInfo || {};
  const { authors, lastModifiedTime, userVO } = dynamicDocInfo || {};

  return (
    <>
      {
        docInfo?.nodeId &&
        <header className="aliyun-docs-view-header">
          <div className={styles.topBar}>
            <BreadCrumb data={breadcrumb} />
          </div>
          <div className={styles.title}>
            <h1>
              {docInfo?.docTitle || docInfo?.title}
            </h1>
            <div className={styles.actionBar}>
              <div className={styles.left}>
                {
                  (authors || docInfo?.authors) &&
                  <span className={[styles.showAuthor, styles.splitBorder].join(' ')}>
                    <ShowAuthor authors={authors || docInfo?.authors} />
                  </span>
                }
                <span className={styles.updateTime}>
                  <FormattedMessage id="help.doc.updateTime" />
                  {!isServer() && toHour(lastModifiedTime || docInfo?.lastModifiedTime)}
                </span>
              </div>
              <div className={styles.right}>
                <div className={styles.linkButton}>
                  {
                    docInfo?.deploys?.length > 0 && <DeployButton docInfo={docInfo} />
                  }
                  <SubmitButton docInfo={docInfo} />
                  <OuterUrl urlObj={{ productUrl }} />
                </div>
                <Contact docInfo={docInfo} userVO={userVO || docInfo?.userVO} productNodeVO={productNodeVO} />
              </div>
            </div>
          </div>
        </header>
      }
    </>
  );
};
