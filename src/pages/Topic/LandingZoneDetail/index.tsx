import React from 'react';
import { ENV, getEnv } from '@/help-fe-common/utils/global/env';
import Detail from '../Detail';
import { TOPIC_TYPE } from '@/pages/Topic/constant';

const LandingZoneDetail = (props) => {
  const breadcrumb = {
    title: 'Landing Zone 主页',
    link: getEnv() === ENV.PROD ?
      'https://www.aliyun.com/landing-zone' :
      'https://www.aliyun.com/landing-zone' };

  return <Detail indexBreadCrumb={breadcrumb} pageType={TOPIC_TYPE.LANDING_ZONE} />;
};

LandingZoneDetail.pageConfig = {
  spm: '29094516',
};

export default LandingZoneDetail;
