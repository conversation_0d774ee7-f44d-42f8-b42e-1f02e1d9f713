import React from 'react';
import { ENV, getEnv } from '@/help-fe-common/utils/global/env';
import Detail from '../Detail';
import { TOPIC_TYPE } from '@/pages/Topic/constant';

const TopicDetail = (props) => {
  const breadcrumb = {
    title: '云计算主题',
    link: getEnv() === ENV.PROD ?
      'https://www.aliyun.com/getting-started/what-is' :
      'https://pre-www.aliyun.com/preview/common/what-is/theme-aggregation' };

  return <Detail indexBreadCrumb={breadcrumb} pageType={TOPIC_TYPE.TOPIC} />;
};

TopicDetail.pageConfig = {
  spm: '28987253',
};

export default TopicDetail;
