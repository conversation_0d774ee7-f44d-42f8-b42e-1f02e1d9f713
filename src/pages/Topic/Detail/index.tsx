import React, { FunctionComponent, useEffect, useMemo, useState } from 'react';
import { useHistory } from 'ice';
import Loading from '@/help-fe-common/components/common/Loading';
import Header from './Header';
import Toc from './Toc';
import Content from './Content';
import RecommendProduct from './RecommendProduct';
import RecommendCommunity from './RecommendCommunity';
import RecommendTopic from './RecommendTopic';
import NotFound from '@/help-fe-common/components/common/NotFound';
import TopicService from '@/services/topic';
import service from '@/help-fe-common/services/apiService';
import useChangeTitle from '@/help-fe-common/hooks/useChangeTitle';
import useResponsive from './utils/useResponsive';
import { isServer } from '@/help-fe-common/utils/node/getContext';
import { extractUrlParam } from '@/help-fe-common/utils/global/url/urlExtract';
import contentProcessor from '@/help-fe-common/plugins/docDetailProcessor';
import { TOPIC_TYPE } from '@/pages/Topic/constant';
import { topicPlugins } from '@/utils/helpDoc/docDetailProcessor/config';
import styles from './index.module.scss';

interface IProps {
  indexBreadCrumb: {
    title: string;
    link: string;
  };
  pageType?: TOPIC_TYPE;
}

const Detail: FunctionComponent<IProps> = ({ indexBreadCrumb, pageType = TOPIC_TYPE.TOPIC }) => {
  const history = useHistory();
  const [isLoading, setIsLoading] = useState(false);
  const [topicDetailRes, setTopicDetailRes] = useState<Record<string, any>>();
  const [recommendRes, setRecommendData] = useState<Record<string, any>>();

  const identifier = useMemo(() => extractUrlParam(location?.pathname), [location?.pathname]);

  const isMobile = useResponsive();

  const {
    title,
    content,
    docTitle,
    productNodeVO,
    likeCount,
    nodeId,
  } = useMemo(() => {
    const { data } = topicDetailRes || {};
    return data || {};
  }, [topicDetailRes?.data]);

  const breads = useMemo(() => {
    return [
      indexBreadCrumb,
      { title },
    ];
  }, [title]);

  useEffect(() => {
    const { nodeId: id, alias } = identifier || {};
    setIsLoading(true);
    service.getDocumentDetail({ nodeId: id, alias: String(alias) }).then((res) => {
      setTopicDetailRes(res);
      setIsLoading(false);
    });
    TopicService.getTopicRecommend({ nodeId, alias, channelType: pageType }).then((res: any) => {
      setRecommendData(res || {});
    });
  }, [identifier]);

  /**
   * 调用plugin插件
   */
  useEffect(() => {
    let unbindEventArr;
    if (isServer() || !topicDetailRes) return;
    try {
      // 设置异步任务保证plugin插件在dom挂载后执行
      setTimeout(() => {
        unbindEventArr = contentProcessor({ history, data: topicDetailRes?.data, plugins: topicPlugins });
      }, 50);
    } catch (e) {
      console.error(e);// 错误
    }
    return () => {
      unbindEventArr?.length && unbindEventArr?.forEach((item) => item && item());
    };
  }, [topicDetailRes, isMobile]);

  useChangeTitle({ title });

  if (isLoading) return <Loading loading />;

  if (topicDetailRes?.code === 404) return <NotFound />;

  if (!topicDetailRes) return null;

  return (isMobile ?
    <div className={styles.topicDetailWrapper}>
      <Toc content={content} isMobile={isMobile} />
      <Header breads={breads} />
      <Content
        content={content}
        title={title}
        docTitle={docTitle}
        productTitle={productNodeVO?.title}
        likeCount={likeCount}
        nodeId={nodeId}
      />
      <RecommendProduct
        productRecommendList={recommendRes?.productRecommendList}
        recommendUrl={recommendRes?.recommendUrl}
        pageType={pageType}
      />
      <RecommendCommunity communityList={recommendRes?.contentRecommendList} />
      <RecommendTopic
        listTopic={recommendRes?.channelDetailRecommendList}
        isMobile
        pageType={pageType}
      />
    </div> :
    <div className={styles.topicDetailWrapper}>
      <Header breads={breads} />
      <div className={styles.contentWrapper}>
        <div className={styles.left}>
          <Toc content={content} isMobile={false} />
        </div>
        <div className={styles.context}>
          <div className={styles.innerContext}>
            <Content
              content={content}
              title={title}
              docTitle={docTitle}
              productTitle={productNodeVO?.title}
              likeCount={likeCount}
              nodeId={nodeId}
            />
            <div className={styles.innerRight}>
              <RecommendProduct
                productRecommendList={recommendRes?.productRecommendList}
                recommendUrl={recommendRes?.recommendUrl}
                pageType={pageType}
              />
              <RecommendCommunity communityList={recommendRes?.contentRecommendList} />
            </div>
          </div>
          <RecommendTopic
            listTopic={recommendRes?.channelDetailRecommendList}
            isMobile={false}
            pageType={pageType}
          />
        </div>
      </div>
    </div>);
};

export default Detail;
