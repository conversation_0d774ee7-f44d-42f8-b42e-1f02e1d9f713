import { useEffect, useState } from 'react';
import { debounce } from 'lodash';

const useResponsive = (point = 1056) => {
  const condition = () => window.innerWidth < point;

  const [isMobile, setIsMobile] = useState(condition);

  const onResize = debounce(() => {
    const s = condition();
    setIsMobile(s);
  }, 300);

  useEffect(() => {
    window.addEventListener('resize', onResize);
    return () => {
      window.removeEventListener('resize', onResize);
    };
  }, []);

  useEffect(() => {
    const event = new Event('resize');
    window.dispatchEvent(event);
  }, []);

  return isMobile;
};

export default useResponsive;
