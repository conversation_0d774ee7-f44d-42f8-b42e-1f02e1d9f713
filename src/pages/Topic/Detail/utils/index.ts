/**
 * 深度优先遍历节点树
 * @param element
 * @param nodeList
 */
export const getTocData = (element: HTMLElement, nodeList: Array<Record<string, any>> = []) => {
  if (['H2', 'H3'].includes(element?.nodeName) && element?.innerText) {
    nodeList.push({ id: element?.id, text: element.innerText, type: element?.nodeName });
  }

  const children = element?.children || [];

  for (let i = 0; i < children.length; i++) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    getTocData(children[i], nodeList);
  }

  return nodeList;
};

/**
 * 将html字符串转化为可遍历element
 * @param content
 */
export function parseHTMLToElement(content: string) {
  const parser = new DOMParser();
  const htmlDoc = parser.parseFromString(content || '', 'text/html');
  return htmlDoc?.documentElement;
}

// 将数字格式化
export function toThousands(count: number) {
  const result: string[] = [];
  let counter = 0;
  const num = (count || 0).toString().split('');
  for (let i = num.length - 1; i >= 0; i--) {
    counter++;
    result.unshift(num[i]);
    if (!(counter % 3) && i !== 0) {
      result.unshift(',');
    }
  }
  return result.join('');
}

export function getElementTop(element: HTMLElement) {
  let actualTop = element?.offsetTop || 0;
  let current: any = element?.offsetParent;

  while (current) {
    actualTop += current?.offsetTop || 0;
    current = current?.offsetParent;
  }

  return actualTop;
}
