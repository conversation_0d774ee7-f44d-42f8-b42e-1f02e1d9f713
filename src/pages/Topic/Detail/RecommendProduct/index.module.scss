@import "../index.module.scss";

.recommendWrapper {

  @media #{$mobile} {
    padding: 0 16px;
  }

  .boxWrapper {
    margin-bottom: 24px;

    @media #{$mobile} {
      margin-bottom: 16px;
    }

    .boxItem {
      padding: 24px;
      border: 1px solid #E9E9E9;
      display: block;

      &:not(:last-child) {
        margin-bottom: 24px;

        @media #{$mobile} {
          margin-bottom: 16px;
        }
      }

      &:hover {
        cursor: pointer;
      }

      .iconImage {
        width: 48px;
        height: 48px;
        object-fit: cover;
        margin-bottom: 24px;
      }

      .title {
        font-size: 16px;
        font-weight: 500;
        line-height: 26px;
        letter-spacing: 0;
        color: #181818;
        margin-bottom: 8px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      .desc {
        font-size: 14px;
        font-weight: normal;
        line-height: 24px;
        letter-spacing: 0;
        color: #737373;
        overflow: hidden;
        display: -webkit-box;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
  }

  .freeBtn {
    display: block;
    height: 32px;
    line-height: 32px;
    background: #1366EC;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    letter-spacing: 0.57px;
    color: #FFFFFF;
    margin-bottom: 24px;

    @media #{$mobile} {
      margin-bottom: 16px;
    }

    &:hover {
      cursor: pointer;
    }
  }
}
