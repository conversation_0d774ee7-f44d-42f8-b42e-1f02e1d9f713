import React, { FunctionComponent } from 'react';
import { truncate } from 'lodash';
import { TOPIC_TYPE } from '@/pages/Topic/constant';
import styles from './index.module.scss';

interface IProps {
  productRecommendList?: Array<Record<string, any>>;
  recommendUrl: string;
  pageType: TOPIC_TYPE;
}

const RecommendProduct: FunctionComponent<IProps> = (props) => {
  const { productRecommendList, recommendUrl, pageType } = props;

  return (
    <div className={styles.recommendWrapper}>
      <ul className={styles.boxWrapper}>
        {
        Array.isArray(productRecommendList) && productRecommendList.map((item) => (
          <a key={item.id} className={styles.boxItem} href={item.productUrl} target="_blank" rel="noreferrer">
            <img src={item?.productIcon} alt="未加载" className={styles.iconImage} />
            <h4 className={styles.title}>{item?.productName}</h4>
            <p className={styles.desc}>{truncate(item?.productDescription || '-', { length: 40 })}</p>
          </a>))
      }
      </ul>
      {
      Boolean(recommendUrl) &&
      <a
        className={styles.freeBtn}
        target="_blank"
        rel="noreferrer"
        href={recommendUrl}
      >
        {pageType === TOPIC_TYPE.LANDING_ZONE ? '立即开通' : '免费试用'}
      </a>
    }
    </div>
  );
};

export default RecommendProduct;
