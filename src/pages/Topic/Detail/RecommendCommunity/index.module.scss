@import "../index.module.scss";

.articleWrapper {
  margin-bottom: 40px;

  @media #{$mobile} {
    padding: 0 16px;
  }

  .articleItem {
    display: flex;
    border: 1px solid #E9E9E9;

    &:not(:last-child) {
      margin-bottom: 24px;

      @media #{$mobile} {
        margin-bottom: 16px;
      }
    }

    &:hover {
      cursor: pointer;
    }

    .context {
      padding: 24px;
      flex: 1;

      .tag {
        padding: 2px 6px;
        font-weight: normal;
        line-height: 18px;
        font-size: 12px;
        color: #8C8C8C;
        background: #F5F5F6;
        margin-bottom: 8px;
        display: inline-block;
      }

      .desc {
        font-size: 14px;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: 0;
        color: #181818;
        margin: 0;
      }
    }

    .image {
      width: 90px;
      height: auto;
      object-fit: cover;
    }
  }
}
