import React, { FunctionComponent } from 'react';
import styles from './index.module.scss';

interface IProps {
  communityList?: Array<Record<string, string>>;
}

const RecommendCommunity: FunctionComponent<IProps> = (props) => {
  const { communityList = [] } = props;

  return (
    <ul className={styles.articleWrapper}>
      {
      Array.isArray(communityList) && communityList.map((item) => (
        <a className={styles.articleItem} href={item?.contentUrl} target="_blank" rel="noreferrer">
          <div className={styles.context}>
            <span className={styles.tag}>{item?.categoryName}</span>
            <h4 className={styles.desc}>{item?.title}</h4>
          </div>
          <img src={item.contentIcon} alt="unknow" className={styles.image} />
        </a>))
    }
    </ul>
  );
};

export default RecommendCommunity;
