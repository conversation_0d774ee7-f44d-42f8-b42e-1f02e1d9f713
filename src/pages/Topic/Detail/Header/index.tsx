import React, { FunctionComponent } from 'react';
import styles from './index.module.scss';

interface IProps {
  breads?: Array<{ title: string; link?: string; target?: string }>;
}

const Header: FunctionComponent<IProps> = (props) => {
  const { breads = [] } = props;
  return (<div className={styles.header}>
    {
      Array.isArray(breads) && breads.map((item) => {
        if (item.link) {
          return (<span className={styles.linkBreak}>
            <a
              target={item.target || '_blank'}
              href={item.link}
              className={styles.link}
              rel="noreferrer"
            >
              {item.title}
            </a>
            <i className={`help-iconfont help-icon-right-arrow ${styles.splitIcon}`} />
          </span>);
        }
        return <span className={styles.normalBreak}>{item.title}</span>;
      })
    }
  </div>);
};

export default Header;
