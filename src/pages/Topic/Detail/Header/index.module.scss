@import "../index.module.scss";

$theme-hover: #1366ec;

.header {
  padding: 24px 0;
  color: #8C8C8C;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  margin-bottom: 20px;

  @media #{$mobile} {
    padding: 20px 16px 24px;
  }

  .linkBreak {
    color: #181818;
    display: inline-flex;
    align-items: center;
    flex-wrap: nowrap;

    @media #{$mobile} {
      font-size: 12px;
    }

    .link {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      color: #181818;

      &:hover {
        color: $theme-hover;
      }
    }
  }

  .normalBreak {
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: 0;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;

    @media #{$mobile} {
      font-size: 12px;
    }
  }

  .splitIcon {
    margin: 0 8px;
    color: #181818;
    font-size: 12px;
  }
}
