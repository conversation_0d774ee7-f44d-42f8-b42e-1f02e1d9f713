import React, { FunctionComponent } from 'react';
import { Grid } from '@alifd/next';
import { isEmpty } from 'lodash';
import { TOPIC_TYPE } from '@/pages/Topic/constant';
import styles from './index.module.scss';

const {
  Row,
  Col,
} = Grid;

interface IProps {
  listTopic?: Array<Record<string, any>>;
  isMobile: boolean;
  pageType: TOPIC_TYPE;
}

const RecommendTopic: FunctionComponent<IProps> = (props) => {
  const { listTopic = [], isMobile, pageType } = props;

  if (isEmpty(listTopic)) return null;

  return (
    <div className={styles.listTopic}>
      <h2 className={styles.title}>{pageType === TOPIC_TYPE.TOPIC ? '主题推荐' : '选型推荐'}</h2>
      <Row wrap gutter={24}>
        {
        Array.isArray(listTopic) && listTopic.map((item) => (
          <Col span={isMobile ? 24 : 8} key={item?.nodeId}>
            <a href={item?.url} className={styles.topicItem} target="_blank" rel="noreferrer">
              <h4 className={styles.topicTitle}>{item.title}</h4>
              <span className={styles.desc}>{item.description}</span>
            </a>
          </Col>))
      }
      </Row>
    </div>
  );
};

export default RecommendTopic;
