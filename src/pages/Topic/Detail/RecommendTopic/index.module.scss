@import "../index.module.scss";

@mixin oneLine() {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.listTopic {
  margin-bottom: 36px;
  border-top: 1px solid #E9E9E9;
  padding-top: 24px;

  @media #{$mobile} {
    margin: 0 16px 16px;
  }

  .title {
    font-size: 26px;
    font-weight: 500;
    line-height: 40px;
    letter-spacing: 0;
    color: #181818;
    margin-bottom: 24px;
  }

  .topicItem {
    padding: 24px;
    background: #F5F5F6;
    margin-bottom: 24px;
    display: block;

    &:hover {
      cursor: pointer;

      .topicTitle {
        color: #1366EC;
      }
    }

    .topicTitle {
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      color: #181818;
      margin-bottom: 12px;
      width: 100%;
      @include oneLine();
    }

    .desc {
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
      letter-spacing: 0;
      color: #737373;
      display: block;
      @include oneLine();
    }
  }
}
