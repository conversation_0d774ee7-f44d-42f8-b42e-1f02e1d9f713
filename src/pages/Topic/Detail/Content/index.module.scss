@import "../index.module.scss";

.content {
  margin-bottom: 24px;
  padding-right: 48px;
  flex: 1;
  overflow: hidden;

  @media #{$mobile} {
    padding: 0 16px;
  }

  .title {
    font-size: 44px;
    font-weight: 600;
    line-height: 44px;
    letter-spacing: 0;
    color: #181818;
    margin-bottom: 32px;

    @media #{$mobile} {
      font-size: 36px;
      line-height: 36px;
      margin-bottom: 20px;
    }
  }

  .message {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #E9E9E9;
    margin-bottom: 40px;
    color: #969696;

    .readTime {
      display: inline-flex;
      align-items: center;
    }

    .timeIcon {
      margin-right: 10px;
    }

    .likeCount {
      margin-right: 16px;
    }

    .actionIcon {
      display: inline-flex;
      align-items: center;
    }
  }

  .desc {
    font-size: 16px;
    font-weight: normal;
    line-height: 26px;
    letter-spacing: 0;
    color: #181818;
    margin-bottom: 64px;
  }
}
