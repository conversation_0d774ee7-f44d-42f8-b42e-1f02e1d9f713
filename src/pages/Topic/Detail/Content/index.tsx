import React, { FunctionComponent, useMemo } from 'react';
import classnames from 'classnames';
import { parseHTMLToElement } from '../utils';
import FeedbackButton from '@/help-fe-common/components/common/Feedback/FeedbackButton';
import styles from './index.module.scss';

interface IProps {
  content: string;
  title: string;
  docTitle: string;
  productTitle?: string;
  likeCount: number;
  nodeId: number;
}

const Content: FunctionComponent<IProps> = (props) => {
  const {
    content,
    title,
    docTitle,
    productTitle,
    likeCount,
    nodeId,
  } = props;

  const readTime = useMemo(() => {
    const element = parseHTMLToElement(content);
    // 文字260字/分钟，图片3秒/张
    const readSpeed = 3 / 13;
    const text = element.innerText.replace(/\s*/g, '');
    const textTime = text.length * readSpeed;
    const imgTime = element.querySelectorAll('img')?.length * 3;
    const minutes = Math.ceil((textTime + imgTime) / 60);
    const h = Math.floor(minutes / 60);
    const m = Math.ceil(minutes % 60);
    if (h > 0) {
      return `${h}小时${m}分钟`;
    }
    return `${m}分钟`;
  }, [content]);

  return (
    <div className={styles.content}>
      <h1 className={styles.title}>{title}</h1>
      <div className={styles.message}>
        <div className={styles.readTime}>
          <i className={classnames('help-iconfont help-icon-time-line', styles.timeIcon)} />
          <span>阅读本文大约需要{readTime}</span>
        </div>
      </div>
      <div className="help-topic-container unionContainer">
        <div
          className="markdown-body"
          style={{ margin: 0 }}
          dangerouslySetInnerHTML={{ __html: content || '' }}
        />
        {/* 反馈按钮区域 */}
        <div id="help-doc-feedback" >
          <FeedbackButton docInfo={{ nodeId }} />
        </div>
      </div>
    </div>
  );
};

export default Content;
