import React, { FunctionComponent, useEffect, useMemo, useRef, useState } from 'react';
import { debounce, findLastIndex, isEmpty, throttle } from 'lodash';
import { FormattedMessage } from 'react-intl';
import { Dropdown, Icon, Menu } from '@alifd/next';
import classnames from 'classnames';
import { getElementTop, getTocData, parseHTMLToElement } from '../utils';
import useEventListener from '@/help-fe-common/hooks/useEventListener';
import { deleteNode } from '@/help-fe-common/utils/global/dom/deleteNode';
import styles from './index.module.scss';

const { Item } = Menu;

interface IProps {
  content: string;
  isMobile: boolean;
}

const Toc: FunctionComponent<IProps> = (props) => {
  const {
    content,
    isMobile,
  } = props;

  const [selectItem, setSelectItem] = useState<string | null>();

  const [tocItemOffset, setTocOffset] = useState<number[]>([]);

  const mountRef = useRef(false);

  const onItemClick = (id: string, index: number) => {
    setSelectItem(id);
    location.hash = id;
    const top = tocItemOffset[index];
    window.scrollTo({
      top,
      behavior: 'smooth',
    });
  };

  const onScroll = () => {
    const currentTop = document.documentElement.scrollTop;
    const index = findLastIndex(tocItemOffset, (point: number) => point - currentTop <= 1);
    if (index > -1) {
      const id = tocData[index]?.id;
      setSelectItem(id);
    } else {
      setSelectItem(null);
    }
  };

  const onResize = () => {
    getOffsetPoint();
  };

  useEventListener(window, 'resize', debounce(onResize, 500));

  useEventListener(window, 'scroll', throttle(onScroll, 100));

  const tocData = useMemo(() => {
    const element = parseHTMLToElement(content);
    // 需对tabbed-content-box内容过滤
    deleteNode(element, 'tabbed-content-box section');
    return getTocData(element);
  }, [content]);

  const getOffsetPoint = () => {
    const tocTitleOffsetPoint: number[] = [];
    tocData.forEach((item) => {
      const el: any = document.getElementById(item.id);
      const topSpace: number = getElementTop(el);
      tocTitleOffsetPoint.push(topSpace);
    });
    setTocOffset(tocTitleOffsetPoint);
  };

  useEffect(() => {
    if (!location.hash || isEmpty(tocItemOffset)) return;
    const id = location.hash.split('#')[1];
    const findIndex = tocData.findIndex((x: any) => x.id === id);
    if (findIndex === -1 || mountRef.current) return;
    mountRef.current = true;
    setSelectItem(id);
    const top = tocItemOffset[findIndex];
    window.scrollTo({
      top,
      behavior: 'smooth',
    });
  }, [tocItemOffset, location.hash]);

  useEffect(() => {
    setTimeout(() => getOffsetPoint(), 500);
  }, [tocData]);

  const renderItemNode = (itemData, index) => {
    return (
      <li
        key={itemData?.id}
        onClick={() => onItemClick(itemData?.id, index)}
        className={classnames(styles.tocItem,
          selectItem === itemData?.id ? styles.selectItem : null,
          itemData?.type === 'H3' ? styles.h3Item : null)}
      >
        {itemData?.text}
      </li>
    );
  };

  return (
    <div className={styles.tocWrapper}>
      <h2 className={styles.title}>
        <FormattedMessage id="help.doc.synopsis.title" />
      </h2>
      {
        isMobile ?
          <Dropdown
            trigger={<Icon type="list" size="small" />}
            triggerType="click"
          >
            <Menu>
              {
                Array.isArray(tocData) && tocData?.filter((item) => item?.type !== 'H3').map((item, index) => (
                  <Item
                    key={item.id}
                    onClick={() => onItemClick(item?.id, index)}
                    style={selectItem === item?.id ? { background: '#f4f4f4' } : {}}
                  >
                    {item?.text}
                  </Item>))
              }
            </Menu>
          </Dropdown>
          :
          <ul className={styles.toc}>
            {Array.isArray(tocData) && tocData.map((item, index) =>
              renderItemNode(item, index))}
          </ul>
      }
    </div>
  );
};

export default Toc;
