@import "../index.module.scss";

@mixin selected() {
  position: absolute;
  left: -1px;
  top: 0;
  content: '';
  display: inline-block;
  width: 2px;
  height: 24px;
  background: #1366EC;
}

.tocWrapper {
  position: sticky;
  top: 70px;
  margin-bottom: 60px;
  background: #FFFFFF;

  @media #{$mobile} {
    position: unset;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0;
    padding: 0 16px;
    border-bottom: 1px solid #E9E9E9;
  }

  .title {
    font-size: 18px;
    font-weight: 500;
    line-height: 30px;
    letter-spacing: 0;
    color: #181818;
    margin-bottom: 16px;

    @media #{$mobile} {
      font-size: 14px;
      line-height: unset;
      margin: 0;
      padding: 12px 0;
    }
  }

  .toc {
    overflow: auto;
    max-height: calc(100vh - 140px);
    scrollbar-width: 6px; // firefox滚动条隐藏

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #d8d8d8;
      border-radius: 3px;
    }

    .tocItem {
      font-size: 14px;
      padding: 0 16px;
      font-weight: normal;
      line-height: 24px;
      letter-spacing: 0;
      color: #8C8C8C;
      border-left: 1px solid #E9E9E9;
      position: relative;

      &:not(:last-child) {
        padding-bottom: 16px;
      }

      &:hover {
        cursor: pointer;
        color: #181818;
      }
    }

    .h3Item {
      padding-left: 32px;
    }

    .selectItem {
      font-weight: 500;
      color: #181818;

      &:before {
        @include selected();
      }
    }
  }
}