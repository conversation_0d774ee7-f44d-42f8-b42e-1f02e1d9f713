.helpBodyBoxProductTitle {
  width: 100%;
  padding: 30px 45px 0 45px;
  background: #ffffff;

  .helpBodyBoxProductTitleBox {
    display: flex;
    justify-content: space-between;
    width: 100%;
    border-bottom: 1px solid #ededed;
    padding-bottom: 20px;

    .productTitleContent {
      margin-right: 22px;
      flex: 1;
      width: 0;

      .productTitle {
        &>h1 {
          font-size: 36px;
          color: #373d41;
          display: inline-block;
          height: 50px;
          line-height: 50px;
          margin: 0;
        }

        .productLink {
          vertical-align: super;
          align-self: flex-end;
          margin-left: 10px;
          height: 25px;
          line-height: 25px;
          font-size: 12px;
          color: #1366ec;
          cursor: pointer;

          >.goProduct {
            width: 16px;
            height: 16px;
          }
        }
      }

      .productTitleIntroduce {
        font-size: 14px;
        color: #373d41;
        line-height: 28px;
        margin-top: 3px;
        margin-bottom: 29px;
        display: -webkit-box;
      }

      .productShow {
        display: none;
        height: 17px;
        font-size: 12px;
        color: #1366ec;
        text-align: right;

        >i {
          display: inline-block;
          transform: scale(0.66);
        }
      }

      .productTitleBtn {
        display: flex;
        justify-content: space-between;
        margin-bottom: 21.3px;

        .btnLeft {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 10px;
        }

        .titleBtn {
          display: inline-block;
          box-sizing: border-box;
          max-width: 148px;
          padding: 0 12px;
          height: 28px;
          border: 1px solid rgba(151, 151, 151, 0.5);
          font-size: 12px;
          text-align: center;
          line-height: 26px;
          text-decoration: none;
          cursor: pointer;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .base {
          color: #181818;

          &:hover {
            color: #1366ec;
            border: 1px solid #1366ec;
          }
        }

        .highlight {
          background-color: #1366ec;
          color: #fff;
          border: 1px solid #1366ec;

          &:hover {
            background-color: #fff;
            color: #1366ec;
          }
        }
      }
    }

    .productTitleVideo {
      position: relative;
      cursor: pointer;
      margin-top: 32px;
      flex: 0 0 252px;
    }
  }
}

//移动端
@media screen and (max-width: 1055px) {
  .helpBodyBoxProductTitle {
    padding: 16px 16px 0 16px;

    .helpBodyBoxProductTitleBox {
      flex-direction: column;

      .productTitleContent {
        width: unset;
        margin-right: 0;
        padding-top: 0;

        .productTitleBtn {
          .btnLeft {
            flex-wrap: wrap;
            gap: 10px;

            .titleBtn {
              margin-right: 0;
            }
          }
        }
      }

      .productTitleVideo {
        flex: unset;
        margin-top: 24px;
      }
    }
  }
}