import React, { useState } from 'react';
import classnames from 'classnames';
import Video from '@/help-fe-common/components/pc/Video';
import CoverImage from '@/help-fe-common/components/pc/CoverImage';
import { aLinkTrigger } from '@/help-fe-common/utils/global/url/aLinkTrigger';
import { useHistory } from 'ice';

import styles from './index.module.scss';

export default ({ data }) => {
  const history = useHistory();
  const [isShowVideo, setIsShowVideo] = useState(false);
  const mainBlockButtons = data?.introduction?.mainBlockButtons;

  return (
    <div className={styles.helpBodyBoxProductTitle}>
      <div className={styles.helpBodyBoxProductTitleBox}>
        <div className={styles.productTitleContent}>
          <div className={styles.productTitle}>
            <h1>{data.title}</h1>
            {
              data?.url ?
                <a
                  className={styles.productLink}
                  href={data?.url}
                  target="_blank"
                  rel="noreferrer"
                >
                  <i className="help-iconfont help-icon-external-link" />
                </a> :
                null
            }
          </div>
          <div className="main-brief">
            <p className={styles.productTitleIntroduce} dangerouslySetInnerHTML={{ __html: data?.introduction?.brief }} />
          </div>
          <div className={styles.productTitleBtn}>
            <div className={styles.btnLeft}>
              {
                mainBlockButtons?.map((item, index) => {
                  return (
                    <a
                      key={index}
                      className={classnames(
                        styles.titleBtn,
                        index === 0 ? styles.highlight : styles.base,
                      )}
                      href={item.url}
                      target="_blank"
                      title={item.title}
                      onClick={(e) => aLinkTrigger(e, history)}
                      rel="noreferrer"
                    >
                      {item.title}
                    </a>);
                })
              }
            </div>
          </div>
        </div>
        {
          data?.introduction?.videoInfo &&
          <div
            className={styles.productTitleVideo}
            onClick={() => { setIsShowVideo(true); }}
          >
            <CoverImage title={`${data?.title}产品简介`} scale={0.8} />
          </div>
        }
      </div>
      {
        isShowVideo ?
          <Video url={data?.introduction?.videoInfo?.videoUrl} onCloseClick={() => { setIsShowVideo(false); }} /> :
          null
      }
    </div >
  );
};

