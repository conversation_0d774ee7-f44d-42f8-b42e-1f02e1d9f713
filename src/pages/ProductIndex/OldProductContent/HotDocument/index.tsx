import React from 'react';
import { FormattedMessage } from 'react-intl';
import styles from './index.module.scss';

export default ({ data }) => {
  return (
    <div className={styles.helpBodyHotDocument}>
      <h2><FormattedMessage id="help.product.hotArticle" /></h2>
      <ul className={styles.helpBodyHotDocumentList}>
        {
          data?.map((item, index) => (
            <li key={index}>
              <span className={styles.circle} />
              <a href={item?.url} >{item?.title}</a>
            </li>
          ))
        }
      </ul>
    </div>
  );
};
