.helpBodyTofuBlock {
  padding: 0 45px;

  .tofuBlockCell {
    overflow: hidden;
    color: #666;
    border: 1px solid rgba(225, 225, 225, 0.6);
    border-radius: 3px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    min-height: 170px;

    &:hover {
      box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.1);
    }

    .tofuBlockLeft {
      width: 270px;
      margin: 21px 0px;
      border-right: 1px solid rgba(225, 225, 225, 0.6);

      .toufuBlockIcon {
        height: 25px;
        line-height: 25px;
        padding-left: 29px;
        font-size: 0px;
        display: flex;
        margin-bottom: 9px;
        padding-right: 17px;

        >i {
          font-size: 25px;
          color: #1366ec;
        }

        >h3 {
          font-size: 18px;
          height: 25px;
          line-height: 25px;
          padding-left: 18px;
          color: #181818;
        }
      }

      .tofuBlockDefaultContent {
        width: 180px;
        margin-left: 72px;

        >p {
          line-height: 24px;
          font-size: 12px;
          color: #999;
        }
      }
    }

    .tofuBlockList {
      flex: 1;
      column-count: 2;
      padding: 16px 0px 16px 21px;

      >li {
        padding: 5px;
        word-break: break-all;

        >a {
          color: #181818;
          font-size: 14px;

          &:hover {
            color: #1366ec;
            text-decoration: none;
          }
        }
      }
    }
  }
}

// mobile端
@media only screen and (max-width: 1055px) {
  .helpBodyTofuBlock {
    padding: 16px 16px 0 16px;

    .tofuBlockCell {
      padding: 0 16px;
      flex-direction: column;

      .tofuBlockLeft {
        width: 100%;
        padding: 20px 0;
        margin: 0;
        border-right: none;
        border-bottom: 1px solid rgba(225, 225, 225, 0.6);

        .toufuBlockIcon {
          padding: 0;
        }
      }

      .tofuBlockList {
        width: 100%;
        padding: 16px 0;
        column-count: 1;
      }
    }
  }
}