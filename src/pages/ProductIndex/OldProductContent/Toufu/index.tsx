import React from 'react';
import { aLinkTrigger } from '@/help-fe-common/utils/global/url/aLinkTrigger';
import { useHistory } from 'ice';
import styles from './index.module.scss';

const ICON_ENUM = ['help-icon-book', 'help-icon-introduction', 'help-icon-practice'];

export default ({ data }) => {
  const history = useHistory();

  return (
    <div className={styles.helpBodyTofuBlock}>
      {
        data?.map((touFu, index) => (
          <div key={index} className={styles.tofuBlockCell}>
            <div className={styles.tofuBlockLeft}>
              <div className={styles.toufuBlockIcon}>
                <i className={`help-iconfont ${ICON_ENUM[index < 3 ? index : 0]}`} />
                <h3>{touFu?.title}</h3>
              </div>
              <div className={styles.tofuBlockDefaultContent}>
                <p>{touFu?.brief}</p>
              </div>
            </div>
            <ul className={styles.tofuBlockList}>
              {
                touFu?.items?.map((item, ii) => (
                  <li key={ii}>
                    <a
                      onClick={(e) => aLinkTrigger(e, history, { from: 'product' })}
                      href={item.url}
                    >
                      <span>{item?.title}</span>
                    </a>
                  </li>
                ))
              }
            </ul>
          </div>
        ))
      }
    </div>
  );
};
