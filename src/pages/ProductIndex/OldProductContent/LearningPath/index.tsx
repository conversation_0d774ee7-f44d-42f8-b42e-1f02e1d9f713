import React from 'react';
import styles from './index.module.scss';

export default ({ data }) => {
  return (
    <div className={styles.container}>
      <h2>{data?.title}</h2>
      <div className={styles.contentBox}>
        {data?.chapters?.map((chapter, index) => (
          <div className={styles.contentCell} key={index}>
            <div className={styles.step}>{index + 1}</div>
            <ul className={styles.content}>
              <li className={styles.header}>{chapter?.title}</li>
              {chapter?.sections?.map((item, ii) => (
                <li key={ii} className={styles.item}>
                  <a href={item?.items?.[0]?.url}>{item?.title}</a>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  );
};
