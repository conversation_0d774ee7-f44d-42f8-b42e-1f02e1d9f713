.container {
  width: 100%;
  padding: 29px 45px;
  background-color: #fbfbfb;

  h2 {
    height: 48px;
    text-align: center;
    font-size: 20px;
    color: #181818;
  }

  .contentBox {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    border: 1px solid #e1e1e1;
    background-color: #ffffff;
    padding: 28px 0px;

    .contentCell {
      flex: 1;
      padding: 12px 22px;
      border-right: 1px solid #e1e1e1;
      display: flex;

      &:last-child {
        border-right: none;
      }

      .step {
        font-size: 28px;
        font-weight: 600;
        color: #1366ec;
        flex: 0 0 40px;
      }

      .content {
        .header {
          font-size: 18px;
          padding: 8px 0;
        }

        .item {
          font-size: 14px;
          padding: 4px 0;

          a {
            color: #181818;

            &:hover {
              color: #1366ec;
            }
          }
        }
      }
    }
  }
}