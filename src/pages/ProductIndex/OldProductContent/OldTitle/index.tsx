import React from 'react';
import classnames from 'classnames';
import { FormattedMessage } from 'react-intl';

import styles from './index.module.scss';

export default ({ data }) => {
  return (
    <div className={styles.helpBodyBoxProductTitle}>
      <div className={styles.helpBodyBoxProductTitleBox}>
        <div className={styles.productTitleContent}>
          <div className={styles.productTitle}>
            <h1>{data?.title}</h1>
            {
              data?.url ?
                <a
                  className={styles.productLink}
                  href={data?.url}
                  target="_blank"
                  rel="noreferrer"
                >
                  <i className="help-iconfont help-icon-external-link" />
                </a> :
                null
            }
          </div>
          <div className={styles.productTitleIntroduceNotvideo}>
            <p className="main-brief" dangerouslySetInnerHTML={{ __html: data?.introduction?.brief }} />
          </div>
        </div>
      </div>
    </div>
  );
};
