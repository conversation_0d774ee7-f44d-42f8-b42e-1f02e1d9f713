.helpBodyBoxProductTitle {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 30px 45px 0 44px;
  background: #ffffff;

  .helpBodyBoxProductTitleBox {
    display: flex;
    justify-content: space-between;
    width: 100%;
    border-bottom: 1px solid #ededed;

    .productTitleContent {
      margin-right: 22px;

      .productTitle {
        &>h1 {
          font-size: 36px;
          color: #373d41;
          display: inline-block;
          height: 50px;
          line-height: 50px;
        }

        .productLink {
          vertical-align: super;
          align-self: flex-end;
          margin-left: 10px;
          height: 25px;
          line-height: 25px;
          font-size: 12px;
          color: #1366ec;
          cursor: pointer;

          >.goProduct {
            width: 16px;
            height: 16px;
          }
        }
      }

      .productTitleIntroduceNotvideo {
        width: 100%;
        height: auto;
        font-size: 14px;
        color: #373d41;
        line-height: 28px;
        overflow: hidden;
        margin-top: 3px;
        margin-bottom: 36px;
        overflow: hidden;

        >p {
          line-height: 28px;
        }

        .productTitleBtn {
          display: flex;
          justify-content: space-between;
          margin-top: 20px;

          .btnLeft {
            display: flex;
            align-items: center;
          }

          .titleBtn {
            display: inline-block;
            box-sizing: border-box;
            max-width: 148px;
            padding: 0 12px;
            height: 28px;
            border: 1px solid rgba(151, 151, 151, 0.5);
            font-size: 12px;
            text-align: center;
            line-height: 26px;
            text-decoration: none;
            cursor: pointer;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-right: 14px;
          }

          .highlight {
            background-color: #1366ec;
            color: #fff;
            border: 1px solid #1366ec;

            &:hover {
              background-color: #fff;
              color: #1366ec;
            }
          }
        }
      }
    }
  }
}

// mobile端
@media only screen and (max-width: 1055px) {
  .helpBodyBoxProductTitle {
    padding: 16px 16px 0 16px;

    .helpBodyBoxProductTitleBox {
      border-bottom: none;
    }
  }
}