.contentWrapper {
  position: relative;
  width: 0;
  flex: 1;
  padding: 36px 0 0px 0;

  .helpBodyBoxHeader {
    padding-left: 45px;
  }

  .helpBodyBoxProductContent {
    padding-top: 22px;

    .productContentList {
      display: block;

      >:last-child {
        padding-bottom: 45px;
      }
    }
  }
}

//移动端
@media screen and (max-width: 1055px) {
  .contentWrapper {
    padding: 16px 0 0;

    .helpBodyBoxHeader {
      padding: 0 16px;
    }

    .helpBodyBoxProductContent {
      padding: 0;
    }
  }
}