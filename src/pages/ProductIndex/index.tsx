import React, { useEffect, useMemo, useState } from 'react';
import { useHistory, useLocation } from 'ice';
import NotFound from '@/help-fe-common/components/common/NotFound';
import BreadCrumb from '@/help-fe-common/components/common/BreadCrumb';
import Loading from '@/help-fe-common/components/common/Loading';
import Title from '@/pages/ProductIndex/Title';
import SubProduct from '@/help-fe-common/components/common/ProductIndex/SubProduct';
import LearningPath from '@/pages/ProductIndex/ProductContent/Learning';
import ExperienceCourse from '@/pages/ProductIndex/ProductContent/Course';
import Question from '@/pages/ProductIndex/ProductContent/Question';
import HotVideo from '@/pages/ProductIndex/ProductContent/Video';
import OldTitle from '@/pages/ProductIndex/OldProductContent/OldTitle';
import OldToufu from '@/pages/ProductIndex/OldProductContent/Toufu';
import OldLearningPath from '@/pages/ProductIndex/OldProductContent/LearningPath';
import OldHotDocument from '@/pages/ProductIndex/OldProductContent/HotDocument';
import { extractUrlParam } from '@/help-fe-common/utils/global/url/urlExtract';
import { triggerUrlBehavior } from '@/help-fe-common/utils/global/url/urlBehavior';
import { isSameAlias } from '@/help-fe-common/utils/global/isSameAlias';
import useChangeTitle from '@/help-fe-common/hooks/useChangeTitle';
import { isServer } from '@/help-fe-common/utils/node/getContext';
import { ProductModel } from '@/help-fe-common/models/ProductModel';
import { storeDataMerge } from '@/help-fe-common/models/common/storeDataMerge';
import { productStore } from '@/help-fe-common/models/application/store';
import styles from './index.module.scss';

const NEW_TEMPLATE_TYPE = 2;

async function fetchInitialData(
  urlPath: string,
  // CSR 下使用已经new 过的 Store，避免多份 Store 一直在内存中
  storeMap: {
    productInnerStore: ProductModel;
  },
) {
  if (!urlPath) {
    return null;
  }

  const url = urlPath.indexOf('?') > -1 ? urlPath.substring(0, urlPath.indexOf('?')) : urlPath;

  const { productInnerStore } = storeMap;

  await productInnerStore.getProductInfo(url);

  return { storeData: productInnerStore };
}

const ProductIndex = ({ productData }) => {
  const history = useHistory();
  const location = useLocation();
  const [isLoading, setIsLoading] = useState(false);
  const [initData, setInitData] = useState(productData?.storeData || null);

  const identifier = useMemo(() => extractUrlParam(location?.pathname), [location?.pathname]);

  const {
    data,
    redirectUrl,
    isNotFound,
    helpResponseCode,
  } = initData || {};

  useChangeTitle({ title: data?.seoTitle || data?.title });

  useEffect(() => {
    const { nodeId, alias } = identifier || {};
    const isNewProduct = (Number(nodeId) !== data?.nodeId && !isSameAlias(String(alias), data?.alias));
    setIsLoading(true);

    // SSR getInitialProps 没有值时的兜底逻辑
    // isNewProduct表示切换新产品，走CSR重新请求

    const dataPromise = productData && !isNewProduct
      ? Promise.resolve(productData)
      : fetchInitialData(`${location.pathname}`, {
        productInnerStore: new ProductModel(),
      });
    if (productData && !isNewProduct) {
      setInitData(null);
      productStore.initData();
    }
    dataPromise
      .then((res) => {
        if (res === null) {
          console.error('csr 渲染问题');
        }
        // 初始化数据
        const { storeData } = res;
        setInitData(storeData);
        storeDataMerge(productStore, storeData);

        // 设置全局变量nodeId
        window.globalData = window.globalData || {};
        window.globalData.nodeId = storeData?.data?.nodeId;

        // loading效果最后清除，避免新数据未获取到，导致旧数据渲染
        setIsLoading(false);
      })
      .catch((err) => {
        console.error('[Doc detail] error: ', err);
      });
  }, [identifier]);

  useEffect(() => {
    if (redirectUrl) {
      triggerUrlBehavior(redirectUrl, history, { from: 'product' }, '', 'replace');
    }
  }, [redirectUrl]);

  if (isLoading) {
    return (
      <Loading
        loading
        style={{ position: 'relative', minHeight: '100vh', zIndex: 1 }}
        iconStyle={{ position: 'fixed', top: '50%' }}
      />
    );
  }

  if (isNotFound) {
    return (
      <div className={styles.contentWrapper}>
        <NotFound responseCode={helpResponseCode} />
      </div>);
  }

  return (
    <div className={styles.contentWrapper}>
      {
        data ?
          <div className={styles.helpBodyBoxHeader}>
            <BreadCrumb data={[{ title: data?.title, id: data?.id || '' }]} />
          </div> :
          null
      }
      {
        data?.templateType === NEW_TEMPLATE_TYPE ?
          <>
            {data ? <Title data={data} /> : null}
            <div className={styles.helpBodyBoxProductContent}>
              <ul className={styles.productContentList}>
                {data?.subModule ? <SubProduct data={data?.subModule} /> : null}
                {data?.learningPath ? <LearningPath data={data?.learningPath} /> : null}
                {data?.experienceLab && data?.experienceLab?.courseList?.length ? <ExperienceCourse data={data?.experienceLab} /> : null}
                {data?.commonProblem && data?.commonProblem?.linkList?.length ? <Question data={data?.commonProblem} /> : null}
                {data?.hotVideoList ? <HotVideo data={data?.hotVideoList} /> : null}
              </ul>
            </div>
          </> :
          <>
            {data ? <OldTitle data={data} /> : null}
            <div className={styles.helpBodyBoxProductContent}>
              <ul className={styles.productContentList}>
                {data?.subModule ? <SubProduct data={data?.subModule} /> : null}
                {data?.touFuList && data?.touFuList?.length > 0 ? <OldToufu data={data?.touFuList} /> : null}
                {data?.learningPath && data?.learningPath?.chapters ? <OldLearningPath data={data?.learningPath} /> : null}
                {data?.hotList && data?.hotList?.length > 0 ? <OldHotDocument data={data?.hotList} /> : null}
              </ul>
            </div>
          </>
      }
    </div>
  );
};

ProductIndex.getInitialProps = async (ctx) => {
  if (!isServer()) {
    return { productData: null };
  }

  const url = ctx?.req?.url || '';
  const res = await fetchInitialData(url, {
    productInnerStore: new ProductModel(),
  });
  return { productData: res };
};

ProductIndex.pageConfig = {
  spm: '11174283',
};

export default ProductIndex;
