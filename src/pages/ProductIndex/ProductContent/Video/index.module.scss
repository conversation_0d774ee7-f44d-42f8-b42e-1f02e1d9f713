.hotVideo {
  background-color: #fbfbfb;
  padding: 40px 45px 0 45px;

  .hotVideoContent {

    ul {
      &::after {
        content: "";
        display: block;
        clear: both;
      }

      li {
        position: relative;
        float: left;
        margin-bottom: 30px;
        margin-right: 20px;
        cursor: pointer;
      }
    }
  }
}

// mobile端
@media only screen and (max-width: 1055px) {
  .hotVideo {
    padding: 35px 16px 0;

    .hotVideoContent {
      ul {

        li {
          margin-right: 0;
        }
      }
    }
  }
}