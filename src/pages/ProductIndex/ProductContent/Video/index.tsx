import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import ModuleHead from '@/help-fe-common/components/common/ProductIndex/ModuleHead';
import Video from '@/help-fe-common/components/pc/Video';
import CoverImage from '@/help-fe-common/components/pc/CoverImage';

import styles from './index.module.scss';

export default ({ data }) => {
  const intl = useIntl();
  const [videoUrl, setVideoUrl] = useState('');
  const [isShowVideo, setIsShowVideo] = useState(false);

  return (
    <li className={styles.hotVideo}>
      <ModuleHead title={intl.formatMessage({ id: 'help.product.hotVideo' })} desc={''} />
      <div className={styles.hotVideoContent}>
        <ul>
          {data?.map((item, index) => (
            <li
              key={index}
              onClick={() => {
                setIsShowVideo(true);
                setVideoUrl(item?.videoUrl);
              }}
            >
              <CoverImage title={item?.title} />
            </li>
          ))}
        </ul>
      </div>
      {isShowVideo ?
        <Video url={videoUrl} onCloseClick={() => { setIsShowVideo(false); }} /> :
        null
      }
    </li>
  );
};
