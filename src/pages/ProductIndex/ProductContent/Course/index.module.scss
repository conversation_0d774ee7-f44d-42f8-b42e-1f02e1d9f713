.experienceCourse {
  background-color: #fbfbfb;
  padding: 40px 45px 0 45px;

  .experienceCourseContent {
    margin-top: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(248px, 1fr));
    grid-gap: 20px;

    .courseContentItem {
      min-width: 248px;
      min-height: 156px;
      height: auto;
      background-color: #ffffff;
      border: 1px solid #ededed;
      padding: 24px;
      cursor: pointer;
      background-position: right bottom;
      background-size: cover;
      background-repeat: no-repeat;

      &:hover {
        animation: backgroundIMG 0.2s ease-in 0s;
        animation-fill-mode: forwards;

        .courseHeader {
          color: #1366ec;
        }
      }

      @keyframes backgroundIMG {
        100% {
          box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.1);
          background-image: url('https://img.alicdn.com/imgextra/i3/O1CN01ldbihd1J6syekLpUG_!!6000000000980-2-tps-1456-246.png');
        }
      }

      .courseHeader {
        height: 24px;
        line-height: 24px;
        font-size: 16px;
        color: #181818;
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-decoration: none;
      }

      .courseList {
        margin-top: 11px;

        .courseLi {
          position: relative;
          cursor: pointer;

          >i {
            display: inline-block;
            font-size: 12px;
            transform: scale(0.5);
            color: #d8d8d8;
            position: absolute;
            top: 5px;
          }

          >p {
            display: inline-block;
            color: #3d3d3d;
            font-size: 12px;
            line-height: 24px;
            padding-left: 15px;
            width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-decoration: none;
          }
        }
      }
    }
  }
}

// mobile端
@media only screen and (max-width: 1055px) {
  .experienceCourse {
    padding: 35px 16px 0;
  }
}