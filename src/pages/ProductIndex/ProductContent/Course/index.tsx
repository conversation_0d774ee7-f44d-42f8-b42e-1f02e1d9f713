import React from 'react';
import { useIntl } from 'react-intl';
import { useHistory } from 'ice';
import CommonHead from '@/help-fe-common/components/common/ProductIndex/ModuleHead';
import { aLinkTrigger } from '@/help-fe-common/utils/global/url/aLinkTrigger';
import styles from './index.module.scss';

export default ({ data }) => {
  const intl = useIntl();
  const history = useHistory();

  return (
    <li className={styles.experienceCourse}>
      <CommonHead title={intl.formatMessage({ id: 'help.product.experienceCourse' })} desc={data?.desc} />
      <div className={styles.experienceCourseContent}>
        {
          data?.courseList?.map((item, index) => (
            <a
              className={styles.courseContentItem}
              key={index}
              href={item?.url}
              onClick={(e) => aLinkTrigger(e, history)}
            >
              <h3 className={styles.courseHeader} title={item?.title}>{item?.title}</h3>
              <ul className={styles.courseList}>
                {
                  item?.content?.map((child, childIdex) => (
                    <li className={styles.courseLi} key={childIdex}>
                      <i className={'help-iconfont help-icon-yuandianicon'} />
                      <p title={child}>{child}</p>
                    </li>
                  ))
                }
              </ul>
            </a>
          ))
        }
      </div>
    </li >
  );
};
