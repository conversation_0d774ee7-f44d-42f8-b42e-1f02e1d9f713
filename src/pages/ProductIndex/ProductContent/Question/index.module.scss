.commonQuestion {
  padding: 45px 45px 0 45px;
  background-color: #fbfbfb;
  overflow: hidden;

  .productFaqContent {
    background-color: #ffffff;
    background-image: url('https://img.alicdn.com/imgextra/i2/O1CN01qdcl6X1kg90S3qruh_!!6000000004712-2-tps-1946-542.png');
    background-position: right bottom;
    background-size: contain;
    background-repeat: no-repeat;
    margin-top: 20px;
    padding-bottom: 29px;
    border: 1px solid #ededed;
    padding: 20px 40px;

    .productFaqList,
    .productFaqListExpand {
      overflow: hidden;
      column-count: 2;

      .productFaqLi {
        font-size: 14px;
        line-height: 38px;
        display: flex;

        >i {
          display: inline-block;
          transform: scale(0.5);
          color: #1366ec;
        }

        >a {
          color: #666666;
          display: inline-block;
          width: 350px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          text-decoration: none;

          &:hover {
            color: #1366ec;
            text-decoration: none;
          }
        }
      }
    }

    .productFaqListExpand {
      margin-top: 0px;
    }

    .productFaqFooter {
      margin: 15px 0px 0px;

      .moreShow {
        height: 20px;
        line-height: 20px;
        color: #1366ec;
        font-size: 14px;
        cursor: pointer;
      }

      >i {
        font-size: 12px;
        color: #1366ec;
        display: inline-block;
        transform: scale(0.66);
      }
    }
  }
}

// mobile端
@media only screen and (max-width: 1055px) {
  .commonQuestion {
    padding: 35px 16px 0;

    .productFaqContent {
      padding: 15px;

      .productFaqList {
        column-count: 1;
      }
    }
  }
}