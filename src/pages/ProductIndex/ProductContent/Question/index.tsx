import React, { useEffect, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { useHistory } from 'ice';
import ModuleHead from '@/help-fe-common/components/common/ProductIndex/ModuleHead';
import { aLinkTrigger } from '@/help-fe-common/utils/global/url/aLinkTrigger';
import styles from './index.module.scss';

export default ({ data }) => {
  const intl = useIntl();
  const history = useHistory();
  const [faqShow, setFaqShow] = useState(false);
  const [faqSwitch, setFaqSwitch] = useState(false);

  const moreShowClick = () => {
    setFaqSwitch(!faqSwitch);
  };
  useEffect(() => {
    if (data?.linkList.length > 10) {
      setFaqShow(true);
    } else {
      setFaqShow(false);
    }
  }, []);

  return (
    <li className={styles.commonQuestion}>
      <ModuleHead title={intl.formatMessage({ id: 'help.product.commonQuestion' })} desc={data?.desc} />
      <div className={styles.productFaqContent}>
        <ul className={styles.productFaqList}>
          {
            data?.linkList?.map((item, index) => (
              index <= 9 &&
              <li className={styles.productFaqLi} key={index} >
                <i className={'help-iconfont help-icon-yuandianicon'} />
                <a
                  onClick={(e) => aLinkTrigger(e, history, { from: 'product' })}
                  href={item?.linkUrl}
                  title={item.linkName}
                >{item.linkName}
                </a>
              </li>
            ))
          }
        </ul>
        {
          (faqShow && faqSwitch) &&
          <ul className={styles.productFaqListExpand}>
            {
              data?.linkList?.map((item, index) => (
                (index > 9 && index <= 19) &&
                <li className={styles.productFaqLi} key={index}>
                  <i className={'help-iconfont help-icon-yuandianicon'} />
                  <a href={item?.linkUrl} target="_blank" title={item.linkName} rel="noreferrer">{item.linkName}</a>
                </li>
              ))
            }
          </ul>
        }
        {
          faqShow &&
          <div className={styles.productFaqFooter}>
            <span className={styles.moreShow} onClick={() => { moreShowClick(); }}>
              <FormattedMessage id={faqSwitch ? 'help.product.putAway' : 'help.product.Expand'} />
            </span>
            <i className={['help-iconfont', faqSwitch ? 'help-icon-yijizhankai' : 'help-icon-yijishouqi'].join(' ')} />
          </div>
        }
      </div>
    </li >
  );
};
