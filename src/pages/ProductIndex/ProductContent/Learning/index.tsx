import React from 'react';
import { useHistory } from 'ice';
import { useIntl } from 'react-intl';
import ModuleHead from '@/help-fe-common/components/common/ProductIndex/ModuleHead';
import { aLinkTrigger } from '@/help-fe-common/utils/global/url/aLinkTrigger';
import { ProductIcon } from '@/help-fe-common/constants';
import styles from './index.module.scss';

export default ({ data }) => {
  const history = useHistory();
  const intl = useIntl();

  return (
    <li className={styles.learningPath}>
      <ModuleHead title={intl.formatMessage({ id: 'help.product.learningPath' })} desc={data?.brief} />
      <div className={styles.learningPathContent}>
        {
          data?.chapters?.length ?
            data?.chapters?.map((chapter, index) => {
              return (
                <div key={index} className={styles.chapterItem}>
                  {
                    chapter?.sections?.map((section, index1) => {
                      return (
                        <div className={styles.chapter} key={index1}>
                          <div className={styles.chapterHead}>
                            {
                              index1 === 0 ?
                                <>
                                  <div className={styles.chapterTitle}>
                                    <h3>{chapter.title}</h3>
                                  </div>
                                  <div className={styles.productIcon}>
                                    <img src={ProductIcon[index]?.icon} alt="" />
                                  </div>
                                </>
                                :
                                null
                            }
                          </div>
                          <div className={styles.chapterContent}>
                            <div className={styles.section}>
                              <div className={styles.sectionTitle} title={section?.title}>
                                <b>{section?.title}</b>
                              </div>
                              <ul className={styles.sectionList}>
                                {
                                  section?.items?.map((item, index2) => {
                                    return (
                                      <li key={index2}>
                                        <a
                                          onClick={(e) => aLinkTrigger(e, history, { from: 'product' })}
                                          href={item?.url}
                                          title={item?.title}
                                        >
                                          {item?.title}
                                        </a>
                                      </li>
                                    );
                                  })
                                }
                              </ul>
                            </div>
                          </div>
                        </div>
                      );
                    })
                  }
                </div>
              );
            })
            :
            null
        }
      </div>
    </li>
  );
};
