.learningPath {
  min-width: 648px;
  padding: 0 45px 40px 45px;

  .learningPathContent {
    .chapterItem {
      position: relative;

      &::before {
        content: '';
        width: 1px;
        height: 100%;
        background-color: #e3e8ed;
        position: absolute;
        left: 72px;
        top: 40px;
      }

      &:last-child::before {
        height: 0px;
        width: 0px;
      }

      .chapter {
        display: flex;
        justify-content: center;

        .chapterHead {
          width: 130px;
          display: flex;
          align-items: center;
          justify-content: left;

          .chapterTitle {
            width: 36px;
            margin-right: 15px;
            text-align: center;
            background-color: #fff;
            z-index: 1;

            h3 {
              font-size: 18px;
              color: #181818;
              font-weight: 500;
              margin: 0;
              padding: 0;
            }
          }

          .productIcon {
            display: flex;
            background-color: #fff;
            padding: 5px 0;
            z-index: 1;

            img {
              max-width: 44px;
              max-height: 44px;
            }
          }
        }

        .chapterContent {
          width: calc(100% - 140px);
          margin-left: 20px;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: center;

          .section {
            width: 100%;
            min-height: 84px;
            color: #666;
            border: 1px solid rgba(225, 225, 225, 0.6);
            border-radius: 3px;
            margin-bottom: 8px;
            padding: 15px 0;
            display: flex;
            align-items: center;
            overflow: hidden;

            &:hover {
              box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.1);
            }

            .sectionTitle {
              width: 110px;
              padding: 0 20px;

              >b {
                margin: 0 auto;
                font-size: 14px;
                font-weight: normal;
                color: #181818;
                text-align: left;
                line-height: 20px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                word-break: break-word;
                -webkit-box-orient: vertical;
              }
            }

            .sectionList {
              width: calc(100% - 111px);
              height: auto;
              border-left: 1px solid rgba(225, 225, 225, 0.6);
              padding-left: 25px;
              display: flex;
              flex-wrap: wrap;
              justify-content: flex-start;

              >li {
                width: auto;
                width: 33%;
                font-size: 14px;
                height: auto;
                line-height: 20px;
                padding: 0 20px;
                margin: 6px 0;

                @media screen and (max-width: 768px) {
                  min-width: 100%;
                }

                >a {
                  color: #666666;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: -webkit-box;
                  word-break: break-word;
                  -webkit-box-orient: vertical;
                  text-decoration: none;

                  &:hover {
                    color: #1366ec;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

// mobile端
@media only screen and (max-width: 1055px) {
  .learningPath {
    min-width: auto;
    width: 100%;
    padding: 35px 16px 0;

    .learningPathContent {
      .chapterItem {
        &::before {
          display: none;
        }

        .chapter {
          flex-direction: column;
          align-items: center;

          .chapterHead {
            width: 100%;
            flex-direction: row-reverse;
            margin-bottom: 12px;

            .chapterTitle {
              margin-left: 12px;
              width: auto;
            }
          }

          .chapterContent {
            width: 100%;
            margin-left: 0;

            .section {
              flex-direction: column;
              padding: 0;

              .sectionTitle {
                width: 100%;
                height: 62px;
                line-height: 62px;

                >b {
                  font-size: 18px;
                  display: inline;
                }
              }

              .sectionList {
                width: 100%;
                border-left: none;
                border-top: 1px solid hsla(0, 0%, 88%, .6);
                padding: 15px 0 21px;
              }
            }
          }
        }
      }
    }
  }
}