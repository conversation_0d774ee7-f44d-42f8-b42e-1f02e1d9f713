export const stepLabel = [
  { label: 'intro', text: '教程简介' },
  { label: 'step', text: '步骤' },
  { label: 'sum', text: '总结' },
];

export const stepIcon = {
  intro: {
    icon: 'help-icon-intro',
    selectedIcon: 'help-icon-selected-intro',
  },
  sum: {
    icon: 'help-icon-sum',
    selectedIcon: 'help-icon-selected-sum',
  },
};

export const tutorialClassList = [
  { className: '.section.onestop', label: 'onestop', category: '一键配置版' },
  { className: '.section.manual', label: 'manual', category: '手动配置版' },
];

export enum TutorialTypeEnum {
  NOT = 0, // 普通文档
  TUTORIAL = 1, // 教程文档
  SOLUTION = 2 // 解决方案文档
}

export const tutorialTypeMap = {
  [TutorialTypeEnum.TUTORIAL]: [
    { label: 'onestop', category: '一键配置版' },
    { label: 'manual', category: '手动配置版' },
  ],
  [TutorialTypeEnum.SOLUTION]:
    [
      { label: 'onestop', category: '一键部署' },
      { label: 'manual', category: '手动部署' },
    ],
};

export const tryCenterUrl = {
  [TutorialTypeEnum.TUTORIAL]: 'https://free.aliyun.com',
  [TutorialTypeEnum.SOLUTION]: 'https://bp.aliyun.com',
};

export const tutorialHeadText = {
  [TutorialTypeEnum.TUTORIAL]:
  {
    buttonText: 'help.tutorial.try',
  },
  [TutorialTypeEnum.SOLUTION]: {
    buttonText: 'help.solution.headClick',
  },
};

export const tutorialFooterText = {
  [TutorialTypeEnum.TUTORIAL]:
  {
    text: 'help.tutorial.text',
    buttonText: 'help.tutorial.try',
  },
  [TutorialTypeEnum.SOLUTION]: {
    text: 'help.solution.text',
    buttonText: 'help.solution.footerClick',
  },
};
