.indexContainer {
  overflow: hidden;

  .top {
    height: 300px;
    background-image: url(https://img.alicdn.com/imgextra/i3/O1CN01LpzOpu1JfhHfr57Rk_!!6000000001056-0-tps-3840-600.jpg);
    background-size: cover;
    background-position: bottom;
    padding-top: 64px;
    padding-bottom: 64px;
    position: relative;
  }

  .loading {
    width: 100vw;
    height: 100vh;

    :global {
      .next-loading-dot {
        background: #1366ec;
      }
    }
  }
}

@media only screen and (max-width: 1055px) {
  .indexContainer {
    .top {
      height: auto;
      padding-top: 40px;
      padding-bottom: 0;
      margin-bottom: 32px;
    }
  }
}