import React, { useEffect, useState, useMemo } from 'react';
import { useLocation } from 'ice';
import classNames from 'classnames';
import { isEmpty } from 'lodash';
import FullColWrapper from '@/help-fe-common/components/common/FullColWrapper';
import Banner from '@/solution/components/Detail/DetailBanner';
import Anchor from '@/solution/components/Common/Anchor';
import Floor from '@/solution/components/Detail/Floor';
import Consult from '@/solution/components/Common/Consult';
import MobileBottom from '@/solution/components/Detail/MobileBottom';
import NotFound from '@/help-fe-common/components/common/NotFound';
import { SolutionDetailModel } from '@/solution/models/SolutionDetailModel';
import { storeDataMerge } from '@/help-fe-common/models/common/storeDataMerge';
import { isServer } from '@/help-fe-common/utils/node/getContext';
import { sendAemEventLog } from '@/help-fe-common/utils/global/track/aem';
import { extractUrlParam } from '@/help-fe-common/utils/global/url/urlExtract';
import { fetchInitialData } from '@/solution/services/fetchData';
import { ComponentTypeEnum } from '@/solution/constants';
import styles from './index.module.scss';

const solutionNodeId = window?.globalData?.nodeId || '';

const SolutionDetail = ({ solutionDetailData }) => {
  const location = useLocation();
  const [initData, setInitData] = useState(solutionDetailData?.storeData || null);

  const identifier = useMemo(() => extractUrlParam(location?.pathname), [location?.pathname]);

  const {
    data: solutionData,
    recommendSolutionList,
    isNotFound,
    helpResponseCode,
  } = useMemo(() => {
    if (!initData) return {};
    return initData;
  }, [initData]);

  const {
    anchorList, bannerHeadData, floorList,
  } = useMemo(() => {
    if (!solutionData) return {};
    const buttonGroup = solutionData?.bannerHeadData?.buttonGroup;
    // ssr渲染时处理只有单个按钮的场景
    if (buttonGroup?.length <= 1 && isServer()) {
      solutionData.bannerHeadData.buttonGroup = [
        ...buttonGroup,
        { text: '联系咨询', url: 'https://smartservice.console.aliyun.com/service/pre-sales-chat?from=solution' },
      ];
    }
    return solutionData;
  }, [solutionData]);

  useEffect(() => {
    const dataPromise = solutionDetailData
      ? Promise.resolve(solutionDetailData)
      : fetchInitialData(`${location.pathname}`, {
        detailInnerStore: new SolutionDetailModel(),
      });
    dataPromise
      .then((res: any) => {
        if (res === null) {
          console.error('csr 渲染问题');
        }
        // 初始化数据
        const { storeData } = res;
        setInitData(storeData);
        storeDataMerge(new SolutionDetailModel(), storeData);
      })
      .catch((err) => {
        console.error('[Solution detail] error: ', err);
      });
  }, [identifier]);

  // 阅读时长大于30s自动发送日志
  useEffect(() => {
    if (!identifier) return;
    const { alias } = identifier;
    const timer = setTimeout(() => {
      sendAemEventLog('validDetailPageView', {
        et: 'EXP',
        c1: solutionNodeId,
        c2: 'detail',
        c3: alias?.split('/')?.[2] || '',
      });
    }, 30000);

    return () => {
      clearInterval(timer);
    };
  }, [identifier]);

  if (isNotFound) {
    return (
      <div className={styles.contentWrapper}>
        <NotFound responseCode={helpResponseCode} />
      </div>);
  }

  return (
    <div className={classNames('aliyun-solution-detail', styles.solutionDetailContainer)}>
      {
        !isEmpty(initData) &&
        <>
          <FullColWrapper >
            <Banner data={bannerHeadData} />
          </FullColWrapper>
          <FullColWrapper className={styles.topNav} >
            <Anchor data={anchorList} buttonGroup={bannerHeadData?.buttonGroup} />
          </FullColWrapper>
          {
            floorList?.map((floorItemData, index) => {
              if (floorItemData?.data?.componentType === ComponentTypeEnum.RECOMMEND) {
                return recommendSolutionList?.length && (
                  <FullColWrapper key={index}>
                    <Floor floorData={{ ...floorItemData, data: { recommendSolutionList, ...floorItemData?.data } }} />
                  </FullColWrapper>);
              }
              return (
                <FullColWrapper key={index}>
                  <Floor
                    floorData={floorItemData}
                    style={index === 0 ?
                      {
                        background: 'linear-gradient(333deg, #ECF2FF 16%, rgba(255, 255, 255, 0) 71%)',
                      } : {}}
                  />
                </FullColWrapper>);
            })
          }
          <FullColWrapper>
            <Consult type="detail" />
          </FullColWrapper>
          <MobileBottom buttonGroup={bannerHeadData?.buttonGroup || []} />
        </>
      }
    </div>
  );
};

SolutionDetail.getInitialProps = async (ctx) => {
  if (!isServer()) {
    return { solutionDetailData: null };
  }

  const url = ctx?.req?.url || '';

  const res = await fetchInitialData(url, {
    detailInnerStore: new SolutionDetailModel(),
  });

  return { solutionDetailData: res };
};

SolutionDetail.pageConfig = {
  spm: solutionNodeId || '29338758',
};

export default SolutionDetail;

