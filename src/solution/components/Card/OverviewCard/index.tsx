import React from 'react';
import classNames from 'classnames';
import Tab from '@/solution/components/Common/Tab';
import { scrollIntoView } from '@/solution/utils';
import styles from './index.module.scss';

export default function SolutionCard({ cardData }) {
  const { title, link, tagList, score, scoreDesc, content } = cardData;

  const tagMap = {
    default: styles.defaultTag,
    mint: styles.lightGreenTag, // 薄荷绿
    iceBlue: styles.lightBlueTag, // 冰蓝
    lavender: styles.lightPurpleTag, // 紫色
  };

  const onUrlClick = () => {
    const url = link?.url;
    if (!url) return;
    if (url.startsWith('#')) {
      scrollIntoView(url.split('#')[1]);
    } else {
      window.open(url);
    }
  };

  const renderScore = () => {
    if (score) {
      const fillNum = Math.floor(score / 2);
      const halfNum = score % 2;
      return (
        <>
          <span className={styles.scoreNum}>
            {
              Array.from({ length: fillNum }).map((item, index) => (
                <i key={index} className="help-iconfont help-icon-star" />
              ))
            }
            {
              halfNum ? <i className={styles.halfStar} /> : null
            }
          </span>
          <span>{scoreDesc}</span>
        </>
      );
    } else {
      return null;
    }
  };

  return (
    <div className={styles.item}>
      <div
        className={styles.head}
        onClick={onUrlClick}
      >
        <div className={styles.title}>{title}</div>
        <div className={styles.tags} >
          {tagList.map((item, index) => (
            <span key={index} className={classNames(styles.tagItem, tagMap[item.type])}>{item.text}</span>
          ))}
        </div>
        <div className={styles.scoreBanner}>
          <div className={styles.left}>
            {
              renderScore()
            }
          </div>
          <div className={styles.button}>
            {link.text}
            <i className="help-iconfont help-icon-down-arrow" />
          </div>
        </div>
      </div>

      <div className={styles.content}>
        <Tab data={content.tabList} />
      </div>
    </div>
  );
}
