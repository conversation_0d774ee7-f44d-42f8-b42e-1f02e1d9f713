@mixin text-line($line-number: 1) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line-number;
}

.item {
  width: 100%;
  padding: 24px;
  background: #FFFFFF;
  border: 1px solid #E9E9E9;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);

  .head {
    margin-bottom: 24px;
    cursor: pointer;

    &:hover {
      .title {
        color: #1366ec;
      }
    }

    .title {
      font-size: 18px;
      font-weight: 600;
      line-height: 28px;
      margin-bottom: 12px;
      @include text-line(1);
    }

    .tags {
      display: flex;
      margin-bottom: 12px;

      .tagItem {
        margin-right: 8px;
        height: 20px;
        line-height: 20px;
        font-size: 12px;
        border-radius: 4px;
        padding: 0px 6px;
      }

      .defaultTag {
        background: #E9E9E9;
        color: #575757;
      }

      .lightGreenTag {
        background: #D8F3E0;
        color: #038024;
      }

      .lightBlueTag {
        background: #DCF1F7;
        color: #00779E;
      }

      .lightPurpleTag {
        background: #EEEBFC;
        color: #6D54EB;
      }
    }

    .scoreBanner {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .left {
        height: 24px;
        line-height: 24px;
        font-size: 14px;
        display: flex;
        align-items: center;

        .scoreNum {
          margin-right: 6px;
          display: flex;
          align-items: center;

          i {
            margin-right: 2px;
            font-size: 20px;
          }

          .halfStar {
            background-image: url('https://img.alicdn.com/imgextra/i4/O1CN01crPwHD24av5yq3d1L_!!6000000007408-55-tps-20-20.svg');
            background-size: 20px 20px;
            background-repeat: no-repeat;
            width: 20px;
            height: 20px;
          }
        }

        span {
          color: #FF6A00;
        }
      }

      .button {
        color: #1366ec;

        i {
          margin-left: 4px;
          font-size: 12px;
        }
      }
    }
  }

  :global(.next-tabs-pure) {
    :global {
      .next-tabs-bar {
        padding-left: 0;
        padding-right: 0;
        margin-left: 0;
        margin-right: 0;
      }
    }
  }
}

// 移动端
@media screen and (max-width: 1055px) {

  .item {}
}