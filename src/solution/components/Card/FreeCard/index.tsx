import { createAlfaWidget } from '@alicloud/alfa-react';
import React, { useEffect, useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';

interface AlfaWidgetProps {
  goodsId: number | string;
  loading?: boolean;
}

let AlfaWidget: any;
const FreeCard = ({ goodsId, loading = true }: AlfaWidgetProps) => {
  const [hasACE, setHasACE] = useState(Boolean(window.$ACE));

  useEffect(() => {
    try {
      window.$ACE_LOADER().then(() => {
        setHasACE(true);
      });
      // eslint-disable-next-line no-empty
    } catch (e) { }
  }, []);

  if (!hasACE) {
    return <React.Fragment />;
  }

  if (!AlfaWidget) {
    AlfaWidget = createAlfaWidget({
      name: '@ali/alfa-aliyundotcom-free-widget-goods-card',
      loading,
      noCache: true,
      sandbox: {
        disable: true,
        sandBoxUrl: 'about:blank',
      },
    });
  }

  return (
    <ErrorBoundary fallback={<></>}>
      <div className="help-free-card">
        <AlfaWidget
          miniBuyCard={{
            isShow: true,
            containerShow: true,
            showMoreInfo: true,
            balloon: 'none',
            loading: true,
          }}
          dialogParams={{
            popupContainer: 'adc-skill-builder-main',
          }}
          goodsId={goodsId}
        />
      </div>
    </ErrorBoundary>
  );
};

export default FreeCard;
