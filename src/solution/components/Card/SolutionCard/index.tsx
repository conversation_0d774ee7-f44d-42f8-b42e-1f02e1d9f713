import React, { useState } from 'react';
import classNames from 'classnames';
import { FormattedMessage } from 'react-intl';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import styles from './index.module.scss';

export default function SolutionCard({ solutionData }) {
  const { imgUrl, title, desc, url } = solutionData;

  const [iconHover, setIconHover] = useState(false);


  const renderCard = () => {
    return (
      <li className={styles.liItem}>
        {imgUrl &&
          <div className={styles.img} >
            <img src={imgUrl} alt="" loading="lazy" />
          </div>
        }
        <div className={classNames('item-bottom', styles.itemBottom)}>
          <div className={classNames('item-info', styles.itemInfo)}>
            <h3 title={title}>{title}</h3>
            <p title={desc}>{desc}</p>
          </div>
          {url && (
            <div
              className={classNames('item-link', styles.itemLink)}
              onMouseEnter={() => setIconHover(true)}
              onMouseLeave={() => setIconHover(false)}
            >
              <span><FormattedMessage id="help.solution.moreInfo" /></span>
              <i
                className={`help-iconfont 
                ${iconHover ? 'help-icon-small-arrow' : 'help-icon-right-arrow'} 
                ${iconHover ? styles.hoverArrow : ''}
              `}
              />
            </div>
          )}
        </div>
      </li>
    );
  };

  return (
    url ?
      <a
        href={url}
        onClick={() => {
          sendSlsLog({
            page: 'solutionDetail',
            section: 'recommend',
            action: 'click',
            userParams1: title,
            userParams2: url,
          });
        }}
        target={url ? '_blank' : undefined}
        rel="noreferrer"
        className={classNames('solution-recommend-card', styles.item)}
      >
        {renderCard()}
      </a>
      :
      <div className={classNames('solution-recommend-card', styles.item)} >
        {renderCard()}
      </div>
  );
}
