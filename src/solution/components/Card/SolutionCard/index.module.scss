@mixin text-line($line-number: 1) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line-number;
}

.item,
a[class*="solution-recommend-card"] {
  width: 100%;

  .liItem,
  >li {
    padding: 0;
    margin: 0;
    list-style: none;
    width: 100%;
    flex: auto;
    display: flex;
    flex-direction: column;
    border: 1px solid #E9E9E9;

    &:hover {
      .itemBottom {
        box-shadow: 0 4px 6px 0 rgba(0, 0, 0, 0.03);
      }
    }

    .img {
      width: 100%;
      height: 124px;
      overflow: hidden;

      img {
        width: 100%;
        height: 124px;
        object-fit: cover;
        transform: scale(1);
        transition: transform 0.6s ease-in-out;

        &:hover {
          transform: scale(1.2);
          transition: transform 0.6s ease-in-out;
        }
      }
    }

    .itemBottom,
    >div[class*="item-bottom"] {
      padding: 24px;
      display: flex;
      flex-direction: column;
      flex: auto;
      justify-content: space-between;

      >*:nth-child(n+2) {
        margin-top: 20px;
      }

      .itemInfo,
      >div[class*="item-info"] {
        display: flex;
        flex-direction: column;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #181818;
          @include text-line(1);
        }

        p {
          height: 60px;
          font-size: 14px;
          color: #8C8C8C;
          margin-top: 8px;
          @include text-line(3);
        }
      }

      .itemLink,
      >div[class*="item-link"] {
        height: 36px;
        width: 100px;
        display: flex;
        align-items: center;
        font-weight: 500;
        font-size: 14px;
        text-decoration: none;

        &:hover {
          span {
            color: #0f52bd;
          }
        }

        span {
          color: #1366EC;
        }

        >i {
          margin-left: 8px;
          width: 14px;
          height: 14px;
          background-color: #1366ec;
          color: #fff;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          font-size: 10px;
          transition: width 0.5s;
        }

        .hoverArrow {
          width: 24px;
          border-radius: 7px;
          font-size: 8px;
          transition: 0.5s;
        }
      }
    }
  }

  &:only-child {
    .liItem {
      flex-direction: row;

      .img {
        width: 50%;
        height: 220px;
        flex: none;

        img {
          height: 220px;
          width: 100%;
          object-fit: cover;
        }
      }

      .itemBottom {
        height: auto;
        border-top: none;
        border-left: 1px solid #e9e9e9;
      }
    }
  }
}

// 移动端
@media screen and (max-width: 1055px) {

  .item,
  a[class*="solution-recommend-card"] {

    .liItem,
    >li {

      .itemBottom,
      >div[class*="item-bottom"] {
        padding: 16px;
      }
    }
  }
}