.main {
  margin-bottom: 40px;

  .tabItem {
    padding-top: 12px;
    margin-left: calc(var(--col-wrapper-margin)* -1);
    margin-right: calc(var(--col-wrapper-margin)* -1);
  }

  .caseDiv {
    height: 268px;
    display: flex;
    margin-left: calc(var(--col-wrapper-margin)* -1);
    margin-right: calc(var(--col-wrapper-margin)* -1);
  }

  .detailImage {
    width: 50%;
    height: 100%;
    position: relative;
    flex: none;

    > div {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-position: center;
      background-repeat: no-repeat;
      background-size: cover;
      transition: .5s;
    }
  }

  .case {
    flex: auto;
    background-color: #1366ec;
    padding: 40px 22px 20px;
    padding-right: var(--col-wrapper-margin);
    display: flex;
    flex-direction: column;
    line-height: 0;

    .animationDiv {
      overflow: hidden;
      transition: .8s;
    }

    img {
      height: 30px;
      margin-bottom: 20px;
    }

    p {
      font-size: 14px;
      line-height: 22px;
      color: #FFFFFF;
      margin-bottom: 24px;
    }

    a {
      text-decoration: none;
      display: flex;
      align-items: center;
      font-weight: 500;
      color: #fff;

      >i {
        margin-left: 8px;
        width: 14px;
        height: 14px;
        background-color: #fff;
        color: #1366ec;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 12px;
      }
    }
  }

  .customerList {
    margin-top: 40px;
    display: flex;
    justify-content: space-between;

    > *:nth-child(n+2) {
      margin-left: 24px;
    }
  }

  .customerItem {
    width: 204px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      height: 60px;
    }
  }
}

@media screen and (max-width: 1055px) {
  .main {
    margin-bottom: 0;
    padding-left: 0;
    padding-right: 0;
    padding-top: 20px;

    .tabItem {
      padding-top: 0;
      margin: 0 -16px;
    }

    .caseDiv {
      height: auto;
      flex-direction: column;

      >img {
        height: 132px;
      }
    }

    .detailImage {
      width: 100%;
      height: 211px;
    }

    .case {
      height: 255px;
      padding: 23px 16px;

      img {
        margin-bottom: 23px;
      }

      p {
        margin-bottom: 18px;
      }
    }

    .customerList {
      margin-top: 16px;
      display: grid;
      grid-template-columns: repeat(2, 50%);
      gap: 0;

      > *:nth-child(n+2) {
        margin-left: 0;
      }
    }

    .customerItem {
      width: 100%;
    }
  }

  :global {
    .img-fade-exit {
      opacity: 1;
      z-index: 1;
      transform: translateX(0);
    }

    .img-fade-exit-active {
      opacity: 0.2;
      z-index: 1;
      transform: translateX(100%);
    }
  }
}
