import React from 'react';
import _ from 'lodash';
import classNames from 'classnames';
import { useIntl, FormattedMessage } from 'react-intl';
import { Tab } from '@alifd/next';
import Head from '@/solution/components/Index/Head';
import tabStyles from '@/solution/components/Common/Tab/tab.module.scss';
import styles from './index.module.scss';
import { CSSTransition, SwitchTransition } from 'react-transition-group';

export default function CustomerCase({ data }) {
  const intl = useIntl();
  const { caseList, customerList } = data;
  const [activeKey, setActiveKey] = React.useState('0');

  const caseItem = caseList?.[activeKey];

  return (
    <div className={classNames('col-extend-left', 'col-extend-right', styles.main)}>
      <Head title={intl.formatMessage({ id: 'help.solution.customerCase' })} />
      <Tab
        className={`${tabStyles.tab} ${tabStyles.tabAdaptive}`}
        contentClassName={styles.tabItem}
        activeKey={activeKey}
        onChange={setActiveKey}
      >
        {_.map(caseList, (item, index) => {
          return (
            <Tab.Item title={item.title} key={index} />
          );
        })}
      </Tab>
      {
        caseItem && (
          <div className={styles.caseDiv}>
            <div className={styles.detailImage}>
              <SwitchTransition mode="in-out">
                <CSSTransition
                  key={activeKey}
                  classNames="img-fade"
                  timeout={{
                    appear: 0,
                    enter: 0,
                    exit: 800,
                  }}
                >
                  <div style={{ backgroundImage: `url(${caseItem?.imageUrl})` }} />
                </CSSTransition>
              </SwitchTransition>
            </div>
            <div className={styles.case}>
              <SwitchTransition>
                <CSSTransition
                  key={activeKey}
                  classNames="text-fade"
                  timeout={500}
                >
                  <div className={styles.animationDiv}>
                    <div>
                      <img src={caseItem?.logoUrl} alt="" loading="lazy" />
                    </div>
                    <p>{caseItem?.desc}</p>
                    {caseItem?.detailUrl && (
                      <a href={caseItem?.detailUrl} target="_blank" rel="noreferrer">
                        <span><FormattedMessage id="help.solution.moreInfo" /></span>
                        <i className="help-iconfont help-icon-right-arrow" />
                      </a>
                    )}
                  </div>
                </CSSTransition>
              </SwitchTransition>
            </div>
          </div>
        )
      }
      <div className={styles.customerList}>
        {_.map(customerList, (customer) => {
          return (
            <div className={styles.customerItem}>
              <img alt="" src={customer?.logoUrl} loading="lazy" />
            </div>
          );
        })}
      </div>
    </div>
  );
}
