@mixin text-line($line-number: 1) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line-number;
}

.topicContainer {
  .cardList {
    display: flex;

    > *:nth-child(n+2) {
      margin-left: 24px;
    }

    .cardItem {
      height: 208px;
      width: calc(25% - 24px);
      padding: 24px;
      background: rgba(255, 255, 255, 0.8);
      color: #181818;
      flex: auto;

      .icon {
        width: 40px;
        height: 40px;
        margin-bottom: 28px;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
      }

      .title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 16px;
        @include text-line(1);

        i {
          margin-left: 7px;
        }

        &:hover {
          color: #1366ec;
        }
      }

      .desc {
        font-size: 14px;
        line-height: 24px;
        color: #999;
        @include text-line(2);
      }
    }
  }
}

@media screen and (max-width: 1055px) {
  .topicContainer {
    margin-bottom: 0;

    .cardList {
      flex-direction: column;

      > *:nth-child(n+2) {
        margin-top: 16px;
        margin-left: 0;
      }

      .cardItem {
        width: 100%;
      }
    }
  }
}
