import React from 'react';
import { useIntl } from 'react-intl';
import Head from '@/solution/components/Index/Head';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import styles from './index.module.scss';

interface IItem {
  title: string;
  desc: string;
  url: string;
  iconUrl: string;
}

interface IProps {
  data: IItem[];
}

const HotTopic = ({ data }: IProps) => {
  const intl = useIntl();

  return (
    <div className={styles.topicContainer} >
      <Head title={intl.formatMessage({ id: 'help.solution.hotTopic' })} />
      <div className={styles.cardList}>
        {
          data?.map((item: IItem, index) => (
            <a
              className={styles.cardItem}
              href={item?.url}
              onClick={() => {
                sendSlsLog({ page: 'solutionIndex', section: 'hotTopic', action: 'click', userParams1: index, userParams2: item?.url });
              }}
              key={index} target="_blank" rel="noreferrer">
              <div className={styles.icon} style={{ backgroundImage: `url(${item?.iconUrl})` }} />
              <h3 className={styles.title}>
                {item?.title}
                <i className="help-iconfont help-icon-right-arrow" />
              </h3>
              <p className={styles.desc}>{item?.desc}</p>
            </a>
          ))
        }
      </div>
      <div />
    </div>
  );
};

export default HotTopic;
