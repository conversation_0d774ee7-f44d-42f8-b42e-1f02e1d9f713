.container {
  width: 210px;

  .title {
    font-size: 14px;
    font-weight: 500;
    color: #181818;
    letter-spacing: 0.43px;
    line-height: 22px;
    margin-bottom: 6px;
    padding-left: 24px;
  }

  .clearButton {
    padding-left: 24px;
    border: none;
    background: none;
    font-weight: 500;
    font-size: 14px;
    color: #1366EC;
    line-height: 30px;
    cursor: pointer;
  }
}

.treeContainer {
  width: 100%;
  max-height: calc(100vh - 180px);
  padding-left: 24px;
  overflow-y: auto;
  position: relative;

  &::-webkit-scrollbar {
    width: 5px;
    height: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #dfdfdf;
    border-radius: 5px;
  }

  .liNodeContainer {
    margin-left: 0;
    margin-bottom: 7px;
    min-height: 30px;
    line-height: 30px;
    letter-spacing: 1px;
    display: flex;
    align-items: center;

    >i {
      height: 16px;
      width: 16px;
      line-height: 16px;
      font-size: 12px;
      color: #999;
      position: absolute;
      left: 0;
    }

    .checkBoxButton {
      font-size: 14px;
      color: #181818;
      letter-spacing: 1px;
      display: flex;

      :global {
        .next-checkbox {
          position: relative;
          top: 4px;
        }

        .next-checkbox-label {
          line-height: 24px;
        }
      }
    }
  }

  .childBox {
    padding-left: 24px;
    margin: 5px 0 3px 0;
    animation-name: box-expand;
    animation-duration: 0.3s;

    .liNodeContainer {
      .checkBoxButton {
        :global {
          .next-checkbox-label {
            color: #666;
          }
        }
      }
    }
  }

  @keyframes box-expand {
    0% {
      opacity: 0;
    }

    25% {
      opacity: 0.25;
    }

    50% {
      opacity: 0.5;
    }

    75% {
      opacity: 0.75;
    }

    100% {
      opacity: 1;
    }
  }
}