import React, { useEffect, useState, FunctionComponent, useImperativeHandle } from 'react';
import { Checkbox } from '@alifd/next';
import classNames from 'classnames';
import { FormattedMessage } from 'react-intl';
import { SolutionTreeTypeEnum } from '@/solution/constants';
import { ITreeItem } from '../type';
import styles from './index.module.scss';
import '@/help-fe-common/styles/checkbox.scss';

interface IProps {
  data: ITreeItem;
  onSelectedItemsChange: (selectedItems: string[]) => void;
  onRef: any;
}

const FilterTree: FunctionComponent<IProps> = ({ data, onSelectedItemsChange, onRef }) => {
  const [selectedItem, setSelectedItem] = useState([] as string[]);
  const [expandedItem, setExpandedItem] = useState([] as string[]);
  const [allowSelect, setAllowSelect] = useState(true); // 是否允许选中状态触发筛选

  useEffect(() => {
    allowSelect && onSelectedItemsChange(selectedItem);
  }, [allowSelect, selectedItem, onSelectedItemsChange]);

  // 向父组件暴露
  useImperativeHandle(onRef, () => {
    return {
      clearFilterItem: () => {
        setAllowSelect(false);
        setSelectedItem([]);
      },
    };
  });

  /**
   * 处理一级分类节点展开逻辑
   * @param item 一级分类节点
   */
  const handleExpandClick = (item) => {
    const itemId = item?.code;
    if (expandedItem.includes(itemId)) {
      setExpandedItem(expandedItem.filter((id) => id !== itemId));
    } else {
      setExpandedItem([...expandedItem, itemId]);
    }
  };

  /**
   * 处理节点选中逻辑
   * @param item 分类节点
   */
  const handleSelectClick = (item) => {
    setAllowSelect(true);
    const itemId = item?.code;
    if (selectedItem.includes(itemId)) {
      // 处理节点取消选中
      let filterItem = [] as string[];
      if (item.parentCode && selectedItem.filter((i) => String(i).startsWith(`${item.parentCode}/`)).length === 1) {
        filterItem = selectedItem.filter((i) => i !== itemId && i !== item.parentCode);
      } else {
        filterItem = selectedItem.filter((i) => i !== itemId && !String(i).startsWith(`${itemId}/`));
      }
      setSelectedItem(filterItem);
    } else {
      const selectedChildren = [] as string[];
      if (item.children?.length > 0) {
        item.children.forEach((child) => {
          selectedChildren.push(`${itemId}/${child.code}`);
        });
      }
      // 针对二级节点，设置parentId为选中节点
      if (item.parentCode && !selectedItem.includes(item.parentCode)) {
        setSelectedItem([...selectedItem, item.parentCode, itemId, ...selectedChildren]);
      } else {
        setSelectedItem([...selectedItem, itemId, ...selectedChildren]);
      }
      // 仅一级节点选中时，处理节点展开
      if (!expandedItem.includes(itemId)) {
        setExpandedItem([...expandedItem, itemId]);
      }
    }
  };

  /**
   * 判断一级节点checkbox是否全部选中
   * @param treeItem 节点数据
   * @returns
   */
  const showAllSelected = (treeItem) => {
    const len = selectedItem.filter((i) => String(i).startsWith(`${treeItem?.code}/`))?.length || 0;
    return len === (treeItem?.children?.length || 0);
  };

  /**
   * 生成二级节点，并处理选中逻辑
   * @param children
   * @param parentCode
   * @returns
   */
  const renderChildren = (children, parentCode) => {
    return (
      <ul className={styles.childBox}>
        {children.map((child, index) => (
          child?.type === SolutionTreeTypeEnum.CATEGORY &&
          <li key={index}>
            <div className={styles.liNodeContainer}>
              <Checkbox
                className={styles.checkBoxButton}
                checked={selectedItem.includes(`${parentCode}/${child.code}`)}
                onChange={() => handleSelectClick({ code: `${parentCode}/${child.code}`, parentCode, children: [] })}
              >
                {child.name}
              </Checkbox>
            </div>
          </li>
        ))}
      </ul>
    );
  };


  /**
   * 生成一级节点，并处理展开逻辑
   * @param treeData 解决方案列表树
   * @returns {ReactNode}
   */
  const renderTree = (treeData: ITreeItem[]) => {
    return (
      <ul className={styles.treeContainer}>
        {treeData?.length && treeData.map((treeItem: ITreeItem, index) => (
          <li key={index}>
            <div className={styles.liNodeContainer}>
              {treeItem?.children?.[0]?.type === SolutionTreeTypeEnum.CATEGORY ?
                <i
                  onClick={() => handleExpandClick(treeItem)}
                  className={classNames('help-iconfont', expandedItem.includes(treeItem?.code) ? 'help-icon-zhankai1-copy' : 'help-icon-zhankai1')}
                />
                : null}
              <Checkbox
                className={styles.checkBoxButton}
                indeterminate={
                  selectedItem.filter((i) => String(i).startsWith(`${treeItem?.code}/`))?.length > 0 &&
                  !showAllSelected(treeItem)}
                onChange={() => handleSelectClick(treeItem)}
                checked={selectedItem.includes(treeItem?.code) && showAllSelected(treeItem)}
              >
                {`${treeItem?.name}`}
              </Checkbox>
            </div>
            {
              treeItem?.children?.length
              && expandedItem.includes(treeItem.code)
              && renderChildren(treeItem.children, treeItem.code)
            }
          </li>
        ))}
      </ul>
    );
  };

  return (
    <div className={styles.container}>
      <h3 className={styles.title}><FormattedMessage id="help.solution.solutionCategory" /></h3>
      {data?.children && renderTree(data?.children)}
    </div>
  );
};

export default FilterTree;
