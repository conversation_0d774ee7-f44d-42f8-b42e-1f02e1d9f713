import { SolutionTreeTypeEnum } from '@/solution/constants';

interface INodeItem {
  id?: string;
  name: string;
  code: string;
  shortDesc: string;
  url: string;
  type: SolutionTreeTypeEnum;
  categoryTagMap?: {
    [key: string]: string;
  };
  freeInfo?: {
    isFree: boolean;
    freeToken: number;
    deployUrl?: string;
  };
}

interface ITreeItem {
  code: string;
  name: string;
  type: SolutionTreeTypeEnum;
  children?: ITreeItem[];
}

export { ITreeItem, INodeItem };
