// pc端
@media only screen and (min-width: 1056px) {
  .container {
    margin-bottom: 40px;
    padding-top: 40px;
    background: linear-gradient(-45deg, #ECF2FF 7%, #fff 50%);

    .contentContainer {
      display: flex;

      .mobileFilter {
        display: none;
      }

      .filterContainer {
        width: 210px;

        h2 {
          line-height: 22px;
          padding-left: 24px;
          margin-bottom: 6px;
          font-size: 16px;
        }

        .fixed {
          position: fixed;
          top: calc(var(--default-head-height) + 40px); // headHeight+paddingTop
        }
      }

      .mainContainer {
        flex: auto;
        overflow: hidden;

        .solutionCardList {
          display: grid;
          grid-template-columns: repeat(3, calc(33.33% - 16px));
          gap: 24px;
        }

        .pagination {
          text-align: right;
          padding: 40px 0;
        }
      }
    }
  }
}

// mobile端
@media only screen and (max-width: 1055px) {
  .filterContainer {
    display: none;
  }

  .mainContainer {
    background: linear-gradient(0deg, #D8D8D8 0%, #F4F9FF 0%, rgba(255, 255, 255, 0) 22%);
    padding-bottom: 10px;
  }

  .mobileFilter {
    display: block;
  }

  .mobileFilterGuide {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    height: 42px;
    width: 100%;
    border: 1px solid #e9e9e9;
    border-left: none;
    border-right: none;
    margin-bottom: 14px;

    >*:nth-child(n+2) {
      margin-left: 8px;
    }
  }

  .solutionCardList {
    display: flex;
    flex-direction: column;

    >*:nth-child(n+2) {
      margin-top: 16px;
    }
  }

  .pagination {
    margin: 18px 10px 0;
  }
}