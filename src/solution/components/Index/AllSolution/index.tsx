import React, { useState, FunctionComponent, useCallback, useRef, useMemo, useEffect } from 'react';
import classnames from 'classnames';
import { isEmpty } from 'lodash';
import { useIntl, FormattedMessage } from 'react-intl';
import { useRequest } from 'ice';
import { Drawer, Pagination } from '@alifd/next';
import Head from '@/solution/components/Index/Head';
import Search from './Search';
import FilterTree from './FilterTree';
import MobileFilterDialog from './MobileFilterDialog';
import NoResult from '@/help-fe-common/components/common/FilterNoResult';
import SolutionCard from './SolutionCard';
import services from '@/solution/services';
import { filterTreeData, scrollIntoTop } from './utils';
import { INodeItem, ITreeItem } from './type';
import isMobileFunc from '@/help-fe-common/utils/global/isMobile';
import styles from './index.module.scss';
import paginationStyle from './pagination.module.scss';
import { isServer } from '@/help-fe-common/utils/node/getContext';

interface IProps {
  data: {
    solutionList: INodeItem[];
    menuTree: ITreeItem;
    hotSearchKeywords: string[];
  };
}

const AllSolution: FunctionComponent<IProps> = ({ data }) => {
  const { menuTree, hotSearchKeywords, solutionList: allSolutionList } = data;

  const intl = useIntl();
  const childRef = useRef<any>();
  const [searchValue, setSearchValue] = useState('');
  const [page, setPage] = useState(1);
  const [shownSolutionList, setShownSolutionList] = useState<INodeItem[]>();
  const [showMobileFilterDialog, setShowMobileFilterDialog] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string[]>([]);
  const [isFreeTrialOnly, setIsFreeTrialOnly] = useState(false);
  const [isMobile, setIsMobile] = useState(isMobileFunc());
  const pageSize = isMobile ? 2 : 12;

  const { data: tokenData, request: getSolutionFreeToken } = useRequest(services.getSolutionFreeToken, { manual: true });

  useEffect(() => {
    if (tokenData?.length > 0 && allSolutionList?.length > 0) {
      allSolutionList.forEach((item) => {
        const freeItem = tokenData.find((tokenItem) => tokenItem?.solutionId === item.id);
        if (!freeItem) return;
        item.freeInfo = {
          isFree: true,
          freeToken: freeItem?.hourlyExperimentCost,
          deployUrl: freeItem?.deployUrl,
        };
      });
    }
    setShownSolutionList([...allSolutionList]);
  }, [tokenData, allSolutionList]);

  useEffect(() => {
    // 获取全部解决方案免费试用信息
    getSolutionFreeToken({});

    window.addEventListener('resize', () => {
      setIsMobile((prev) => {
        const newValue = isMobileFunc();

        if (prev !== newValue) {
          setPage(1);
        }

        return newValue;
      });
    });
  }, []);

  /**
   * 处理需要展示的产品列表
   * @param {Array} selectedItem 选中的节点列表
   */
  const handleShownSolutionList = useCallback((selectedItem) => {
    setSelectedCategory(selectedItem);
    const tmpSelectedItem: string[] = [];
    for (const selectedItemKey in selectedItem) {
      const codes = selectedItem[selectedItemKey].split('/');
      tmpSelectedItem.push(codes[codes.length - 1]);
    }

    let tmpList = allSolutionList;
    if (selectedItem?.length) {
      tmpList = allSolutionList.filter((item) => {
        const { tech_solution = {} } = item?.categoryTagMap || {};
        if (!isEmpty(tech_solution)) {
          for (const code in tech_solution) {
            if (tmpSelectedItem.includes(code)) {
              return true;
            }
          }
        }
        return false;
      });
    }

    // 应用免费试用筛选
    if (isFreeTrialOnly) {
      tmpList = tmpList.filter((item) => item.freeInfo?.isFree);
    }

    setShownSolutionList(tmpList);

    setSearchValue('');
    setPage(1);
  }, [allSolutionList, isFreeTrialOnly]);

  /**
   * 处理免费试用筛选
   * @param {boolean} isFreeTrialOnly 是否只显示免费试用
   */
  const handleFreeTrialFilterChange = useCallback((isFreeTrialOnly: boolean) => {
    setIsFreeTrialOnly(isFreeTrialOnly);

    let tmpList = allSolutionList;

    // 应用分类筛选
    if (selectedCategory?.length) {
      const tmpSelectedItem: string[] = [];
      for (const selectedItemKey in selectedCategory) {
        const codes = selectedCategory[selectedItemKey].split('/');
        tmpSelectedItem.push(codes[codes.length - 1]);
      }

      tmpList = allSolutionList.filter((item) => {
        const { tech_solution = {} } = item?.categoryTagMap || {};
        if (!isEmpty(tech_solution)) {
          for (const code in tech_solution) {
            if (tmpSelectedItem.includes(code)) {
              return true;
            }
          }
        }
        return false;
      });
    }

    // 应用免费试用筛选
    if (isFreeTrialOnly) {
      tmpList = tmpList.filter((item) => item.freeInfo?.isFree);
    }

    setShownSolutionList(tmpList);
    setSearchValue('');
    setPage(1);
  }, [allSolutionList, selectedCategory]);

  /**
   * 经过筛选的所有数据
   */
  const filterDataRes = useMemo(() => {
    if (shownSolutionList) {
      return filterTreeData(shownSolutionList, searchValue);
    } else {
      return [];
    }
  }, [shownSolutionList, searchValue]);

  /**
   * 处理分页后的展示数据
   */
  const dataList = useMemo(() => {
    const currentData = filterDataRes.slice((page - 1) * pageSize, page * pageSize);
    return currentData;
  }, [page, pageSize, filterDataRes]);

  // 筛选时页面滚动到可视区域
  useEffect(() => {
    selectedCategory?.length > 0 && scrollIntoTop();
  }, [selectedCategory]);

  useEffect(() => {
    setPage(1);
  }, [searchValue]);

  const renderSsr = () => {
    const solutionList = isServer() ? filterTreeData(allSolutionList, searchValue)?.slice((page - 1) * pageSize, page * pageSize) : dataList;
    return (
      renderSolutionList(solutionList)
    );
  };

  const renderSolutionList = (solutionList) => {
    return solutionList?.length > 0 ? (
      <>
        <div className={styles.solutionCardList}>
          {solutionList?.map((item) => (
            <SolutionCard cardItemData={item} setSearchValue={setSearchValue} scrollIntoTop={scrollIntoTop} />
          ))}
        </div>
        <Pagination
          className={`${styles.pagination} ${paginationStyle.pagination}`}
          total={filterDataRes.length}
          current={page}
          pageSize={pageSize}
          onChange={(val) => { setPage(val); scrollIntoTop(); }}
          shape="arrow-only"
          pageShowCount={isMobile ? 99 : 5}
          totalRender={(total) => {
            return `${page} / ${Math.ceil(total / pageSize)}`;
          }}
        />
      </>
    ) : (
      <NoResult tip={intl.formatMessage({ id: 'help.solution.solutionList.noData' })} />
    );
  };

  return (
    <div className={classnames('col-extend-left', 'col-extend-right', styles.container)} id="solution-all-container">
      <Head title={intl.formatMessage({ id: 'help.solution.allSolution' })} />
      <Search searchValue={searchValue} setSearchValue={setSearchValue} hotSearchKeywords={hotSearchKeywords} />
      <div className={styles.contentContainer}>
        <div className={styles.filterContainer}>
          <FilterTree
            data={menuTree}
            onSelectedItemsChange={handleShownSolutionList}
            onFreeTrialFilterChange={handleFreeTrialFilterChange}
            onRef={childRef}
          />
        </div>
        <div className={styles.mobileFilter}>
          <div
            className={`${styles.mobileFilterGuide} col-extend-left col-extend-right`}
            onClick={() => setShowMobileFilterDialog(true)}
          >
            <span><FormattedMessage id="help.solution.filter" /></span>
            <i className={'help-iconfont help-icon-filter'} />
          </div>
          <Drawer
            placement="right"
            visible={showMobileFilterDialog}
            onClose={() => setShowMobileFilterDialog(false)}
            width="100%"
            style={{ maxWidth: '100%' }}
            bodyStyle={{ padding: 0 }}
            headerStyle={{ display: 'none' }}
          >
            <MobileFilterDialog
              allData={menuTree}
              selectedItems={selectedCategory}
              isFreeTrialOnly={isFreeTrialOnly}
              onSelectedItemsChange={handleShownSolutionList}
              onFreeTrialFilterChange={handleFreeTrialFilterChange}
              onHandleDialog={setShowMobileFilterDialog}
            />
          </Drawer>
        </div>
        <div className={classnames('col-extend-left', 'col-extend-right', styles.mainContainer)}>
          {renderSsr()}
        </div>
      </div>
    </div>
  );
};

export default AllSolution;
