.filterDialog {
  .dialogTitle {
    height: 42px;
    line-height: 42px;
    padding: 0 18px;
    text-align: center;
    font-weight: 500;
    font-size: 17px;
    box-shadow: inset 0 -1px 0 0 #EDEDEC;

    i {
      font-size: 12px;
      font-weight: 600;
      float: left;
      cursor: pointer;
    }
  }

  .mainContainer {
    display: flex;
    height: calc(100vh - 102px);
  }

  .filterOptions {
    flex: 1;
    background-color: #fff;
    overflow: scroll;

    &::-webkit-scrollbar {
      width: 5px;
      height: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #dfdfdf;
      border-radius: 5px;
    }
  }

  .treeContainer {
    width: 40.5%;
    background-color: #f6f6f6;
    overflow: scroll;

    &::-webkit-scrollbar {
      width: 5px;
      height: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #dfdfdf;
      border-radius: 5px;
    }

    .liNode {
      height: 40px;
      line-height: 40px;
      padding: 0 10px;
      font-size: 14px;
      text-align: left;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-left: 4px solid transparent;

      .label {
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
      }

      >i {
        font-size: 16px;
        color: #979797;
        opacity: 0;
      }

      &:hover {
        background-color: #fff;
      }

      &.active {
        background-color: #fff;
        border-left-color: #1366ec;

        >i {
          opacity: 1;
        }
      }
    }

    .liNodeContainer {
      height: 40px;
      line-height: 40px;
      padding-right: 10px;
      font-size: 14px;
      text-align: left;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .selected {
        width: 100%;
        height: 18px;
        line-height: 18px;
        padding-left: 6px;
        border-left: 4px solid transparent;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        display: flex;
        justify-content: space-between;

        >i {
          font-size: 16px;
          color: #979797;
          opacity: 0;
        }
      }

      &:hover {
        background-color: #fff;
      }
    }

    .liNodeSelected {
      background-color: #fff;

      .selected {
        border-left-color: #1366ec;

        >i {
          opacity: 1;
        }
      }
    }
  }

  .childBox {
    flex: 1;
    padding: 0 15px;

    .liNodeContainer {
      line-height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .label {
        font-weight: 500;
        font-size: 13px;
        color: #333333;
      }

      >i {
        height: 16px;
        width: 16px;
        font-size: 12px;
        color: #999;
      }

      .checkBoxButton {
        font-size: 14px;
        color: #181818;
        letter-spacing: 1px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;

        :global {
          .next-checkbox {
            margin-top: 0;
            vertical-align: middle;
          }

          .next-checkbox-label {
            line-height: 30px;
            vertical-align: middle;
          }
        }
      }
    }
  }
}

.dialogFooter {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 60px;
  padding: 12px;
  text-align: right;
  background-color: #fff;

  .textButton {
    border: none;
    background-color: #fff;
    font-weight: 500;
    font-size: 14px;
    color: #1366ec;
    text-align: center;
    line-height: 36px;
    margin-right: 12px;
  }

  .button {
    height: 36px;
    line-height: 36px;
    width: 128px;
    border: none;
    background-color: #1366ec;
    font-weight: 500;
    font-size: 14px;
    color: #fff;
    text-align: center;
  }
}