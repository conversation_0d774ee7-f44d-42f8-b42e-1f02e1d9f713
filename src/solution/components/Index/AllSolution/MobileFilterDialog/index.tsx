import React, { useState, FunctionComponent, useEffect } from 'react';
import { Checkbox } from '@alifd/next';
import { FormattedMessage } from 'react-intl';
import classNames from 'classnames';
import { ITreeItem } from '../type';
import styles from './index.module.scss';
import { map } from 'lodash';
import { SolutionTreeTypeEnum } from '@/solution/constants';

interface IProps {
  allData: ITreeItem;
  selectedItems: string[];
  isFreeTrialOnly: boolean;
  onHandleDialog: (visible: boolean) => void;
  onSelectedItemsChange: (selectedItems: string[]) => void;
  onFreeTrialFilterChange: (isFreeTrialOnly: boolean) => void;
}

const FilterDialog: FunctionComponent<IProps> = ({
  allData,
  selectedItems: propsSelectedItems,
  isFreeTrialOnly: propsIsFreeTrialOnly,
  onHandleDialog,
  onSelectedItemsChange,
  onFreeTrialFilterChange,
}) => {
  const [selectedItem, setSelectedItem] = useState([] as string[]);
  const [expandedItemId, setExpandedItemId] = useState('');
  const [isFreeTrialOnly, setIsFreeTrialOnly] = useState(false);
  const [currentCategory, setCurrentCategory] = useState(''); // 当前选中的分类

  useEffect(() => {
    setSelectedItem(propsSelectedItems);
  }, [propsSelectedItems]);

  useEffect(() => {
    setIsFreeTrialOnly(propsIsFreeTrialOnly);
  }, [propsIsFreeTrialOnly]);

  const onClearFilter = () => {
    setSelectedItem([]);
    setIsFreeTrialOnly(false);
  };

  const onConfirm = () => {
    onSelectedItemsChange(selectedItem);
    onFreeTrialFilterChange(isFreeTrialOnly);
    onHandleDialog(false);
  };

  /**
   * 处理产品节点显示逻辑
   * @param item 一级产品分类节点
   */
  const handleExpandClick = (item) => {
    setExpandedItemId(item?.code);
  };

  /**
   * 处理产品节点选中逻辑
   * @param item 产品分类节点
   */
  const handleSelectClick = (item) => {
    const itemId = item?.code;
    if (selectedItem.includes(itemId)) {
      // 处理节点取消选中
      setSelectedItem(selectedItem.filter((i) => i !== itemId && !String(i).startsWith(`${itemId}/`)));
    } else {
      const selectedChildren = [] as string[];
      map(item.children, (child) => {
        selectedChildren.push(`${itemId}/${child.code}`);
      });
      setSelectedItem([itemId, ...selectedChildren, ...selectedItem]);
    }
  };

  /**
   * 判断一级节点checkbox是否全部选中
   * @param treeItem 节点数据
   * @returns
   */
  const showAllSelected = (treeItem) => {
    const len = selectedItem.filter((i) => String(i).startsWith(`${treeItem?.code}/`))?.length || 0;
    return len === (treeItem?.children?.length || 0);
  };

  /**
   * 生成二级节点，并处理选中逻辑
   * @param treeData 产品列表树
   * @param parentId
   * @returns
   */
  const renderChildren = (treeData: ITreeItem[], parentId) => {
    const treeDataItem = treeData.find((item) => item.code === parentId);
    const children = treeDataItem?.children || [];
    const code = treeDataItem?.code as any;

    return (
      <ul className={styles.childBox}>
        <li>
          <div className={styles.liNodeContainer}>
            <span className={styles.label}>全部</span>
            <Checkbox
              className={styles.checkBoxButton}
              checked={selectedItem.includes(code) && showAllSelected(treeDataItem)}
              onChange={() => handleSelectClick(treeDataItem)}
            />
          </div>
        </li>
        {children.map((child, index) => (
          child?.type === SolutionTreeTypeEnum.CATEGORY &&
          <li key={index}>
            <div className={styles.liNodeContainer}>
              <span className={styles.label}>
                {child.name}
              </span>
              <Checkbox
                className={styles.checkBoxButton}
                defaultChecked={selectedItem.includes(`${parentId}/${child.code}`)}
                checked={selectedItem.includes(`${parentId}/${child.code}`)}
                onChange={() => handleSelectClick({ code: `${parentId}/${child.code}`, children: [] })}
              />
            </div>
          </li>
        ))}
      </ul>
    );
  };


  useEffect(() => {
    // 默认选中免费试用分类
    if (!expandedItemId) {
      setExpandedItemId('freeTrialFilter');
      setCurrentCategory('freeTrialFilter');
    }
  }, [allData]);

  // 渲染左侧分类列表
  const renderCategoryList = () => {
    const categories = [
      { code: 'freeTrialFilter', name: '免费试用' },
      ...(allData?.children || []),
    ];

    return (
      <div className={styles.treeContainer}>
        {categories.map((category) => (
          <div
            key={category.code}
            className={classNames(styles.liNode, {
              [styles.active]: expandedItemId === category.code,
            })}
            onClick={() => {
              setExpandedItemId(category.code);
              setCurrentCategory(category.code);
            }}
          >
            <span className={styles.label}>
              {category.code === 'freeTrialFilter' ? (
                <FormattedMessage id="help.solution.freeTrialFilter" />
              ) : (
                category.name
              )}
            </span>
            <i className="help-iconfont help-icon-small-arrow" />
          </div>
        ))}
      </div>
    );
  };

  // 渲染右侧筛选项
  const renderFilterOptions = () => {
    if (expandedItemId === 'freeTrialFilter') {
      // 免费试用筛选选项
      return (
        <div className={styles.childBox}>
          <div className={styles.liNodeContainer}>
            <span className={styles.label}>
              <FormattedMessage id="help.solution.freeTrialFilterOption" />
            </span>
            <Checkbox
              className={styles.checkBoxButton}
              checked={isFreeTrialOnly}
              onChange={(checked) => setIsFreeTrialOnly(checked)}
            />
          </div>
        </div>
      );
    } else if (expandedItemId) {
      // 分类筛选选项
      return renderChildren(allData?.children as ITreeItem[], expandedItemId);
    }
    return null;
  };

  return (
    <div className={styles.filterDialog}>
      <div className={styles.dialogTitle}>
        <i className="help-iconfont help-icon-quxiao" onClick={() => onHandleDialog(false)} />
        <FormattedMessage id="help.solution.filter" />
      </div>
      <div className={styles.mainContainer}>
        {/* 左侧分类列表 */}
        {renderCategoryList()}

        {/* 右侧筛选选项 */}
        <div className={styles.filterOptions}>
          {renderFilterOptions()}
        </div>
      </div>
      <div className={styles.dialogFooter}>
        <button className={styles.textButton} onClick={onClearFilter}>
          <FormattedMessage id="help.index.productList.clear" />
        </button>
        <button className={styles.button} onClick={onConfirm}>
          <FormattedMessage id="help.index.productList.confirm" />
        </button>
      </div>
    </div>
  );
};

export default FilterDialog;
