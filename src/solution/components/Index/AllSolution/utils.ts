import { isEmpty, uniqBy } from 'lodash';
import { SolutionTreeTypeEnum } from '@/solution/constants';

/**
 * 获取所有解决方案列表
 * @param treeData
 * @returns
 */
const getAllSolutionList = (treeData: any) => {
  const res: any = [];

  const recurse = (currentNode, category) => {
    if (currentNode.children && currentNode?.type === SolutionTreeTypeEnum.CATEGORY) {
      // 递归子项
      for (const child of currentNode.children) {
        let arr = category;
        if (child?.type === SolutionTreeTypeEnum.CATEGORY) {
          arr = [...category, { code: child?.code, name: child?.name }];
        }
        recurse(child, arr); // 继续递归子项的子项
      }
    } else if (currentNode?.type === SolutionTreeTypeEnum.DATA) {
      res.push({ category, ...currentNode });
    }
  };

  recurse(treeData, []);
  return res;
};

/**
 * 过滤解决方案列表
 * @param solutionList
 * @param searchValue
 * @returns
 */
const filterTreeData = (solutionList, searchValue) => {
  return uniqBy(solutionList.filter((item) => {
    const handleStr = (str: string | undefined) => {
      if (!str) {
        return '';
      }

      return str.toLowerCase().replace(/ /g, '');
    };

    const kwd = handleStr(searchValue);

    if (!kwd) {
      return true;
    }

    const batchMatch = (sourceArr, keywords) => {
      if (isEmpty(sourceArr)) {
        return false;
      }

      for (const key in sourceArr) {
        if (handleStr(sourceArr[key]).includes(keywords) || handleStr(key).includes(keywords)) {
          return true;
        }
      }

      return false;
    };

    const { tech_solution, product_name, tech_solution_scene } = item?.categoryTagMap || {};
    const matchName = (keyword) => handleStr(item?.name).includes(keyword);
    const matchDesc = (keyword) => handleStr(item?.shortDesc).includes(keyword);
    const matchTechSolution = (keyword) => batchMatch(tech_solution, keyword);
    const matchProductName = (keyword) => batchMatch(product_name, keyword);
    const matchTechSolutionScene = (keyword) => batchMatch(tech_solution_scene, keyword);

    return matchName(kwd)
      || matchDesc(kwd)
      || matchTechSolution(kwd)
      || matchProductName(kwd)
      || matchTechSolutionScene(kwd);
  }), 'code');
};

/**
   * 滚动到顶部
   */
const scrollIntoTop = () => {
  const containerDOM = document.querySelector('#solution-all-container') as HTMLElement;
  document.documentElement.scrollTo({ left: 0, top: containerDOM.offsetTop, behavior: 'instant' });
};
export { getAllSolutionList, filterTreeData, scrollIntoTop };
