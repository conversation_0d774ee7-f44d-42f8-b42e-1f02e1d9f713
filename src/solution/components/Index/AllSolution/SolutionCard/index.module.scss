@mixin text-line($line-number: 1) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line-number;
}

.solutionCard {
  height: 350px;
  width: 100%;
  padding: 24px;
  background: #F9FBFF;
  border: 1px solid #D4D6DB;
  cursor: pointer;
  position: relative;

  &:hover {
    border: 1px solid #A2CAFF;
    background: url('https://img.alicdn.com/imgextra/i2/O1CN01gL5rna1oXDyUaWaTB_!!6000000005234-0-tps-740-700.jpg') bottom right / cover no-repeat;

    @keyframes card-show-more-text {
      from {
        max-width: 0;
      }

      to {
        max-width: 100%;
      }
    }

    .content {
      .cardBottom {
        .cardBottomLeft {
          span {
            animation: card-show-more-text 0.5s ease-in-out;
            display: block;
            white-space: nowrap;
            overflow: hidden;
          }
        }
      }
    }
  }

  .head {
    margin-bottom: 24px;

    .title {
      font-size: 18px;
      font-weight: 600;
      color: #181818;
      line-height: 25px;
      margin-bottom: 16px;
      padding-bottom: 24px;
      border-bottom: 1px solid #e9e9e9;
      display: block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .content {
    .desc {
      font-size: 14px;
      color: #666;
      line-height: 22px;
      margin-bottom: 17px;
      height: 66px;
      @include text-line(3);
    }

    .product {
      height: 14px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      overflow: hidden;
      margin-bottom: 16px;

      >*:nth-child(n+2) {
        margin-left: 12px;
      }

      .productItem {
        color: #181818;
        font-size: 12px;
        line-height: 14px;
        height: 100%;
        display: flex;
        align-items: center;
        max-width: 100%;

        span {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          display: block;
        }

        i {
          font-size: 14px;
          font-style: normal;
          color: #81848F;
          margin-right: 4px;
        }
      }
    }

    .tags {
      padding: 18px 0 10px;
      border-top: 1px solid #e9e9e9;
      display: flex;
      flex-wrap: wrap;

      span {
        margin: 0 8px 8px 0;
        padding: 0 8px;
        display: flex;
        height: 20px;
        align-items: center;
        border-radius: 4px;
        border: 1px solid #D8D8D8;
        font-size: 12px;
        color: #999999;

        &:hover {
          color: #1366ec;
          border-color: #1366ec;
        }
      }
    }

    .cardBottom {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 24px;
      font-size: 12px;

      .cardBottomLeft {
        display: flex;
        align-items: center;
        font-size: 14px;

        span {
          color: #1366EC;
          margin-right: 14px;
          display: none;
        }

        i {
          color: #1366EC;
        }
      }

      .cardBottomRight {
        .freeTag {
          width: 80px;
          height: 20px;
          display: flex;

          .logo {
            width: 20px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            background: #FF6A00;
            color: #fff;
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
          }

          .text {
            width: 60px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            background: #FFE9DB;
            color: #FF6A00;
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
          }
        }
      }
    }
  }
}

.freeTip {

  .token {
    color: #FF6A00;
    font-weight: 600;
  }

  .logo {
    height: 24px;
    line-height: 24px;
    font-size: 14px;
    margin-bottom: 4px;
  }

  .linkContainer {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .link {
    color: #1366EC;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    text-decoration: none;

    &:hover {
      color: #1154C0;
    }

    span {
      display: inline-block;
      width: 12px;
      height: 12px;
      line-height: 10px;
      text-align: center;
      background-color: #1366EC;
      color: #fff;
      font-weight: normal;
      border-radius: 100%;
      margin-left: 8px;

      i {
        font-size: 10px;
      }
    }
  }
}

// mobile端
@media only screen and (max-width: 1055px) {
  .solutionCard {
    width: 100%;
    padding: 24px 16px;
  }
}