.balloon {
  background: #fff;
  border: 1px solid #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  opacity: 0;
  transform: scale(0.8);
  animation: balloonFadeIn 0.2s ease-out forwards;
  pointer-events: auto;
}

.balloonContent {
  position: relative;
  z-index: 1;
}

.balloonArrow {
  position: absolute;
  width: 0;
  height: 0;
  border: 6px solid transparent;
}

// 箭头位置 - 上方
.arrow-t {
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  border-top-color: #fff;
  z-index: 2;

  &::before {
    content: '';
    position: absolute;
    top: -7px;
    left: -6px;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-top-color: #fff;
    z-index: 1;
  }
}

// 箭头位置 - 下方
.arrow-b {
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  border-bottom-color: #fff;
  z-index: 2;

  &::before {
    content: '';
    position: absolute;
    bottom: -7px;
    left: -6px;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-bottom-color: #fff;
    z-index: 1;
  }
}

// 箭头位置 - 左侧
.arrow-l {
  right: -12px;
  top: 50%;
  transform: translateY(-50%);
  border-left-color: #fff;
  z-index: 2;

  &::before {
    content: '';
    position: absolute;
    left: -7px;
    top: -6px;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-left-color: #fff;
    z-index: 1;
  }
}

// 箭头位置 - 右侧
.arrow-r {
  left: -12px;
  top: 50%;
  transform: translateY(-50%);
  border-right-color: #fff;
  z-index: 2;

  &::before {
    content: '';
    position: absolute;
    right: -7px;
    top: -6px;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-right-color: #fff;
    z-index: 1;
  }
}

// 箭头位置 - 左上
.arrow-tl {
  bottom: -12px;
  left: 16px;
  border-top-color: #fff;
  z-index: 2;

  &::before {
    content: '';
    position: absolute;
    top: -7px;
    left: -6px;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-top-color: #fff;
    z-index: 1;
  }
}

// 箭头位置 - 右上
.arrow-tr {
  bottom: -12px;
  right: 16px;
  border-top-color: #fff;
  z-index: 2;

  &::before {
    content: '';
    position: absolute;
    top: -7px;
    left: -6px;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-top-color: #fff;
    z-index: 1;
  }
}

// 箭头位置 - 左下
.arrow-bl {
  top: -12px;
  left: 16px;
  border-bottom-color: #fff;
  z-index: 2;

  &::before {
    content: '';
    position: absolute;
    bottom: -7px;
    left: -6px;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-bottom-color: #fff;
    z-index: 1;
  }
}

// 箭头位置 - 右下
.arrow-br {
  top: -12px;
  right: 16px;
  border-bottom-color: #fff;
  z-index: 2;

  &::before {
    content: '';
    position: absolute;
    bottom: -7px;
    left: -6px;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-bottom-color: #fff;
    z-index: 1;
  }
}

@keyframes balloonFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}