.balloon {
  background: #fff;
  border: 1px solid #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  opacity: 0;
  transform: scale(0.8);
  animation: balloonFadeIn 200ms ease-out forwards;
  pointer-events: auto;
}

.balloonContent {
  position: relative;
  z-index: 1;
}

// 箭头公共样式
.arrow-base {
  position: absolute;
  width: 0;
  height: 0;
  border: 6px solid transparent;
  z-index: 2;

  &::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    left: -6px;
    z-index: 1;
  }
}

.balloonArrow {
  @extend .arrow-base;
}

// 中心对齐箭头公共样式
.arrow-center {
  @extend .arrow-base;
  left: 50%;
}

// 角落箭头公共样式
.arrow-corner {
  @extend .arrow-base;
}

// 上方箭头组
.arrow-t {
  @extend .arrow-center;
  bottom: -12px;
  transform: translateX(-50%);
  border-top-color: #fff;

  &::before {
    top: -7px;
    border-top-color: #fff;
  }
}

.arrow-tl {
  @extend .arrow-corner;
  bottom: -12px;
  left: 16px;
  border-top-color: #fff;

  &::before {
    top: -7px;
    border-top-color: #fff;
  }
}

.arrow-tr {
  @extend .arrow-corner;
  bottom: -12px;
  right: 16px;
  border-top-color: #fff;

  &::before {
    top: -7px;
    border-top-color: #fff;
  }
}

// 下方箭头组
.arrow-b {
  @extend .arrow-center;
  top: -12px;
  transform: translateX(-50%);
  border-bottom-color: #fff;

  &::before {
    bottom: -7px;
    border-bottom-color: #fff;
  }
}

.arrow-bl {
  @extend .arrow-corner;
  top: -12px;
  left: 16px;
  border-bottom-color: #fff;

  &::before {
    bottom: -7px;
    border-bottom-color: #fff;
  }
}

.arrow-br {
  @extend .arrow-corner;
  top: -12px;
  right: 16px;
  border-bottom-color: #fff;

  &::before {
    bottom: -7px;
    border-bottom-color: #fff;
  }
}

// 左右箭头组
.arrow-l {
  @extend .arrow-base;
  right: -12px;
  top: 50%;
  transform: translateY(-50%);
  border-left-color: #fff;

  &::before {
    left: -7px;
    top: -6px;
    border-left-color: #fff;
  }
}

.arrow-r {
  @extend .arrow-base;
  left: -12px;
  top: 50%;
  transform: translateY(-50%);
  border-right-color: #fff;

  &::before {
    right: -7px;
    left: auto;
    top: -6px;
    border-right-color: #fff;
  }
}

@keyframes balloonFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}