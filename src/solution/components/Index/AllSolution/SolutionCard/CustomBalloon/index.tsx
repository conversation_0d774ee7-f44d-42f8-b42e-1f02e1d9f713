import React, { useState, useRef, useEffect, ReactNode, useCallback } from 'react';
import { createPortal } from 'react-dom';
import styles from './index.module.scss';

interface CustomBalloonProps {
  children: ReactNode;
  trigger: ReactNode;
  triggerType?: 'hover' | 'click' | Array<'hover' | 'click'>;
  align?: 't' | 'b' | 'l' | 'r' | 'tl' | 'tr' | 'bl' | 'br';
  offset?: [number, number];
  style?: React.CSSProperties;
  closable?: boolean;
  visible?: boolean;
  onVisibleChange?: (visible: boolean) => void;
}

const CustomBalloon: React.FC<CustomBalloonProps> = ({
  children,
  trigger,
  triggerType = 'hover',
  align = 't',
  offset = [0, 8],
  style = {},
  closable = true, // eslint-disable-line @typescript-eslint/no-unused-vars
  visible: controlledVisible,
  onVisibleChange,
}) => {
  const [internalVisible, setInternalVisible] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const triggerRef = useRef<HTMLDivElement>(null);
  const balloonRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const isControlled = controlledVisible !== undefined;
  const visible = isControlled ? controlledVisible : internalVisible;

  const triggerTypes = Array.isArray(triggerType) ? triggerType : [triggerType];

  const updatePosition = useCallback(() => {
    if (!triggerRef.current || !balloonRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const balloonRect = balloonRef.current.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let top = 0;
    let left = 0;

    // 根据align计算初始位置
    switch (align) {
      case 't':
        top = triggerRect.top + scrollTop - balloonRect.height - offset[1];
        left = triggerRect.left + scrollLeft + (triggerRect.width - balloonRect.width) / 2 + offset[0];
        break;
      case 'b':
        top = triggerRect.bottom + scrollTop + offset[1];
        left = triggerRect.left + scrollLeft + (triggerRect.width - balloonRect.width) / 2 + offset[0];
        break;
      case 'l':
        top = triggerRect.top + scrollTop + (triggerRect.height - balloonRect.height) / 2 + offset[1];
        left = triggerRect.left + scrollLeft - balloonRect.width - offset[0];
        break;
      case 'r':
        top = triggerRect.top + scrollTop + (triggerRect.height - balloonRect.height) / 2 + offset[1];
        left = triggerRect.right + scrollLeft + offset[0];
        break;
      case 'tl':
        top = triggerRect.top + scrollTop - balloonRect.height - offset[1];
        left = triggerRect.left + scrollLeft + offset[0];
        break;
      case 'tr':
        top = triggerRect.top + scrollTop - balloonRect.height - offset[1];
        left = triggerRect.right + scrollLeft - balloonRect.width + offset[0];
        break;
      case 'bl':
        top = triggerRect.bottom + scrollTop + offset[1];
        left = triggerRect.left + scrollLeft + offset[0];
        break;
      case 'br':
        top = triggerRect.bottom + scrollTop + offset[1];
        left = triggerRect.right + scrollLeft - balloonRect.width + offset[0];
        break;
      default:
        break;
    }

    // 智能边界检测和位置调整
    const padding = 8;

    // 水平方向边界检测
    if (left < scrollLeft + padding) {
      left = scrollLeft + padding;
      // 如果是右侧对齐，调整为左侧对齐
      if (align === 'r') {
        left = triggerRect.right + scrollLeft + offset[0];
        if (left + balloonRect.width > scrollLeft + viewportWidth - padding) {
          left = scrollLeft + viewportWidth - balloonRect.width - padding;
        }
      }
    } else if (left + balloonRect.width > scrollLeft + viewportWidth - padding) {
      left = scrollLeft + viewportWidth - balloonRect.width - padding;
      // 如果是左侧对齐，调整为右侧对齐
      if (align === 'l') {
        left = triggerRect.left + scrollLeft - balloonRect.width - offset[0];
        if (left < scrollLeft + padding) {
          left = scrollLeft + padding;
        }
      }
    }

    // 垂直方向边界检测
    if (top < scrollTop + padding) {
      top = scrollTop + padding;
      // 如果是上方对齐，调整为下方对齐
      if (align === 't' || align === 'tl' || align === 'tr') {
        top = triggerRect.bottom + scrollTop + offset[1];
      }
    } else if (top + balloonRect.height > scrollTop + viewportHeight - padding) {
      top = scrollTop + viewportHeight - balloonRect.height - padding;
      // 如果是下方对齐，调整为上方对齐
      if (align === 'b' || align === 'bl' || align === 'br') {
        top = triggerRect.top + scrollTop - balloonRect.height - offset[1];
      }
    }

    setPosition({ top, left });
  }, [align, offset]);

  const showBalloon = () => {
    if (!isControlled) {
      setInternalVisible(true);
    }
    onVisibleChange?.(true);
  };

  const hideBalloon = () => {
    if (!isControlled) {
      setInternalVisible(false);
    }
    onVisibleChange?.(false);
  };

  const handleMouseEnter = () => {
    if (triggerTypes.includes('hover')) {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      showBalloon();
    }
  };

  const handleMouseLeave = () => {
    if (triggerTypes.includes('hover')) {
      timeoutRef.current = setTimeout(() => {
        hideBalloon();
      }, 100);
    }
  };

  const handleClick = () => {
    if (triggerTypes.includes('click')) {
      if (visible) {
        hideBalloon();
      } else {
        showBalloon();
      }
    }
  };

  const handleBalloonMouseEnter = () => {
    if (triggerTypes.includes('hover') && timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  const handleBalloonMouseLeave = () => {
    if (triggerTypes.includes('hover')) {
      timeoutRef.current = setTimeout(() => {
        hideBalloon();
      }, 100);
    }
  };

  // 处理Balloon内部链接点击，隐藏组件
  const handleBalloonClick = (event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    // 检查点击的是否是链接元素
    if (target.tagName === 'A' || target.closest('a')) {
      hideBalloon();
    }
  };

  useEffect(() => {
    if (visible) {
      // 延迟计算位置，确保DOM已渲染
      const timer = setTimeout(updatePosition, 0);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [visible, align, offset, updatePosition]);

  useEffect(() => {
    if (visible) {
      const handleResize = () => updatePosition();
      const handleScroll = () => updatePosition();

      window.addEventListener('resize', handleResize);
      window.addEventListener('scroll', handleScroll);

      return () => {
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('scroll', handleScroll);
      };
    }
    return undefined;
  }, [visible, updatePosition]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const balloonContent = visible && (
    <div
      ref={balloonRef}
      className={styles.balloon}
      style={{
        position: 'absolute',
        top: position.top,
        left: position.left,
        zIndex: 1000,
        ...style,
      }}
      onMouseEnter={handleBalloonMouseEnter}
      onMouseLeave={handleBalloonMouseLeave}
      onClick={handleBalloonClick}
    >
      <div className={styles.balloonContent}>
        {children}
      </div>
      <div className={`${styles.balloonArrow} ${styles[`arrow-${align}`]}`} />
    </div>
  );

  return (
    <>
      <div
        ref={triggerRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        style={{ display: 'inline-block' }}
      >
        {trigger}
      </div>
      {typeof document !== 'undefined' && createPortal(balloonContent, document.body)}
    </>
  );
};

export default CustomBalloon;
