import React, { useState, useRef, useEffect, ReactNode, useCallback } from 'react';
import { createPortal } from 'react-dom';
import styles from './index.module.scss';

interface CustomBalloonProps {
  children: ReactNode;
  trigger: ReactNode;
  triggerType?: 'hover' | 'click' | Array<'hover' | 'click'>;
  align?: 't' | 'b' | 'l' | 'r' | 'tl' | 'tr' | 'bl' | 'br';
  offset?: [number, number];
  style?: React.CSSProperties;
  closable?: boolean;
  visible?: boolean;
  onVisibleChange?: (visible: boolean) => void;
}

const CustomBalloon: React.FC<CustomBalloonProps> = ({
  children,
  trigger,
  triggerType = 'hover',
  align = 't',
  offset = [0, 8],
  style = {},
  closable = true, // eslint-disable-line @typescript-eslint/no-unused-vars
  visible: controlledVisible,
  onVisibleChange,
}) => {
  const [internalVisible, setInternalVisible] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const [actualAlign, setActualAlign] = useState(align);
  const triggerRef = useRef<HTMLDivElement>(null);
  const balloonRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const isControlled = controlledVisible !== undefined;
  const visible = isControlled ? controlledVisible : internalVisible;

  const triggerTypes = Array.isArray(triggerType) ? triggerType : [triggerType];

  const updatePosition = useCallback(() => {
    if (!triggerRef.current || !balloonRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const balloonRect = balloonRef.current.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let top = 0;
    let left = 0;
    let currentAlign = align;

    // 根据align计算初始位置
    switch (align) {
      case 't':
        top = triggerRect.top + scrollTop - balloonRect.height - offset[1];
        left = triggerRect.left + scrollLeft + (triggerRect.width - balloonRect.width) / 2 + offset[0];
        break;
      case 'b':
        top = triggerRect.bottom + scrollTop + offset[1];
        left = triggerRect.left + scrollLeft + (triggerRect.width - balloonRect.width) / 2 + offset[0];
        break;
      case 'l':
        top = triggerRect.top + scrollTop + (triggerRect.height - balloonRect.height) / 2 + offset[1];
        left = triggerRect.left + scrollLeft - balloonRect.width - offset[0];
        break;
      case 'r':
        top = triggerRect.top + scrollTop + (triggerRect.height - balloonRect.height) / 2 + offset[1];
        left = triggerRect.right + scrollLeft + offset[0];
        break;
      case 'tl':
        top = triggerRect.top + scrollTop - balloonRect.height - offset[1];
        left = triggerRect.left + scrollLeft + offset[0];
        break;
      case 'tr':
        top = triggerRect.top + scrollTop - balloonRect.height - offset[1];
        left = triggerRect.right + scrollLeft - balloonRect.width + offset[0];
        break;
      case 'bl':
        top = triggerRect.bottom + scrollTop + offset[1];
        left = triggerRect.left + scrollLeft + offset[0];
        break;
      case 'br':
        top = triggerRect.bottom + scrollTop + offset[1];
        left = triggerRect.right + scrollLeft - balloonRect.width + offset[0];
        break;
      default:
        break;
    }

    // 智能边界检测和位置调整
    const padding = 8;

    // 垂直方向边界检测和调整
    if (top < scrollTop + padding) {
      // 上边界溢出，调整为下方显示
      if (align === 't' || align === 'tl' || align === 'tr') {
        top = triggerRect.bottom + scrollTop + offset[1];
        // 更新箭头方向
        if (align === 't') currentAlign = 'b';
        else if (align === 'tl') currentAlign = 'bl';
        else if (align === 'tr') currentAlign = 'br';
      } else {
        top = scrollTop + padding;
      }
    } else if (top + balloonRect.height > scrollTop + viewportHeight - padding) {
      // 下边界溢出，调整为上方显示
      if (align === 'b' || align === 'bl' || align === 'br') {
        top = triggerRect.top + scrollTop - balloonRect.height - offset[1];
        // 更新箭头方向
        if (align === 'b') currentAlign = 't';
        else if (align === 'bl') currentAlign = 'tl';
        else if (align === 'br') currentAlign = 'tr';
      } else {
        top = scrollTop + viewportHeight - balloonRect.height - padding;
      }
    }

    // 水平方向边界检测和调整
    // 计算视口边界（相对于文档的绝对位置）
    const leftBoundary = scrollLeft + padding;
    const rightBoundary = scrollLeft + viewportWidth - padding;

    if (left < leftBoundary) {
      // 左边界溢出
      if (align === 'r') {
        // 右侧对齐改为左侧对齐
        left = triggerRect.right + scrollLeft + offset[0];
        currentAlign = 'l';
        // 再次检查右边界
        if (left + balloonRect.width > rightBoundary) {
          left = rightBoundary - balloonRect.width;
        }
      } else if (align === 'tr') {
        // 右上角对齐改为左上角对齐
        left = triggerRect.left + scrollLeft + offset[0];
        currentAlign = 'tl';
        // 再次检查右边界
        if (left + balloonRect.width > rightBoundary) {
          left = rightBoundary - balloonRect.width;
        }
      } else if (align === 'br') {
        // 右下角对齐改为左下角对齐
        left = triggerRect.left + scrollLeft + offset[0];
        currentAlign = 'bl';
        // 再次检查右边界
        if (left + balloonRect.width > rightBoundary) {
          left = rightBoundary - balloonRect.width;
        }
      } else {
        left = leftBoundary;
      }
    } else if (left + balloonRect.width > rightBoundary) {
      // 右边界溢出
      if (align === 'l') {
        // 左侧对齐改为右侧对齐
        left = triggerRect.left + scrollLeft - balloonRect.width - offset[0];
        currentAlign = 'r';
        // 再次检查左边界
        if (left < leftBoundary) {
          left = leftBoundary;
        }
      } else if (align === 'tl') {
        // 左上角对齐改为右上角对齐
        left = triggerRect.right + scrollLeft - balloonRect.width + offset[0];
        currentAlign = 'tr';
        // 再次检查左边界
        if (left < leftBoundary) {
          left = leftBoundary;
        }
      } else if (align === 'bl') {
        // 左下角对齐改为右下角对齐
        left = triggerRect.right + scrollLeft - balloonRect.width + offset[0];
        currentAlign = 'br';
        // 再次检查左边界
        if (left < leftBoundary) {
          left = leftBoundary;
        }
      } else {
        // 直接调整到边界内
        left = rightBoundary - balloonRect.width;
      }
    }

    setPosition({ top, left });
    setActualAlign(currentAlign);
  }, [align, offset]);

  const showBalloon = () => {
    if (!isControlled) {
      setInternalVisible(true);
    }
    onVisibleChange?.(true);
  };

  const hideBalloon = () => {
    if (!isControlled) {
      setInternalVisible(false);
    }
    onVisibleChange?.(false);
  };

  const handleMouseEnter = () => {
    if (triggerTypes.includes('hover')) {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      showBalloon();
    }
  };

  const handleMouseLeave = () => {
    if (triggerTypes.includes('hover')) {
      timeoutRef.current = setTimeout(() => {
        hideBalloon();
      }, 200);
    }
  };

  const handleClick = () => {
    if (triggerTypes.includes('click')) {
      if (visible) {
        hideBalloon();
      } else {
        showBalloon();
      }
    }
  };

  const handleBalloonMouseEnter = () => {
    if (triggerTypes.includes('hover') && timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  const handleBalloonMouseLeave = () => {
    if (triggerTypes.includes('hover')) {
      timeoutRef.current = setTimeout(() => {
        hideBalloon();
      }, 200);
    }
  };

  // 处理Balloon内部链接点击，隐藏组件（使用捕获阶段）
  const handleBalloonClick = (event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    // 检查点击的是否是链接元素
    if (target.tagName === 'A' || target.closest('a')) {
      // 延迟隐藏，确保链接点击事件能正常执行
      setTimeout(() => {
        hideBalloon();
      }, 0);
    }
  };

  useEffect(() => {
    if (visible) {
      // 延迟计算位置，确保DOM已渲染
      const timer = setTimeout(updatePosition, 0);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [visible, align, offset, updatePosition]);

  // 当align改变时，重置actualAlign
  useEffect(() => {
    setActualAlign(align);
  }, [align]);

  useEffect(() => {
    if (visible) {
      const handleResize = () => updatePosition();
      const handleScroll = () => updatePosition();

      window.addEventListener('resize', handleResize);
      window.addEventListener('scroll', handleScroll);

      return () => {
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('scroll', handleScroll);
      };
    }
    return undefined;
  }, [visible, updatePosition]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const balloonContent = visible && (
    <div
      ref={balloonRef}
      className={styles.balloon}
      style={{
        position: 'absolute',
        top: position.top,
        left: position.left,
        zIndex: 1000,
        ...style,
      }}
      onMouseEnter={handleBalloonMouseEnter}
      onMouseLeave={handleBalloonMouseLeave}
      onClickCapture={handleBalloonClick}
    >
      <div className={styles.balloonContent}>
        {children}
      </div>
      <div className={`${styles.balloonArrow} ${styles[`arrow-${actualAlign}`]}`} />
    </div>
  );

  return (
    <>
      <div
        ref={triggerRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        style={{ display: 'inline-block' }}
      >
        {trigger}
      </div>
      {typeof document !== 'undefined' && createPortal(balloonContent, document.body)}
    </>
  );
};

export default CustomBalloon;
