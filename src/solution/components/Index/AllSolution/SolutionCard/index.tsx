import React from 'react';
import { map } from 'lodash';
import { FormattedMessage } from 'react-intl';
import CustomBalloon from './CustomBalloon';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import { getEnv, ENV } from '@/help-fe-common/utils/global/env';
import { solutionBasePath } from '@/solution/constants';
import styles from './index.module.scss';

interface IProps {
  cardItemData: any;
  setSearchValue: (value: string) => void;
  scrollIntoTop: () => void;
}

const SolutionCard = ({ cardItemData, setSearchValue, scrollIntoTop }: IProps) => {
  const { name, url, shortDesc, categoryTagMap, freeInfo } = cardItemData;

  /**
   * 根据环境生成 URL
   * @param url
   * @returns 生成的完整 URL
   */
  const generateUrlByEnv = (path: string): string => {
    // 获取当前环境
    const env = getEnv();

    // 预发环境：改写路径并添加查询参数
    if (env === ENV.PRE) {
      const queryString = 'closeRedirect=true';

      const solutionCode = path?.split('/').pop();

      // 拼接预发环境的 URL
      return `${solutionBasePath}/${solutionCode}?${queryString}`;
    }

    return path;
  };

  const tagTarget = (
    <div className={styles.freeTag} >
      <span className={styles.logo}>
        <i className="help-iconfont help-icon-free-logo" />
      </span>
      <span className={styles.text}>免费试用</span>
    </div>
  );

  return (
    <div className={styles.solutionCard}>
      <a
        href={generateUrlByEnv(url)}
        onClick={() => {
          sendSlsLog({ page: 'solutionIndex', section: 'solutionCard', action: 'click', userParams1: name, userParams2: url });
        }}
        title={name}
        target="_blank"
        rel="noreferrer"
      >
        <div className={styles.head}>
          <h5 className={styles.title} title={name}>{name}</h5>
        </div>
        <div className={styles.content}>
          <p className={styles.desc} title={shortDesc}>{shortDesc}</p>
          <div className={styles.product}>
            {map(categoryTagMap?.product_name, (productName, pipCode) => {
              return (
                <div className={styles.productItem}>
                  <i className={`dbl-icon-product-${pipCode} dbl-icon-product-aliyun`} />
                  <span>{productName}</span>
                </div>
              );
            })}
          </div>
          <div className={styles.tags}>
            {map(categoryTagMap?.tech_solution_scene, (tagName) => (
              <span
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  setSearchValue(tagName);
                  scrollIntoTop();
                  sendSlsLog({
                    page: 'solutionIndex',
                    section: 'solutionCard',
                    action: 'searchClick',
                    userParams1: tagName,
                    userParam2: name,
                  });
                }}
              >
                {tagName}
              </span>
            ))}
          </div>
          <div className={styles.cardBottom}>
            <div className={styles.cardBottomLeft}>
              <span>查看详情</span>
              <i className="help-iconfont help-icon-small-arrow" />
            </div>
            <div className={styles.cardBottomRight}>
              {
                freeInfo?.isFree &&
                <CustomBalloon
                  style={{ minWidth: 240, padding: 12 }}
                  trigger={tagTarget}
                  triggerType="click"
                  closable={false}
                  align="t"
                >
                  <div className={styles.freeTip}>
                    <p className={styles.logo}>
                      <span className={styles.token}>{`${freeInfo?.freeToken} 试用点`}</span>
                      <span> 起/小时免费试用该方案</span>
                    </p>
                    <div className={styles.linkContainer}>
                      {freeInfo?.deployUrl && (
                        <>
                          <a
                            href={freeInfo.deployUrl}
                            className={styles.link}
                            target="_blank"
                            rel="noreferrer"
                            onClick={(e) => {
                              e.stopPropagation();
                              sendSlsLog({
                                page: 'solutionIndex',
                                section: 'solutionCard',
                                action: 'tryNow',
                                userParams1: name,
                                userParams2: freeInfo.deployUrl,
                              });
                            }}
                          ><FormattedMessage id="help.solution.tryNow" />
                            <span><i className="help-iconfont help-icon-Right-Arrow" /></span>
                          </a>
                          <span className={styles.divider}>|</span>
                        </>
                      )}
                      <div className={styles.freeGetContainer}>
                        <span className={styles.noPointsText}>没有试用点？</span>
                        <a
                          href={getEnv() === ENV.PRE ? 'https://pre-www.aliyun.com/solution/free' : 'https://www.aliyun.com/solution/free'}
                          className={styles.freeGetLink}
                          target="_blank"
                          rel="noreferrer"
                          onClick={(e) => e.stopPropagation()}
                        >免费领取
                        </a>
                      </div>
                    </div>
                  </div>
                </CustomBalloon>
              }
            </div>
          </div>
        </div>
      </a>
    </div>
  );
};

export default SolutionCard;
