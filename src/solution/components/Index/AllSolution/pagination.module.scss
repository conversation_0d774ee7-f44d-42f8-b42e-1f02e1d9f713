.pagination:global(.next-pagination) {
  :global {
    .next-pagination-total {
      display: none;
    }

    .next-pagination-list {
      margin: 0;
    }

    .next-pagination-display {
      display: none;

      &+.next-pagination-jump-text {
        color: transparent;

        &:after {
          content: '跳转至：';
          color: #999;
          font-size: 12px;
          margin-left: -12px;
        }
      }
    }

    .next-pagination-jump-input {
      width: 40px;
      height: 30px;
      margin-right: 8px;
      margin-left: 0;
      display: inline-block;

      &.next-focus {
        border-color: #1366ec;
        box-shadow: none;
      }

      &:hover {
        border-color: #1366ec;
      }

      &+.next-pagination-jump-text {
        display: none;
      }
    }

    .next-pagination-jump-go {
      height: 30px;
      border: 1px solid #1366ec;
      color: #1366ec;
      margin-left: 0;
      font-size: 12px;

      &:hover {
        background-color: #1366ec;
        color: #fff;
      }
    }

    .next-pagination-pages {
      .next-icon-last::before {
        font-size: 12px;
      }

      .next-icon-first:before {
        font-size: 12px;
      }
    }

    .next-pagination-list {
      .next-pagination-ellipsis {
        border: 1px solid #d8d8d8;
        border-left-color: transparent;
        border-right-color: transparent;
        padding: 0 9px;
        margin: 0;
        height: 30px;
        width: 30px;
      }

      @media (min-width: 1056px) {
        .next-pagination-item {
          &:first-child {
            border-left-color: transparent;
          }

          &:last-child {
            border-right-color: transparent;
          }

          &+.next-pagination-item {
            margin: 0;
            border-left-color: transparent;
          }

          &.next-current {
            border-color: #1366ec;
            background-color: #1366EC;

            &:hover {
              background-color: #0f52bd;
            }
          }

          &:hover {
            border-color: #1366ec;
          }
        }
      }
    }

    .next-pagination-item {
      height: 30px;
      width: 30px;
      font-size: 12px;
      text-align: center;
      padding: 0;

      &:not(:disabled):hover {
        border-color: #1366ec;
      }
    }
  }
}

@media screen and (max-width: 1055px) {
  .pagination:global(.next-pagination) {
    position: relative;
    padding-bottom: 62px;

    :global {
      .next-pagination-total {
        position: absolute;
        left: 50%;
        bottom: 2px;
        transform: translateX(-50%);
        border-top: 1px solid rgba(126, 134, 142, 0.16);
        width: calc(100% - 24px);
        height: 46px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: #555555;
      }

      .next-pagination-pages {
        height: 46px;
        box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.16);
        border-radius: 8px;
        position: relative;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 12px;
      }

      .next-pagination-jump-text,
      .next-pagination-jump-input,
      .next-pagination-jump-go {
        display: none;
      }

      .next-pagination-pages {
        .next-pagination-list {
          overflow-x: auto;
          white-space: nowrap;

          &::-webkit-scrollbar {
            display: none;
          }
        }

        .next-pagination-item {
          width: 36px;
          height: 36px;
          border-radius: 4px;
          background-color: #EEE;
          border: none;
          font-size: 14px;
          font-weight: 500;

          &.next-current {
            color: #1366ec;
          }

          &:not(:first-child) {
            margin-left: 4px;
          }

          &.next-prev {
            width: 36px;
            flex: none;
            margin-right: 10px;
          }

          &.next-next {
            width: 36px;
            flex: none;
            margin-left: 10px;
          }
        }

        &::after {
          content: "";
          display: block;
          position: absolute;
          left: 50%;
          top: 42px;
          transform: translate(-50%);
          border-left: 10px solid transparent;
          border-right: 10px solid transparent;
          border-top: 10px solid #fff;
        }
      }
    }
  }
}