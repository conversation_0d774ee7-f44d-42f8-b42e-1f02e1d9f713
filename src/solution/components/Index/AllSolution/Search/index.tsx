import React, { useState } from 'react';
import classnames from 'classnames';
import { useIntl, FormattedMessage } from 'react-intl';
import { scrollIntoTop } from '../utils';
import styles from './index.module.scss';

const Search = ({ searchValue, setSearchValue, hotSearchKeywords }) => {
  const intl = useIntl();
  const [searchFocus, setSearchFocus] = useState(false);

  /**
   * 处理搜索逻辑
   * @param e 搜索event对象
   */
  const onProductListFilterChange = (e) => {
    const value = e?.target?.value;
    setSearchValue(value);
  };
  return (
    <div className={styles.searchContainer}>
      <div className={classnames(styles.searchInput, searchFocus ? styles.searchFocus : '')} >
        <i className="help-iconfont help-icon-sousuoicon" />
        <input
          value={searchValue}
          placeholder={intl.formatMessage({ id: 'help.solution.search' })}
          className={classnames(styles.input)}
          onChange={(e) => onProductListFilterChange(e)}
          onFocus={() => { setSearchFocus(true); }}
          onBlur={() => { setSearchFocus(false); }}
          autoComplete="off"
        />
        {
          searchValue &&
          <button
            className={styles.clearButton}
            onClick={() => { onProductListFilterChange(null); setSearchValue(''); }}
          >
            <i className="help-iconfont help-icon-quxiao" />
          </button>
        }
      </div>
      <div className={styles.hotSearch} >
        <FormattedMessage id="help.solution.hotValue" />
        {hotSearchKeywords.map((item) => (
          <span
            key={item}
            className={styles.searchText}
            onClick={() => {
              setSearchValue(item);
              scrollIntoTop();
            }}
          >
            {item}
          </span>
        ))}
      </div>
    </div>
  );
};
export default Search;
