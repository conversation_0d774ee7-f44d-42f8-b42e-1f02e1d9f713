.searchContainer {
  margin-bottom: 28px;

  .searchInput {
    height: 50px;
    width: 100%;
    background: #F5F8FC;
    font-size: 14px;
    color: #999;
    padding-left: 24px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;

    i {
      font-size: 18px;
    }

    .input {
      padding-left: 10px;
      width: 100%;
      height: 24px;
      line-height: 24px;
      letter-spacing: 1px;
      outline: none;
      border: none;
      background: transparent;
      font-size: 14px;
      letter-spacing: 0;
    }

    .clearButton {
      cursor: pointer;
      border: none;
      background: none;

      i {
        color: #999;
        margin-right: 20px;
        font-size: 12px;

        &:hover {
          color: #1366ec;
        }
      }
    }

    &:hover {
      border-bottom-color: #1366ec;

      >i {
        color: #1366ec;
      }
    }
  }

  .searchFocus {
    border-bottom-color: #1366ec;

    .searchInput,
    >i {
      color: #1366ec;
    }
  }

  .hotSearch {
    font-size: 14px;
    letter-spacing: 1px;
    color: #8C8C8C;
    padding: 0 24px;

    .searchText {
      margin-right: 12px;
      cursor: pointer;

      &:hover {
        color: #1366ec;
      }
    }
  }
}

@media only screen and (max-width: 1055px) {

  .searchContainer {
    margin-bottom: 20px;

    .searchInput {
      height: 32px;
      padding-left: 10px;
    }

    .hotSearch {
      padding: 0;
      color: #181818;
      height: 14px;
      overflow: hidden;
      line-height: 14px;

      .searchText {
        white-space: nowrap;
      }
    }
  }
}