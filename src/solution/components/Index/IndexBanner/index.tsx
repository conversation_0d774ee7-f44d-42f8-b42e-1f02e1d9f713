import React, { useMemo } from 'react';
import { scrollIntoView as seamlessScrollIntoView } from 'seamless-scroll-polyfill';
import ButtonGroup from '@/solution/components/Common/ButtonGroup';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import styles from './index.module.scss';

interface recommendItem {
  detailUrl: string;
  imgUrl: string;
  title: string;
  desc: string;
  tag: string;
}

interface IProps {
  data: {
    title: string;
    desc: string;
    contactUrl: string;
    evaluateUrl?: string;
    recommendDataList: recommendItem[];
  };
  hotTopicData: Array<{
    title: string;
    desc: string;
    url: string;
  }>;
}

const Banner = ({ data, hotTopicData }: IProps) => {
  const { title, desc, buttonGroup } = useMemo(() => {
    if (!data) return {} as any;
    const group = [{
      text: '全部解决方案',
      onClick: () => {
        const ele = document.querySelector('#solution-all-container');
        if (ele) {
          seamlessScrollIntoView(ele, { behavior: 'smooth' });
        }
      },
    }, {
      text: '联系咨询',
      url: data?.contactUrl,
    }];

    return { buttonGroup: group, ...data };
  }, [data]);

  if (!data) return null;

  return (
    <div className={styles.bannerContainer}>
      <div className={styles.titleContainer}>
        <h1 className={styles.title} dangerouslySetInnerHTML={{ __html: title }} />
        <p className={styles.desc} >{desc}</p>
        <ButtonGroup data={buttonGroup} size="large" type="secondary" />
      </div>
      <div className={styles.topicContainer}>
        {
          hotTopicData?.slice(0, 2).map((item, index) => {
            return (
              <a
                href={item.url}
                className={styles.topicItem}
                key={index}
                target="_blank"
                rel="noreferrer"
                onClick={() => {
                  sendSlsLog({ page: 'solutionIndex', section: 'banner', action: 'click', userParams1: index, userParams2: item?.url });
                }}
              >
                <div className={styles.topicTitle}>
                  <div>{item.title}</div>
                  <i className="help-iconfont help-icon-small-arrow" />
                </div>
                <p className={styles.topicDesc}>{item.desc}</p>
              </a>
            );
          })
        }
      </div>
    </div>
  );
};

export default Banner;
