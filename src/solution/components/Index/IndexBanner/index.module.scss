@mixin text-line($line-number: 1) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line-number;
}

.bannerContainer {
  margin-bottom: 40px;

  .titleContainer {
    display: flex;
    flex-direction: column;
    padding-left: 24px;

    .title {
      line-height: 48px;
      font-size: 44px;
      color: #181818;
      font-weight: 600;
      margin: 0 0 20px;

      span {
        background: linear-gradient(79deg, #1366EC 0%, #6355FF 94%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
    }

    .desc {
      width: 648px;
      line-height: 28px;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 16px;
      margin-bottom: 32px;
    }
  }

  .topicContainer {
    width: 50%;
    height: 100px;
    position: absolute;
    bottom: 0;
    right: 24px;
    display: flex;

    .topicItem {
      width: 50%;
      padding: 24px;
      background: rgba(255, 255, 255, 0.7);
      backdrop-filter: blur(40px);
      line-height: 20px;
      color: #181818;
      font-weight: 600;
      cursor: pointer;
      text-decoration: none;
      display: block;

      &:hover {
        background-color: rgba(245, 245, 246, 0.7);
        text-decoration: none;
        color: #181818;
      }

      &:first-child {
        background-color: #0062ff;
        color: #fff;

        &:hover {
          background-color: #0f52bd;
          color: #fff;
        }
      }

      .topicTitle {
        font-size: 16px;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        i {
          font-size: 14px;
          font-weight: normal;
        }
      }

      .topicDesc {
        font-size: 12px;
        @include text-line(1);
      }
    }
  }
}

@media screen and (max-width: 1055px) {
  .bannerContainer {
    margin-bottom: 0;

    .titleContainer {
      margin: 0 0 36px;
      padding-left: 0;

      .title {
        font-size: 26px;
        font-weight: 600;
        line-height: 42px;
      }

      .desc {
        width: unset;
        font-size: 16px;
        line-height: 28px;
      }
    }

    .topicContainer {
      height: 154px;
      width: calc(100% + 32px);
      position: relative;
      bottom: 0;
      right: 0;

      .topicItem {
        position: relative;
        padding: 20px 16px;
        text-decoration: none;
        display: block;

        &:first-child {
          margin-left: -16px;
          background: rgba(255, 255, 255, 0.7);
          color: #181818;
          border-right: 1px solid #E9E9E9;

          &:hover {
            background-color: rgba(245, 245, 246, 0.7);
            color: #181818;
          }
        }

        .topicTitle {
          i {
            color: #1366EC;
            font-size: 16px;
            position: absolute;
            left: 24px;
            bottom: 24px;
          }
        }

        .topicDesc {
          @include text-line(2);
        }
      }
    }
  }
}