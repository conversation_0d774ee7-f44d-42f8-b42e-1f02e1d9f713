.anchorContainer {
  height: 50px;
  border-bottom: 1px solid #D8D8D8;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
  position: relative;

  .arrowLeft, .arrowRight {
    display: none;
  }

  @media screen and (min-width: 1056px) {
    .arrowLeft, .arrowRight {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      z-index: 1;
      background-color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 10px;

      i {
        transform: rotate(180deg);
        color: #000;
        font-size: 12px;

        &:hover {
          color: #1366ec;
        }
      }

      &:hover {
        background-color: #FAFAFA;
      }
    }

    .arrowRight {
      i {
        transform: initial;
      }

      right: 0;
      left: auto;
    }
  }

  .anchorsWrapper {
    flex: auto;
    overflow: hidden;
    position: relative;
    height: 100%;
    line-height: 50px;
  }

  .anchors {
    display: inline-flex;
    align-items: center;
    transition: .3s;

    .anchorItem {
      padding: 0 24px;
      height: 17px;
      line-height: 17px;
      font-size: 16px;
      font-weight: 700;
      list-style: none;
      cursor: pointer;
      white-space: nowrap;

      &:hover {
        @media screen and (min-width: 1055px) {
          color: #1366ec;
        }
      }

      &.active {
        color: #1366ec;
      }
    }
  }

  .buttons {
    flex: none;
  }
}

@media screen and (max-width: 1055px) {
  .anchorContainer {
    overflow-x: auto;

    &::-webkit-scrollbar {
      height: 0;
    }

    .anchorsWrapper {
      overflow: initial;
    }

    .anchors {

      > *:nth-child(n+2) {
        margin-left: 8px;
      }

      .anchorItem {
        font-weight: 600;
        color: #181818;
        padding: 0 16px;
        white-space: nowrap;

        &:first-child {
          padding-left: 0;
        }
      }
    }

    .buttons {
      display: none;
    }
  }
}
