import React, { useEffect, useRef, useState } from 'react';
import classNames from 'classnames';
import { throttle } from 'lodash';
import ButtonGroup from '@/solution/components/Common/ButtonGroup';
import { AnchorItemData } from '@/solution/utils/dataEngine/dataSchema';
import { scrollIntoView } from '@/solution/utils';
import floorHeadStyle from '@/solution/components/Detail/Floor/FloorHead/index.module.scss';
import styles from './index.module.scss';

interface IProps {
  data: AnchorItemData[];
  buttonGroup: [];
}

type ArrowType = 'left' | 'right' | null;

const Anchor = ({ data, buttonGroup }: IProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const ref = useRef<HTMLDivElement>(null);
  const [showButton, setShowButton] = useState(false);
  const [activeIndex, setActiveIndex] = useState(-1);
  const [arrow, setArrow] = useState<ArrowType>(null);

  const throttleScroll = throttle(() => {
    const anchorDom = document.getElementById('aliyun-solution-anchor');
    const offsetTop = anchorDom?.getBoundingClientRect()?.top || 0;
    const isShow = offsetTop <= 0;
    setShowButton(isShow);

    document.querySelectorAll(`.${floorHeadStyle.h2Title}`).forEach((e, index) => {
      const ele = e.closest('.help-body-wrapper-column');

      if (ele) {
        const rect = ele.getBoundingClientRect();

        if (rect.top < 50 && rect.bottom > 50) {
          setActiveIndex(index);
        }
      }
    });
  }, 100);

  useEffect(() => {
    window.addEventListener('scroll', throttleScroll);

    return () => {
      window.removeEventListener('scroll', throttleScroll);
    };
  }, []);

  useEffect(() => {
    if (!ref.current || !containerRef.current) return;

    const resizeObserver = new ResizeObserver((entries) => {
      entries.forEach(() => {
        if (!ref.current || !containerRef.current) return;

        if (containerRef.current.clientWidth < ref.current.clientWidth) {
          setArrow('right');
        } else {
          setArrow(null);
        }
      });
    });

    resizeObserver.observe(containerRef.current);
    return () => {
      resizeObserver.disconnect();
    };
  }, [containerRef.current]);

  useEffect(() => {
    if (!ref.current || !containerRef.current) return;

    if (arrow === 'left') {
      ref.current.style.transform = `translateX(${containerRef.current.clientWidth - ref.current.clientWidth}px)`;
    } else {
      ref.current.style.transform = 'initial';
    }
  }, [arrow, ref.current]);

  return (
    <div className={classNames('col-extend-left', 'col-extend-right', styles.anchorContainer)} id="aliyun-solution-anchor">
      <div className={styles.anchorsWrapper} ref={containerRef}>
        {arrow === 'left' && (
          <div className={styles.arrowLeft} onClick={() => setArrow('right')}>
            <i className="help-iconfont help-icon-close-arrow" />
          </div>
        )}
        <div className={styles.anchors} ref={ref}>
          {data?.map((anchorItem, index) => (
            <li
              key={index}
              className={`${styles.anchorItem} ${index === activeIndex ? styles.active : ''}`}
              onClick={() => { scrollIntoView(anchorItem?.id); }}
            >
              {anchorItem.text}
            </li>
          ))}
        </div>
        {arrow === 'right' && (
          <div className={styles.arrowRight} onClick={() => setArrow('left')}>
            <i className="help-iconfont help-icon-close-arrow" />
          </div>
        )}
      </div>
      {
        showButton &&
        <div className={styles.buttons}>
          <ButtonGroup data={buttonGroup} size="small" type="primary" />
        </div>
      }
    </div>
  );
};

export default Anchor;
