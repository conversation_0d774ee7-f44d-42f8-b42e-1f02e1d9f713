.main {
  overflow: hidden;

  .infoContainer {
    margin-bottom: 24px;
    display: flex;

    &:last-child {
      margin-bottom: 0;
    }

    .title {
      flex: 0 0 80px;
      height: 24px;
      font-size: 14px;
      font-weight: 600;
      line-height: 24px;
    }

    .content {
      .infoItem {
        list-style: none;
        height: auto;
        line-height: 24px;
        margin-bottom: 2px;
        font-size: 14px;
        padding-left: 24px;
        position: relative;

        &:last-child {
          margin-bottom: 0;
        }

        i {
          color: #1366ec;
          margin-right: 12px;
          font-size: 10px;
          font-weight: 700;
          width: 14px;
          display: inline-block;
          position: absolute;
          left: 0;
        }
      }
    }
  }
}