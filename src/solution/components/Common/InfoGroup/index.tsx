import React from 'react';
import { InfoItem } from '@/solution/utils/dataEngine/dataSchema';
import styles from './index.module.scss';


interface IProps {
  data: InfoItem[];
}
const InfoGroup = ({ data }: IProps) => {
  return (
    <div className={styles.main}>
      {
        data?.length > 0 && data?.map((info, listIndex) => (
          <div className={styles.infoContainer} key={listIndex}>
            <div className={styles.title}>{info?.title}</div>
            <ul className={styles.content}>
              {
                info?.infoArray?.map((infoItem: any, index: number) => (
                  <li key={index} className={styles.infoItem}>
                    <i className="help-iconfont help-icon-duigou1" />
                    {infoItem?.text}
                  </li>
                ))
              }
            </ul>
          </div>
        ))
      }
    </div>
  );
};
export default InfoGroup;
