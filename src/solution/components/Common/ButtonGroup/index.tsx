import React from 'react';
import { ButtonItemData } from '@/solution/utils/dataEngine/dataSchema';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import styles from './index.module.scss';
import classNames from 'classnames';

interface IProps {
  data: ButtonItemData[];
  size: string;
  type?: string;
}

const ButtonGroup = ({ data, size, type }: IProps) => {
  const getButtonType = (index) => {
    if (index === 0) {
      return styles.applyButton;
    } else if (type === 'primary') {
      return styles.primaryButton;
    } else if (type === 'secondary') {
      return styles.secondaryBtn;
    } else {
      return styles.normalButton;
    }
  };

  return (
    <div
      className={styles.buttonGroup}
      style={{ width: size === 'adaptive' ? '100%' : undefined }}
    >
      {
        data?.map((buttonItem, index) => (
          <a
            key={index}
            className={classNames(
              getButtonType(index),
              size === 'small' ? styles.smallButton : '',
              size === 'adaptive' ? styles.adaptiveButton : '',
            )}
            href={buttonItem?.url}
            target="_blank"
            onClick={() => {
              if (buttonItem?.onClick) {
                buttonItem.onClick();
              }
              sendSlsLog({ page: 'solutionDetail', section: 'button', action: 'click', userParams1: buttonItem?.text, userParams2: buttonItem?.url })
            }}
          >
            {buttonItem?.text}
          </a>
        ))
      }
    </div>
  );
};

export default ButtonGroup;
