.buttonGroup {
  display: flex;
  align-items: center;

  .applyButton,
  .normalButton,
  .primaryButton,
  .secondaryBtn {
    width: 160px;
    height: 44px;
    margin-right: 16px;
    border: none;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

    &:last-child {
      margin-right: 0;
    }
  }

  .smallButton {
    width: 130px;
    height: 30px;
    font-size: 14px;
  }

  .adaptiveButton {
    flex: auto;
    height: 36px;
    font-size: 14px;
  }

  .applyButton {
    background-image: linear-gradient(253deg, #7366FF 0%, #1366EC 100%);

    color: #fff;

    &:hover {
      background-image: linear-gradient(79deg, #1154C0 0%, #6340FF 100%);
    }
  }

  .normalButton {
    border: 1px solid #9CA4AF;
    background-color: #fff;
    color: #1366EC;

    &:hover {
      border-color: #1366ec;
      background-color: #1366ec;
      color: #fff;
    }
  }

  .primaryButton {
    border: 1px solid #9CA4AF;
    background-color: #fff;
    color: #3D495C;

    &:hover {
      background-color: #FF6A00;
      border-color: #FF6A00;
      color: #fff;
    }
  }

  .secondaryBtn {
    color: #1366ec;
    border: 1px solid #999999;
    background: transparent;

    &:hover {
      color: #1154c0;
    }
  }
}

@media screen and (max-width: 1055px) {
  .buttonGroup {
    width: 100%;
    justify-content: center;

    .applyButton,
    .normalButton,
    .primaryButton,
    .secondaryBtn {
      width: calc(50% - 7.5px);

      &:only-child {
        margin-right: 0;
      }
    }

    .applyButton {
      margin-right: 15px;
    }
  }
}