import React, { useRef, useState } from 'react';
import PcImgPreview from '@/help-fe-common/components/pc/ImgPreview';

const ImgZoom = ({ imgUrl }: { imgUrl: string }) => {
  const imgRef = useRef<any>(null);
  const [showImgDetail, setShowImgDetail] = useState(false);

  return (
    <>
      {
        showImgDetail ? (
          <div className="unionContainer" >
            <div className="markdown-body">
              <PcImgPreview data={imgRef.current} onClose={() => setShowImgDetail(false)} />
            </div>
          </div >
        ) : (
          <img onClick={() => setShowImgDetail(true)} ref={imgRef} src={imgUrl} alt="" loading="lazy" />
        )}
    </>
  );
};
export default ImgZoom;
