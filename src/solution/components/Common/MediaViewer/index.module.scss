.mediaViewer {
  width: 100%;
  height: 100%;

  .img,
  .video {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    &:empty {
      display: none;
    }
  }

  .img {
    background-color: #f3f3f3;
    padding: 32px 24px;
  }

  .video {
    background-color: #000;
    padding: 0;
  }

  img,
  video {
    max-width: 100%;
    height: auto;
    object-fit: contain;
  }

  img {
    max-height: 455px;
    cursor: zoom-in;
  }
}

@media screen and (max-width: 1055px) {
  .mediaViewer {
    video {
      max-height: 455px;
    }
  }
}