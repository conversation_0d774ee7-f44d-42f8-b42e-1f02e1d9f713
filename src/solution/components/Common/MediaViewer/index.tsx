import React from 'react';
import { Player } from '@ali/rachel-player';
import ImgZoom from '@/solution/components/Common/ImgZoom';
import { ENV, getEnv } from '@/help-fe-common/utils/global/env';
import styles from './index.module.scss';

interface IProps {
  imgSrc?: string;
  diagramId?: string;
  videoSrc?: string;
}
const MediaViewer = ({ imgSrc, diagramId, videoSrc }: IProps) => {
  // 根据环境设置不同的env入参
  const currentEnv = getEnv();
  const playerEnv = currentEnv === ENV.PROD ? 'prod' : 'pre';

  if (diagramId) {
    return (
      <div className={styles.mediaViewer} >
        <Player
          templateId={diagramId}
          env={playerEnv}
          style={{
            height: '100%',
            padding: '32px 24px',
          }}
        />
      </div>
    );
  }

  return (
    <div className={styles.mediaViewer} >
      <div className={styles.img}>
        {
          imgSrc && <ImgZoom imgUrl={imgSrc} />
        }
      </div>
      <div className={styles.video}>
        {
          videoSrc &&
          <video
            src={videoSrc}
            controls
            playsInline
          />
        }
      </div>
    </div>
  );
};

export default MediaViewer;
