.main {
  height: 240px;
  background-position: 50%;
  background-size: cover;
  background-repeat: no-repeat;
  margin-top: 40px;
}

.container {
  width: 100%;
  height: 100%;
  max-width: 1920px;
  margin: 0 auto;
  overflow: hidden;
}

.left {
  height: 100%;
  padding: 56px 48px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.title {
  font-size: 34px;
  font-weight: 600;
  line-height: 44px;
  color: #181818;
}

a.link {
  width: 23%;
  min-width: 180px;
  height: 52px;
  padding: 0 24px;
  font-size: 14px;
  font-weight: 600;
  background-image: linear-gradient(253deg, #7366FF 0%, #1366EC 100%);
  color: #fff !important;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:hover,
  &:active,
  &:visited {
    color: #fff;
    background-image: linear-gradient(79deg, #1154C0 0%, #6340FF 100%);
  }

  img {
    width: 10px;
    height: 8px;
  }
}

@media screen and (max-width: 1055px) {
  .main {
    height: 165px;
    margin-top: 24px;
    background-position: 70%;
  }

  .left {
    padding: 32px 0;
  }

  .title {
    font-size: 24px;
    font-weight: 600;
    line-height: 37px;
  }

  a.link {
    width: 100%;
    max-width: 344px;
    height: 44px;
    padding: 10px 16px;
    font-weight: 500;
    font-size: 12px;
  }
}