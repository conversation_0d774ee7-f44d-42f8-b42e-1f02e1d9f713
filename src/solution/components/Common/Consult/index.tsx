import React from 'react';
import classNames from 'classnames';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import styles from './index.module.scss';

export default function Consult({ type }) {
  const data = {
    title: '技术解决方案咨询',
    btn: {
      text: '立即咨询',
      link: 'https://smartservice.console.aliyun.com/service/pre-sales-chat?from=solution',
    },
  };

  const backgroundUrl = {
    index: 'https://img.alicdn.com/imgextra/i4/O1CN01Cx8FIp1uybQ78gkHS_!!6000000006106-0-tps-3840-480.jpg',
    detail: 'https://img.alicdn.com/imgextra/i4/O1CN01Cx8FIp1uybQ78gkHS_!!6000000006106-0-tps-3840-480.jpg',
  };

  return (

    <div
      className={classNames('col-extend-left', 'col-extend-right', styles.main)}
      style={{ backgroundImage: `url('${backgroundUrl?.[type]}')` }}
    >
      <div className={styles.container}>
        <div className={styles.left}>
          <div className={styles.leftTop}>
            <div className={styles.title}>
              <b>{data.title}</b>
            </div>
          </div>
          {
            data.btn && (
              <a
                href={data.btn.link}
                onClick={() => {
                  sendSlsLog({ page: type === 'index' ? 'solutionIndex' : 'solutionDetail', section: 'contact', action: 'click' });
                }}
                target="_blank"
                className={styles.link}
                rel="noreferrer"
              >
                <span>{data.btn.text}</span>
                <img alt="" src="https://img.alicdn.com/imgextra/i3/O1CN01XuLiSd1HHIsuiLzSa_!!6000000000732-2-tps-20-16.png" loading="lazy" />
              </a>
            )
          }
        </div>
      </div>
    </div>
  );
}
