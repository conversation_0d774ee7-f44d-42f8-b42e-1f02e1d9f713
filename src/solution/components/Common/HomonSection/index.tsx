import React, { HTMLAttributes, useCallback } from 'react';
import { renderPage } from '@ali/homon-page-delivery';

interface HomonSectionProps extends HTMLAttributes<HTMLDivElement> {
  pagePath: string;
}

const HomonSection = ({ pagePath, ...props }: HomonSectionProps) => {
  const sectionRef = useCallback((node: HTMLDivElement) => {
    if (node !== null) {
      renderPage({
        selector: node,
        pagePath,
        env: 'release',
      });
    }
  }, []);

  return <section ref={sectionRef} {...props} />;
};

export default HomonSection;
