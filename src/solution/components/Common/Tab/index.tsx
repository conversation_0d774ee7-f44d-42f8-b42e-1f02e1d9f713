import React from 'react';
import _ from 'lodash';
import { Tab } from '@alifd/next';
import FloorContent from '@/solution/components/Detail/Floor/FloorContent';
import { TabItemData } from '@/solution/utils/dataEngine/dataSchema';
import styles from './index.module.scss';
import tabStyles from './tab.module.scss';

interface IProps {
  data: TabItemData[];
}

export default function TabContainer({ data }: IProps) {
  return (
    <div className={styles.main}>
      <Tab
        className={tabStyles.tab}
        contentClassName={tabStyles.tabItem}
      >
        {_.map(data, (tabItem: TabItemData) => {
          const { tabId: id, componentType, data: tabData } = tabItem;
          return (
            <Tab.Item title={tabItem.tabTitle} key={tabItem.tabId}>
              <FloorContent data={{ id, componentType, ...tabData }} />
            </Tab.Item>
          );
        })}
      </Tab>
    </div>
  );
}

