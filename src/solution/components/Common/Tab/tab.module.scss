.tab:global(.next-tabs-pure) {
  :global {
    .next-tabs-bar {
      padding-left: var(--col-wrapper-margin);
      padding-right: var(--col-wrapper-margin);
      margin-left: calc(var(--col-wrapper-margin)* -1);
      margin-right: calc(var(--col-wrapper-margin)* -1);
    }

    .next-tabs-bar .next-tabs-nav-container .next-tabs-tab {
      min-width: 100px;
      font-size: 14px;
      font-weight: 500;
      color: #181818;
      text-align: center;

      &:hover {
        color: #1366ec;
      }

      &.active {
        color: #1366ec;
      }

      &:before {
        border-color: #1366EC;
        left: 0;
      }

      .next-tabs-tab-inner {
        font-size: 14px;
        padding: 12px 24px;
      }
    }
  }

  &.tabAdaptive {
    :global {
      .next-tabs-nav {
        display: flex;

        .next-tabs-tab {
          flex: auto;
        }
      }
    }
  }
}

.tabItem:global(.next-tabs-content) {
  padding-top: 20px;
  overflow: initial;
}


@media screen and (max-width: 1055px) {
  .tab:global(.next-tabs-pure) {
    :global {
      .next-tabs-bar {
        margin: 0 -16px;

        .next-tabs-nav-container {
          line-height: 0;

          &.next-tabs-nav-container-scrolling {
            line-height: 0;

            >button {
              line-height: 0;
            }
          }

          .next-tabs-tab {
            min-width: auto;

            &+.next-tabs-tab {
              margin-left: 24px;
            }
          }
        }
      }

      .next-tabs-tab-inner {
        padding: 10px 0;
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
      }
    }
  }
}