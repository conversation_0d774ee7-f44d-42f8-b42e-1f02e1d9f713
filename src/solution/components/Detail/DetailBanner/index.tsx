import React, { useMemo } from 'react';
import ButtonGroup from '@/solution/components/Common/ButtonGroup';
import { BannerHeadData } from '@/solution/utils/dataEngine/dataSchema';
import styles from './index.module.scss';
import classNames from 'classnames';

interface IProps {
  data: BannerHeadData;
}

const Banner = ({ data }: IProps) => {
  const { title, desc, buttonGroup, info } = useMemo(() => {
    if (!data) return {} as any;
    return data;
  }, [data]);

  if (!data) return null;

  return (
    <div className={classNames('col-extend-left', 'col-extend-right', styles.bannerContainer)} >
      <div className={styles.titleContainer}>
        <div className={styles.breadcrumb}>
          <i className="help-iconfont help-icon-right-arrow" />
          <a href="https://www.aliyun.com/solution/tech-solution/" target="_blank" rel="noreferrer">技术解决方案</a>
        </div>
        <h1 className={styles.title} title={title}>
          {title}
        </h1>
        <p className={styles.desc} title={desc}>{desc}</p>
        <ButtonGroup data={buttonGroup} size="large" type="primary" />
      </div>
      <div className={styles.infoContainer} >
        <div className={styles.title}>{info?.title}</div>
        <div className={styles.content}>
          {
            info?.infoArray?.map((infoItem: any, index: number) => (
              <li key={index} className={styles.infoItem}>
                <i className="help-iconfont help-icon-duigou1" />
                {infoItem?.text}
              </li>
            ))
          }
        </div>
      </div>
    </div>
  );
};

export default Banner;
