@mixin text-line($line-number: 1) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line-number;
}

.bannerContainer {
  height: 300px;
  background-color: #F5F8FC;
  background-image: url(https://img.alicdn.com/imgextra/i3/O1CN01cLZL0t1EYl8RvCURG_!!6000000000364-0-tps-2880-600.jpg);
  background-position: center;
  background-size: cover;
  padding: 60px 0 60px 24px;
  display: flex;
  justify-content: space-between;

  .titleContainer {
    position: relative;
    padding-left: 24px;

    .breadcrumb {
      position: absolute;
      top: -30px;
      font-size: 14px;
      height: 20px;
      display: flex;
      align-items: center;

      i {
        transform: rotateY(180deg);
        display: inline-block;
        margin-right: 8px;
      }

      a {
        color: #181818;

        &:hover {
          color: #1366ec;
        }
      }
    }

    .title {
      line-height: 48px;
      font-size: 34px;
      margin: 0 0 24px;
      @include text-line(1);
    }

    .desc {
      line-height: 26px;
      font-size: 16px;
      margin-bottom: 24px;
      margin-right: 24px;
      @include text-line(2);
    }
  }

  .infoContainer {
    width: 500px;
    min-width: 400px;
    height: 180px;
    padding: 20px;
    background-color: #fff;

    .title {
      height: 25px;
      font-size: 18px;
      font-weight: 500;
    }

    .content {
      margin: 15px 0;

      .infoItem {
        list-style: none;
        height: auto;
        line-height: 20px;
        margin-bottom: 12px;
        font-size: 14px;

        i {
          color: #1366ec;
          margin-right: 12px;
          font-size: 10px;
          font-weight: 700;
          width: 14px;
          display: inline-block;
        }
      }
    }
  }
}

@media screen and (max-width: 1055px) {
  .bannerContainer {
    flex-direction: column;
    height: auto;
    padding: 60px 16px 24px;
    position: relative;
    background-image: url(https://img.alicdn.com/imgextra/i4/O1CN01yNyJ391GEj2jZzPpB_!!6000000000591-0-tps-750-990.jpg);

    .titleContainer {
      padding-left: 0;

      .breadcrumb {
        top: -36px;
        height: auto;
      }

      .title {
        font-size: 26px;
        font-weight: 600;
        line-height: 42px;
        margin-bottom: 10px;
      }

      .desc {
        color: #181818;
        font-size: 14px;
        font-weight: 400;
        line-height: 24px;
        margin-bottom: 256px;
      }
    }

    .infoContainer {
      width: calc(100% - 32px);
      height: auto;
      position: absolute;
      bottom: 92px;
      min-width: auto;

      .title {
        height: auto;
      }

      .content {
        margin: 12px 0;

        .infoItem {
          height: auto;
          display: flex;

          i {
            transform: translateY(3px);
          }
        }
      }
    }
  }
}
