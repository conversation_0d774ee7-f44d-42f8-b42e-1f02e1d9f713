import React, { useEffect, useState } from 'react';
import { throttle } from 'lodash';
import ButtonGroup from '@/solution/components/Common/ButtonGroup';
import styles from './index.module.scss';

export default function MobileBottom({ buttonGroup }) {
  const height = 64;
  const isIOS = /iPhone/g.test(window.navigator.userAgent);
  const styleHeight = isIOS ? height + 22 : height;
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    const listener = throttle(() => {
      setVisible(window.scrollY > 500);
    }, 50);
    window.addEventListener('scroll', listener);

    return () => window.removeEventListener('scroll', listener);
  }, []);

  return (
    <div
      className={styles.bottom}
      style={{
        height: `${styleHeight}px`,
        padding: isIOS ? '10px 16px 36px' : '10px 16px 16px',
        bottom: visible ? 0 : `-${styleHeight}px`,
      }}
    >
      <ButtonGroup data={buttonGroup} size="adaptive" type="primary" />
    </div>
  );
}
