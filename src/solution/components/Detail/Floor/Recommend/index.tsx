import React from 'react';
import _ from 'lodash';
import SolutionCard from '@/solution/components/Card/SolutionCard';
import { SolutionCardItem } from '@/solution/utils/dataEngine/dataSchema';
import styles from './index.module.scss';
import { isServer } from '@/help-fe-common/utils/node/getContext';
import classNames from 'classnames';

interface RecommendProps {
  data?: SolutionCardItem[];
}

export default function Recommend({ data }: RecommendProps) {
  if (isServer()) {
    return (
      <div className="aliyun-solution-recommend" />
    );
  }

  return (
    <div className={classNames('aliyun-solution-recommend', styles.container)}>
      {_.map(data, (item: SolutionCardItem) => {
        return (
          <SolutionCard solutionData={item} />
        );
      })}
    </div>
  );
}

