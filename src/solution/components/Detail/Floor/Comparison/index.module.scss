.comparisonContainer {
  .comparisonTable {
    width: 100%;
    border: 1px dashed #A6A6A6;
    border-collapse: collapse;
    table-layout: fixed;

    .left {
      width: 50%;
    }

    .contrast {
      width: 116px;
    }

    .right {
      width: 50%;
    }

    .head {
      width: 100%;
      height: 62px;
      font-size: 24px;
      font-weight: 500;
      text-align: center;
      letter-spacing: 0.73px;

      td {
        height: 62px;
      }

      .left {
        background-color: #F3F3F3;
        color: #181818;
      }

      .contrast {
        background-color: #A6BFFF;
        color: #fff;
        font-size: 35px;
        letter-spacing: 1.09px;
        position: relative;

        &::after {
          content: "";
          width: 0;
          height: 0;
          border-left: 58px solid transparent;
          border-right: 58px solid transparent;
          border-top: 19px solid #A6BFFF;
          position: absolute;
          top: 61px;
          left: 0;
          z-index: 1;
        }
      }

      .right {
        background-color: #1366ec;
        color: #fff;
      }
    }

    .tableItem {
      width: 100%;
      height: 162px;

      &:not(:last-child) {

        .left,
        .right,
        .contrast {
          position: relative;

          &::after {
            content: '';
            height: 2px;
            border-bottom: 1.94px dashed #D4D6DB;
            width: calc(100% - 24px);
            position: absolute;
            bottom: 0;
          }
        }

        .left::after {
          right: 0;
        }

        .contrast::after {
          left: 0;
          width: 114px;
        }

        .right::after {
          left: 0;
        }
      }

      .left {
        background-color: rgba(243, 243, 243, 0.3);
        vertical-align: baseline;
      }

      .contrast {
        background-color: #EBF1FF;
        font-size: 20px;
        font-weight: 600;
        line-height: 30.98px;
        text-align: center;
        letter-spacing: 0.61px;
        color: #1366EC;
      }

      .right {
        background-color: rgba(209, 221, 255, 0.2);
        vertical-align: baseline;
      }

      .itemCell {
        padding: 40px 24px 30px 24px;
        list-style: none;

        .title {
          font-size: 20px;
          font-weight: 500;
          line-height: 30px;
          margin-bottom: 8px;

          i {
            color: #34A853;
            font-size: 17px;
            font-weight: 800;
            margin-right: 13px;
          }
        }

        .desc {
          font-size: 14px;
          line-height: 27px;
          letter-spacing: 0.65px;
          color: #999;
        }
      }
    }
  }
}

@media screen and (max-width: 1055px) {
  .comparisonContainer {
    .comparisonTable {
      .contrast {
        width: 36px;
      }

      .head {
        height: 48px;
        font-size: 16px;
        font-weight: 500;

        td {
          height: 48px;
        }

        .contrast {
          font-size: 16px;
          font-weight: bold;

          &::after {
            top: 47px;
            border-left-width: 18px;
            border-right-width: 18px;
            border-top-width: 6px;
          }
        }
      }

      .tableItem {
        &:not(:last-child) {

          .left,
          .right,
          .contrast {
            &::after {
              width: calc(100% - 6px);
            }
          }
        }

        .contrast {
          font-size: 12px;
          line-height: 22px;
          padding: 0 12px;
        }

        .itemCell {
          padding: 16px 12px;

          .title {
            font-size: 14px;
            font-weight: 500;
            line-height: 22px;
            color: #262626;

            i {
              font-size: 10px;
              margin-right: 6px;
            }
          }

          .desc {
            font-size: 12px;
            font-weight: normal;
            line-height: 20px;
            color: #81848f;
          }
        }
      }
    }
  }
}
