import React from 'react';
import { ComparisonData } from '@/solution/utils/dataEngine/dataSchema';
import styles from './index.module.scss';

interface IProps {
  data: ComparisonData;
}

const Comparison = ({ data }: IProps) => {
  const { headData, comparisonList } = data;

  const renderTableCell = (itemInfo, type) => {
    return (
      <li className={styles.itemCell}>
        <div className={styles.title}>
          {type === 'right' && <i className="help-iconfont help-icon-duigou1" />}
          <b>{itemInfo?.title}</b>
        </div>
        <p className={styles.desc}>{itemInfo?.desc}</p>
      </li>
    );
  };

  return (
    <div className={styles.comparisonContainer}>
      {
        data &&
        <table className={styles.comparisonTable}>
          <thead className={styles.head}>
            <td className={styles.left}><b>{headData?.leftTitle}</b></td>
            <td className={styles.contrast}>VS</td>
            <td className={styles.right}><b>{headData?.rightTitle}</b></td>
          </thead>
          {
            comparisonList?.map((comparisonItem, index) => {
              return (
                <tr className={styles.tableItem} key={index}>
                  <td className={styles.left}>{renderTableCell(comparisonItem?.leftInfo, 'left')}</td>
                  <td className={styles.contrast}>{comparisonItem?.comparisonAttribute}</td>
                  <td className={styles.right}>{renderTableCell(comparisonItem?.rightInfo, 'right')}</td>
                </tr>
              );
            })
          }
        </table>
      }
    </div>
  );
};
export default Comparison;
