.headContainer {
  padding: 0 24px;
  margin-bottom: 40px;

  .anchorTitle {
    font-size: 12px;
    font-weight: 600;
    line-height: 17px;
    color: #999;
    margin-bottom: 8px;
    scroll-margin: 80px;

    &:empty {
      display: none;
    }
  }

  .h2Title {
    font-size: 26px;
    font-weight: 600;
    line-height: 40px;
  }

  .h3Title {
    font-size: 22px;
    font-weight: 600;
    line-height: 40px;
    margin-bottom: 10px;
  }

  .desc {
    font-size: 14px;
    line-height: 24px;

    &:empty {
      display: none;
    }

    a {
      color: #1366ec;
    }
  }
}



@media screen and (max-width: 1055px) {
  .headContainer {
    padding: 0;
    margin-bottom: 20px;

    .h2Title {
      font-size: 24px;
      line-height: 37px;
    }

    .h3Title {
      font-size: 20px;
      font-weight: 500;
      line-height: 30px;
      margin-bottom: 16px;
    }

    .desc {
      font-size: 14px;
      line-height: 22px;
      color: #81848F;
    }
  }
}