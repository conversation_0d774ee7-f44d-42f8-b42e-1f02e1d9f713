import React from 'react';
import { AnchorItemData } from '@/solution/utils/dataEngine/dataSchema';
import styles from './index.module.scss';

interface IProps {
  anchorData?: AnchorItemData;
  title?: string;
  titleType?: string;
  desc?: string;
}

const FloorHead = ({ anchorData, title, titleType, desc }: IProps) => {
  return (
    <div className={styles.headContainer}>
      <div className={styles.anchorTitle} id={anchorData?.id}>{anchorData?.text}</div>
      <h2 className={titleType === 'h3' ? styles.h3Title : styles.h2Title}>{title}</h2>
      {
        desc && <p className={styles.desc} dangerouslySetInnerHTML={{ __html: desc }} />
      }
    </div>
  );
};

export default FloorHead;
