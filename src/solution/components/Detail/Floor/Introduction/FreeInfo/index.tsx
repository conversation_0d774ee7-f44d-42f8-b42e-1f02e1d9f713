import React from 'react';
import ButtonGroup from '@/solution/components/Common/ButtonGroup';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import { getEnv, ENV } from '@/help-fe-common/utils/global/env';
import styles from './index.module.scss';

const FreeInfo = ({ solutionInfo }) => {
  const {
    solutionDesc,
    solutionTime,
    solutionCost,
    tokenBalance,
    buttonGroup,
    relatedProductList } = solutionInfo;
  return (
    <div className={styles.infoContainer}>
      <div className={styles.info}>
        <p className={styles.desc}>{solutionDesc}</p>
        <div className={styles.relatedProduct}>
          <div className={styles.title}><b>相关云产品</b></div>
          <div className={styles.iconContainer}>
            {
              relatedProductList?.map((productItem, index) => (
                <li className={styles.item} key={index}>
                  <a
                    href={productItem?.productUrl}
                    onClick={() => {
                      sendSlsLog({
                        page: 'solutionDetail',
                        section: 'introduction',
                        action: 'click',
                        userParams1: productItem?.productName,
                        userParams2: productItem?.productUrl,
                      });
                    }}
                    className={styles.productUrl}
                    target="_blank"
                    rel="noreferrer"
                  >
                    <i className={`dbl-icon-product-${productItem?.pipCode} dbl-icon-product-aliyun`} />
                    <span title={productItem?.productName}>{productItem?.productName}</span>
                  </a>
                </li>
              ))
            }
          </div>
        </div>
      </div>
      <div className={styles.content}>
        <div className={styles.title}>
          <h4>免费试用</h4>
          <span>消耗试用点进行部署体验。
            <a
              href={getEnv() === ENV.PRE ? 'https://pre-www.aliyun.com/solution/free' : 'https://www.aliyun.com/solution/free'}
              target="_blank"
              rel=" noreferrer"
            >如何获取试用点
            </a>
          </span>
        </div>
        <div className={styles.cost}>
          <span>{solutionCost?.cost} 试用点</span>起/小时
        </div>
        <div className={styles.time}><b>建议试用时长：</b>{solutionTime} 小时</div>
        <div className={styles.time}><b>账户余额：</b>{tokenBalance === null ? '-' : tokenBalance} 试用点</div>
      </div>
      <ButtonGroup data={buttonGroup} size="large" />
    </div>
  );
};
export default FreeInfo;
