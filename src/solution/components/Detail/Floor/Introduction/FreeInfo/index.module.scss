.infoContainer {
  flex: 1;
  padding: 30px 24px;
  font-size: 14px;
  border: 1px solid #D3D3D3;
  border-left: none;
  background-color: #FCFCFC;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .desc {
    line-height: 27px;
    margin-bottom: 20px;
  }

  .info {
    padding-bottom: 32px;
    margin-bottom: 32px;
    border-bottom: 1px solid #E8E8E8;
  }

  .relatedProduct {

    .title {
      height: 27px;
      line-height: 27px;
      margin-bottom: 12px
    }

    .iconContainer {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .item {
        height: 27px;
        line-height: 27px;
        list-style: none;

        .productUrl {
          height: 27px;
          display: flex;
          padding: 4px 8px;
          box-sizing: border-box;
          font-size: 12px;
          line-height: 18px;
          color: #717171;
          border: 1px solid #EDEDED;

          &:hover {
            color: #1366ec;
            background-image: linear-gradient(253deg, rgba(55, 0, 255, .05) 0%, rgba(0, 98, 255, .05) 100%);

            i {
              color: #1366ec;
            }
          }

          i {
            font-size: 18px;
            font-style: normal;
            color: #81848F;
            margin-right: 8px;
          }
        }
      }
    }
  }

  .content {
    margin-bottom: 24px;

    .title {
      margin-bottom: 20px;

      h4 {
        font-size: 18px;
        font-weight: 600;
        line-height: 28px;
      }

      span {
        font-size: 14px;
        line-height: 24px;
        color: #999999;

        a {
          color: #1366ec;
        }
      }
    }

    .cost {
      line-height: 27px;
      font-weight: 600;
      color: #ff6a00;

      span {
        font-size: 18px;
        margin-right: 8px;
      }
    }

    .time {
      line-height: 24px;
    }

    .cost,
    .time {
      margin-bottom: 4px;
    }
  }
}

@media screen and (max-width: 1055px) {
  .infoContainer {
    border-left: 1px solid #e3e3e3;
    border-top: none;
    padding: 20px 16px;

    .info {
      padding-bottom: 10px;
      margin-bottom: 20px;
    }

    .relatedProduct {
      margin-bottom: 20px;

      .iconContainer {

        .item {
          margin-bottom: 0;
          height: auto;
          line-height: 24px;
        }
      }
    }
  }
}