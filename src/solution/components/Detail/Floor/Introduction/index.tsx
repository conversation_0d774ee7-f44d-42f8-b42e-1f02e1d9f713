import React, { useEffect, useMemo, useState } from 'react';
import { useRequest } from 'ice';
import ButtonGroup from '@/solution/components/Common/ButtonGroup';
import MediaViewer from '@/solution/components/Common/MediaViewer';
import services from '@/solution/services';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import { SolutionIntroduction, ButtonItemData } from '@/solution/utils/dataEngine/dataSchema';
import styles from './index.module.scss';
import FreeInfo from './FreeInfo';

interface IProps {
  data: SolutionIntroduction;
}

enum SolutionCardTypeEnum {
  SOLUTION_INTRODUCTION = 0,
  SOLUTION_FREE_TRY = 1,
}

const getSolutionDeployInfo = (solutionInfo) => {
  const {
    brief: solutionDesc,
    relatedProducts,
    freeExperimentCostInHour,
    freeDefaultExperienceTime: solutionTime,
    tokenBalance,
    freeUrl,
    cpqShareLink,
  } = solutionInfo;

  const solutionCost = {
    cost: freeExperimentCostInHour,
    desc: '',
  };
  const buttonGroup: ButtonItemData[] = [
    {
      text: '免费试用',
      url: freeUrl,
    },
  ];
  if (cpqShareLink) {
    buttonGroup.push({
      text: '配置购买',
      url: cpqShareLink,
    });
  }
  const relatedProductList: Array<{
    pipCode: string;
    productName: string;
    productUrl: string;
  }> = relatedProducts.map((item) => ({
    pipCode: item?.cloudCode,
    productName: item?.name,
    productUrl: item?.url,
  }));

  return {
    solutionDesc, solutionTime, solutionCost, tokenBalance, buttonGroup, relatedProductList,
  };
};

const Introduction = ({ data }: IProps) => {
  const [shownSolutionInfo, setShownSolutionInfo] = useState(data);
  const [showType, setShowType] = useState(SolutionCardTypeEnum.SOLUTION_INTRODUCTION);

  const {
    solutionImg,
    solutionDiagramId,
    solutionVideo,
    solutionDesc,
    solutionTime,
    solutionCost,
    buttonGroup,
    relatedProductList,
    solutionDeployId,
  } = useMemo(() => {
    return shownSolutionInfo;
  }, [shownSolutionInfo]);

  const { request: getSolutionIntroductionInfo } = useRequest(services.getSolutionIntroductionInfo, { manual: true });

  useEffect(() => {
    if (!relatedProductList) return;
    window.AIAssistant = window.AIAssistant || {};
    window.AIAssistant.settings = {
      promptContext: {
        productCode: relatedProductList?.map((item) => item?.pipCode)?.join(','),
        extra: {
          articleId: window?.globalData?.nodeId,
        },
      },
    };
  }, [relatedProductList]);

  useEffect(() => {
    if (!solutionDeployId) return;
    // 获取全部解决方案免费试用信息
    getSolutionIntroductionInfo({ nodeIdList: [solutionDeployId] }).then((list) => {
      const solutionInfo = list?.length > 0 && list?.[0];
      if (solutionInfo?.freeModeEnabled) {
        setShowType(SolutionCardTypeEnum.SOLUTION_FREE_TRY);
        const solutionDeployData = getSolutionDeployInfo(solutionInfo);
        setShownSolutionInfo({ ...shownSolutionInfo, ...solutionDeployData });
      }
    });
  }, [solutionDeployId]);

  return (
    <div className={styles.introductionContainer}>
      <div className={styles.imgContainer}>
        <MediaViewer imgSrc={solutionImg} diagramId={solutionDiagramId} videoSrc={solutionVideo} />
      </div>
      {
        showType === SolutionCardTypeEnum.SOLUTION_FREE_TRY ?
          <FreeInfo solutionInfo={shownSolutionInfo} />
          :
          <div className={styles.infoContainer}>
            <div className={styles.info}>
              <p className={styles.desc}>{solutionDesc}</p>
              <div className={styles.time}><b>部署时长：</b>{solutionTime}</div>
              <div className={styles.cost}>
                <b>预估费用：</b>{solutionCost?.cost}
                <span>{solutionCost?.desc}</span>
              </div>
            </div>
            <div className={styles.relatedProduct}>
              <div className={styles.title}><b>相关云产品</b></div>
              <div className={styles.iconContainer}>
                {
                  relatedProductList?.map((productItem, index) => (
                    <li className={styles.item} key={index}>
                      <a
                        href={productItem?.productUrl}
                        onClick={() => {
                          sendSlsLog({
                            page: 'solutionDetail',
                            section: 'introduction',
                            action: 'click',
                            userParams1: productItem?.productName,
                            userParams2: productItem?.productUrl,
                          });
                        }}
                        className={styles.productUrl}
                        target="_blank"
                        rel="noreferrer"
                      >
                        <i className={`dbl-icon-product-${productItem?.pipCode} dbl-icon-product-aliyun`} />
                        <span title={productItem?.productName}>{productItem?.productName}</span>
                      </a>
                    </li>
                  ))
                }
              </div>

            </div>
            <ButtonGroup data={buttonGroup} size="large" />
          </div>
      }
    </div>
  );
};
export default Introduction;
