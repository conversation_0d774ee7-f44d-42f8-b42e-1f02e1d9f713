.introductionContainer {
  display: flex;

  .imgContainer {
    width: calc(50% - 58px);
    background-color: #f3f3f3;
  }

  .infoContainer {
    flex: 1;
    padding: 30px 24px;
    font-size: 14px;
    border: 1px solid #D3D3D3;
    border-left: none;
    background-color: #FCFCFC;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .desc {
      line-height: 27px;
      color: #999;
      margin-bottom: 20px;
    }

    .info {
      padding-bottom: 30px;
      margin-bottom: 30px;
      border-bottom: 1px solid #E8E8E8;

      .cost {
        line-height: 27px;

        span {
          color: #999;
        }
      }

      .time,
      .cost {
        margin-bottom: 10px;
      }
    }

    .relatedProduct {
      margin-bottom: 40px;

      .title {
        height: 27px;
        line-height: 27px;
        margin-bottom: 12px
      }

      .iconContainer {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .item {
          height: 27px;
          line-height: 27px;
          list-style: none;

          .productUrl {
            height: 27px;
            display: flex;
            padding: 4px 8px;
            box-sizing: border-box;
            font-size: 12px;
            line-height: 18px;
            color: #717171;
            border: 1px solid #EDEDED;

            &:hover {
              color: #1366ec;
              background-image: linear-gradient(253deg, rgba(55, 0, 255, .05) 0%, rgba(0, 98, 255, .05) 100%);

              i {
                color: #1366ec;
              }
            }

            i {
              font-size: 18px;
              font-style: normal;
              color: #81848F;
              margin-right: 8px;
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: 1055px) {
  .introductionContainer {
    flex-direction: column;
    background-color: #fcfcfc;

    .imgContainer {
      width: 100%;
      padding: 7px;
    }

    .infoContainer {
      border-left: 1px solid #e3e3e3;
      border-top: none;
      padding: 20px 16px;

      .desc {
        color: #81848F;
      }

      .info {
        padding-bottom: 10px;
        margin-bottom: 20px;

        .cost {
          span {
            color: #81848F;
          }
        }
      }

      .relatedProduct {
        margin-bottom: 20px;

        .iconContainer {

          .item {
            margin-bottom: 0;
            height: auto;
            line-height: 24px;
          }
        }
      }
    }
  }
}