.main {
  height: 212px;
  margin-top: 60px;
  background-position: left center;
  background-repeat: no-repeat;
  background-size: auto 212px;
  position: relative;
  top: -17px;
}

.infoDiv {
  padding: 17px 0 0 400px;
  height: 212px;
}

.info {
  display: block;
  padding: 40px 0 20px;
  text-decoration: none;

  h2 {
    font-size: 26px;
    font-weight: 600;
    line-height: 36px;
    color: #fff;
    margin: 0;
  }

  p {
    margin-top: 14px;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    color: #fff;
  }

  span {
    margin-top: 25px;
    font-size: 14px;
    font-weight: 600;
    line-height: 30px;
    text-decoration: none;
    display: block;
    color: #fff
  }

  &:hover {
    span {
      text-decoration: underline;
    }
  }
}

@media screen and (max-width: 1055px) {
  .main {
    height: 220px;
    margin: 20px -16px 0;
    background-size: auto 150px;
    background-position: right -750px bottom;
    top: 0;
  }

  .infoDiv {
    padding: 32px 16px;
    height: 220px;
  }

  .info {
    padding: 0;

    h2 {
      font-size: 24px;
      line-height: 34px;
    }

    p {
      font-weight: 400;
    }
  }
}