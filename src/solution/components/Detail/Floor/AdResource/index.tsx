import React from 'react';
import styles from './index.module.scss';

export default function AdResource({ data }) {
  const { adTitle, adDesc, adButton, adImg } = data;

  return (
    <div className={styles.main} style={{ backgroundImage: `url(${adImg})` }}>
      <div className={styles.infoDiv}>
        <a href={adButton.url} target="_blank" className={styles.info}>
          <h2>{adTitle}</h2>
          <p>{adDesc}</p>
          {adButton && <span>{adButton.text}{' >'}</span>}
        </a>
      </div>
    </div>
  );
};
