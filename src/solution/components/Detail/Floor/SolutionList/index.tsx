import React from 'react';
import _ from 'lodash';
import SolutionCard from '@/solution/components/Card/SolutionCard';
import { SolutionCardItem } from '@/solution/utils/dataEngine/dataSchema';
import styles from './index.module.scss';

interface IProps {
  data: SolutionCardItem[];
}

export default function SolutionList({ data }: IProps) {
  return (
    <div className={styles.main}>
      <div className={styles.list}>
        {_.map(data, (item: SolutionCardItem) => {
          return (
            <SolutionCard solutionData={item} />
          );
        })}
      </div>
    </div>
  );
}

