.floorContainer {
  padding-top: 40px;

  .floorLink {
    padding: 32px 0;

    a {
      padding-right: 46px;
      font-weight: 500;
      font-size: 14px;
      line-height: 21px;
      color: #1366ec;
      position: relative;

      &:hover {
        color: #0f52bd;
      }

      i {
        display: inline-block;
        width: 14px;
        height: 14px;
        margin-left: 8px;
        border-radius: 7px;
        font-size: 12px;
        text-align: center;
        line-height: 14px;
        color: #fff;
        background-color: #1366ec;
        transition: width .5s;
      }
    }
  }
}

.adBack {
  background: linear-gradient(90deg, #1261E1 100%, #1261E1 100%);
  padding-top: 0;
  height: 195px;
}

@media screen and (max-width: 1055px) {
  .floorContainer {
    padding-top: 20px;

    .floorLink {
      padding: 25px 0;

    }
  }

  .adBack {
    height: 220px;
    padding-top: 0;
  }
}