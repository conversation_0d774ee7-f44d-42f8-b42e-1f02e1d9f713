import React from 'react';
import styles from './index.module.scss';
import FreeCard from '@/solution/components/Card/FreeCard';
import { FreeCardItem } from '@/solution/utils/dataEngine/dataSchema';

interface FreeCardsProps {
  data?: FreeCardItem[];
}

export default function ({ data }: FreeCardsProps) {
  if (!data || data.length === 0) {
    return <></>;
  }

  return (
    <div
      className={styles.list}
      style={{
        // @ts-ignore
        '--card-count': data.length >= 4 ? 4 : data.length,
      }}
    >
      {data.map((item) => (
        item?.freeId ? <FreeCard goodsId={item?.freeId} /> : null
      ))}
    </div>
  );
}
