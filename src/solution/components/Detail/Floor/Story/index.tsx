import React from 'react';
import styles from './index.module.scss';
import _ from 'lodash';

export default function CustomerPlanCard() {
  const data = [
    {
      logo: 'https://img.alicdn.com/imgextra/i3/O1CN01JLehmW1GnX8cEh5EX_!!6000000000667-2-tps-339-75.png',
      img: 'https://img.alicdn.com/imgextra/i1/O1CN01iTg0J823isHXtYyxK_!!6000000007290-0-tps-2290-888.jpg',
      desc: '“因为使用了阿里云RDS MySQL这产品，让我们实现了纵向弹性扩缩容，让我们实现了，并降低了我们的运维成本。”',
    },
    {
      logo: 'https://img.alicdn.com/imgextra/i4/O1CN01Ku6ToP1jIV9xzNlhJ_!!6000000004525-2-tps-407-105.png',
      img: 'https://img.alicdn.com/imgextra/i1/O1CN01iTg0J823isHXtYyxK_!!6000000007290-0-tps-2290-888.jpg',
      desc: '“因为使用了阿里云RDS MySQL这产品，让我们实现了纵向弹性扩缩容，让我们实现了，并降低了我们的运维成本。”',
    },
    {
      logo: 'https://img.alicdn.com/imgextra/i4/O1CN01Ku6ToP1jIV9xzNlhJ_!!6000000004525-2-tps-407-105.png',
      img: 'https://img.alicdn.com/imgextra/i1/O1CN01iTg0J823isHXtYyxK_!!6000000007290-0-tps-2290-888.jpg',
      desc: '“因为使用了阿里云RDS MySQL这产品，让我们实现了纵向弹性扩缩容，让我们实现了，并降低了我们的运维成本。”',
    },
    {
      logo: 'https://img.alicdn.com/imgextra/i4/O1CN01Ku6ToP1jIV9xzNlhJ_!!6000000004525-2-tps-407-105.png',
      img: 'https://img.alicdn.com/imgextra/i1/O1CN01iTg0J823isHXtYyxK_!!6000000007290-0-tps-2290-888.jpg',
      desc: '“因为使用了阿里云RDS MySQL这产品，让我们实现了纵向弹性扩缩容，让我们实现了，并降低了我们的运维成本。”',
    },
    {
      logo: 'https://img.alicdn.com/imgextra/i4/O1CN01Ku6ToP1jIV9xzNlhJ_!!6000000004525-2-tps-407-105.png',
      img: 'https://img.alicdn.com/imgextra/i1/O1CN01iTg0J823isHXtYyxK_!!6000000007290-0-tps-2290-888.jpg',
      desc: '“因为使用了阿里云RDS MySQL这产品，让我们实现了纵向弹性扩缩容，让我们实现了，并降低了我们的运维成本。”',
    },
  ];

  const [firstData, ...otherData] = data || [];

  if (!firstData) {
    return <></>;
  }

  return (
    <div className={styles.container}>
      <div className={styles.left} style={{ width: data.length <= 2 ? '50%' : undefined }}>
        <img alt="" className={styles.logo} src={firstData.logo} loading="lazy" />
        <img alt="" className={styles.img} src={firstData.img} loading="lazy" />
        {otherData.length > 0 ? <p>{firstData.desc}</p> : ''}
      </div>
      <div className={styles.right}>
        {otherData.length > 0 ? (
          <div className={styles.list}>
            {_.map(otherData, (item) => {
              const { logo, img, desc } = item;
              return (
                <div className={styles.item}>
                  <div>
                    <img alt="" className={styles.logo} src={logo} loading="lazy" />
                    <img alt="" className={styles.img} src={img} loading="lazy" />
                  </div>
                  <p>{desc}</p>
                </div>
              );
            })}
          </div>
        ) : (
          <div className={styles.onceDesc}>
            <p>{firstData.desc}</p>
          </div>
        )}
      </div>
    </div>
  );
}

