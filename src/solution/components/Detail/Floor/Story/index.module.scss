.container {
  display: flex;
  min-height: 350px;
  overflow: hidden;
}

.left {
  width: 42.8%;
  flex: none;
  padding: 45px 24px 24px;
  background-color: #1366ec;

  p {
    font-size: 14px;
    font-weight: 500;
    line-height: 24px;
    color: #FFFFFF;
  }
}

.right {
  flex: auto;
  padding: 24px;
  background-color: #ECECEE;
}

.logo {
  width: 135px;
}

.img {
  width: 100%;
  margin: 26px 0;
  max-height: 230px;
  object-fit: cover;
}

.list {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  height: 100%;
}

.item {
  width: 100%;
  flex: auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 32px 24px 20px;
  background-color: #fff;

  .img {
    display: none;
  }

  @media screen and (min-width: 1056px) {
    &:first-child:nth-last-child(1) {
      max-width: 100%;

      .img {
        display: block;
        width: 100%;
        margin-top: 20px;
        object-fit: cover;
        max-height: 100%;
      }
    }

    &:first-child:nth-last-child(2), &:first-child:nth-last-child(2) ~ .item {
      max-width: calc(50% - 12px);
    }

    &:first-child:nth-last-child(3),&:first-child:nth-last-child(3) ~ .item {
      max-width: calc(33.33% - 16px);
    }

    &:first-child:nth-last-child(4),&:first-child:nth-last-child(4) ~ .item {
      max-width: calc(50% - 12px);
    }
  }

  p {
    font-size: 14px;
    font-weight: normal;
    line-height: 24px;
    color: #808080;
    margin-top: 20px;
  }
}

.onceDesc{
  margin-top: 96px;

  p {
    font-size: 14px;
    font-weight: normal;
    line-height: 24px;
    color: #808080;
  }
}

@media screen and (max-width: 1055px) {
  .container {
    flex-direction: column;
  }

  .left {
    width: 100%;
    padding: 16px;
  }

  .list {
    gap: 8px;
  }

  .right {
    padding: 8px;
  }

  .logo {
    width: 72px;
  }

  .img {
    margin: 16px 0;
  }

  .item {
    padding: 24px 16px;

    >div{
      display: flex;
      flex-direction: column;

      .logo {
        width: 135px;
        align-self: center;
      }
    }
  }
}


