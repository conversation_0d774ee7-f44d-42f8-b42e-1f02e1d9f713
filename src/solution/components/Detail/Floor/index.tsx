import React, { useMemo } from 'react';
import classNames from 'classnames';
import FloorHead from './FloorHead';
import FloorContent from './FloorContent';
import { BaseFloorData } from '@/solution/utils/dataEngine/dataSchema';
import { ComponentTypeEnum } from '@/solution/constants';
import styles from './index.module.scss';

interface IProps {
  floorData: BaseFloorData;
  style?: React.CSSProperties;
}

const Floor = ({ floorData, style }: IProps) => {
  const { anchor, title, desc, titleType, data: componentData, link } = useMemo(() => {
    return floorData || {} as any;
  }, [floorData]);

  return (
    <div
      className={classNames('col-extend-left', 'col-extend-right', styles.floorContainer,
        componentData?.componentType === ComponentTypeEnum.AD_RESOURCE ? styles.adBack : null)}
      style={style}
    >
      {
        title &&
        <FloorHead
          anchorData={anchor}
          title={title}
          desc={desc}
          titleType={titleType}
        />}
      <div className={styles.floorContent}>
        <FloorContent data={componentData} />
      </div>
      {
        link?.url &&
        <div className={styles.floorLink}>
          <a href={link?.url} target="_blank" rel="noreferrer">
            {link?.text}
            <i className="help-iconfont help-icon-right-arrow" />
          </a>
        </div>
      }
    </div>
  );
};

export default Floor;
