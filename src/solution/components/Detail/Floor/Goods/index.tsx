import React from 'react';
import _ from 'lodash';
import { ProductCardItem } from '@/solution/utils/dataEngine/dataSchema';
import HomonSection from '@/solution/components/Common/HomonSection';

interface GoodsCardProps {
  data?: ProductCardItem[];
}

export default function ({
  data,
}: GoodsCardProps) {
  if (!data) {
    return <></>;
  }

  const goodIds = _.map(data, ((item: ProductCardItem) => item.goodsId))
    .filter((item) => item);
  if (!goodIds.length) {
    return <></>;
  }

  return (
    <HomonSection
      className="help-solution-goods-container"
      pagePath="/help/module-page/tech-solution-goods-card"
      data-ids={goodIds.join(',')}
    />
  );
}
