import React from 'react';
import _ from 'lodash';
import OverviewCard from '@/solution/components/Card/OverviewCard';
import { OverviewCardItem } from '@/solution/utils/dataEngine/dataSchema';
import styles from './index.module.scss';

interface IProps {
  data: OverviewCardItem[];
}

export default function OverviewList({ data }: IProps) {
  return (
    <div className={styles.main}>
      <div className={styles.list}>
        {_.map(data, (item: OverviewCardItem, index) => {
          return (
            <OverviewCard cardData={item} key={index} />
          );
        })}
      </div>
    </div>
  );
}

