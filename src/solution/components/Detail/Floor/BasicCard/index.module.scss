.basicCard {
  display: flex;
  border: 1px solid #E9E9E9;
  box-shadow: 0 4px 6px 0 rgba(0, 0, 0, 0.03);

  .basicCardImg {
    width: 50%;
    flex: none;
    background-color: #f3f3f3;
    position: relative;

    img,
    video {
      max-width: calc(100% - 48px);
      height: calc(100% - 48px);
      object-fit: contain;
      cursor: zoom-in;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    // 视频占满容器
    video {
      max-width: 100%;
      height: 100%;
    }
  }

  .basicCardContentDiv {
    flex: auto;
    padding: 24px;
    background-color: #fff;
    max-height: 680px;
    min-height: 280px;
    display: flex;
    flex-direction: column;

    .basicCardContent {
      flex: 1;
      overflow: auto;
      font-size: 14px;
      color: #181818;
      font-weight: 400;
      line-height: 27px;
      padding-right: 8px;
      scrollbar-width: 6px; // firefox滚动条

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #d8d8d8;
        border-radius: 3px;
      }

      >*:first-child {
        margin-top: 0;
      }

      h3 {
        font-size: 18px;
        font-weight: 500;
        margin: 20px 0 10px 0;
      }

      p {
        line-height: 27px;
      }

      ul,
      ol {
        padding-left: 16px;
        margin: 10px 0;
      }

      ul {
        list-style: disc;

        li::marker {
          font-size: 12px;
        }
      }

      ol {
        list-style: decimal;
      }

      a {
        color: #1366ec;
      }
    }

    .basicCardButtonGroup {
      height: 44px;
      margin-top: 40px;
    }
  }
}

@media screen and (max-width: 1055px) {
  .basicCard {
    flex-direction: column;

    .basicCardImg {
      width: 100%;

      img,
      video {
        height: auto;
        position: relative;
        top: 0;
        left: 0;
        transform: none;
      }
    }

    .basicCardContentDiv {
      min-height: auto;
      border-left: 1px solid #E9E9E9;
      border-top: none;
      padding: 16px;

      .basicCardContent {
        margin-bottom: 20px;
        line-height: 22px;

        >*:first-child {
          margin-top: 0;
        }

        h3 {
          font-size: 16px;
          margin: 16px 0 8px 0;
        }

        p {
          line-height: 22px;
        }

        ul,
        ol {
          padding-left: 14px;
          margin: 8px 0;
        }
      }
    }
  }
}