import React, { useEffect, useRef } from 'react';
import { BasicCardItem } from '@/solution/utils/dataEngine/dataSchema';
import MediaViewer from '@/solution/components/Common/MediaViewer';
import ButtonGroup from '@/solution/components/Common/ButtonGroup';
import styles from './index.module.scss';

interface BasicCardProps {
  data: BasicCardItem;
}

export default function BasicCard({ data }: BasicCardProps) {
  const { img, solutionDiagramId, video, content, buttonGroup } = data;
  const ref = useRef<any>();

  useEffect(() => {
    if (!ref?.current) return;
    const aNodeList = ref?.current?.querySelectorAll('a');
    aNodeList && aNodeList?.forEach((aNode: HTMLElement) => {
      aNode?.setAttribute('target', '_blank');
    });
  }, [ref?.current]);

  return (
    <div className={styles.basicCard}>
      <div className={styles.basicCardImg}>
        <MediaViewer imgSrc={img} diagramId={solutionDiagramId} videoSrc={video} />
      </div>
      <div className={styles.basicCardContentDiv}>
        <div
          ref={ref}
          className={styles.basicCardContent}
          dangerouslySetInnerHTML={{ __html: content }}
        />
        {
          buttonGroup?.length > 0 &&
          <div className={styles.basicCardButtonGroup}>
            <ButtonGroup data={buttonGroup} size="large" />
          </div>
        }
      </div>
    </div>
  );
}
