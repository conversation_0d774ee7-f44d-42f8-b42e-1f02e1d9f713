import React from 'react';
import Tab from '@/solution/components/Common/Tab';
import Advantage from '../Advantage';
import Comparison from '../Comparison';
import Introduction from '../Introduction';
import AdResource from '../AdResource';
import SolutionList from '../SolutionList';
import OverviewList from '../OverviewList';
import BasicCard from '../BasicCard';
import FreeCards from '../FreeCards';
import Story from '../Story';
import Recommend from '../Recommend';
import Goods from '../Goods';
import InfoGroup from '@/solution/components/Common/InfoGroup';

import { ComponentTypeEnum } from '@/solution/constants';
import { ComponentData } from '@/solution/utils/dataEngine/dataSchema';

interface IProps {
  data: ComponentData;
}

const FloorContent = ({ data }: IProps) => {
  const {
    componentType,
    advantageList,
    comparisonData,
    solutionIntroduction,
    tabList,
    basicCard,
    solutionCardList,
    overviewCardList,
    adResource,
    productCardList,
    freeCardList,
    recommendSolutionList,
    infoList } = data;

  const renderComponent = () => {
    switch (componentType) {
      case ComponentTypeEnum.ADVANTAGE:
        return (
          advantageList && <Advantage data={advantageList} />
        );
      case ComponentTypeEnum.COMPARISON:
        return (
          comparisonData && <Comparison data={comparisonData} />
        );
      case ComponentTypeEnum.SOLUTION:
        return (
          solutionIntroduction && <Introduction data={solutionIntroduction} />
        );
      case ComponentTypeEnum.AD_RESOURCE:
        return (
          adResource && <AdResource data={adResource} />
        );
      case ComponentTypeEnum.TAB:
        return (
          tabList && <Tab data={tabList} />
        );
      case ComponentTypeEnum.BASIC_CARD:
        return (
          basicCard && <BasicCard data={basicCard} />
        );
      case ComponentTypeEnum.SOLUTION_CARDS:
        return (
          solutionCardList && <SolutionList data={solutionCardList} />
        );
      case ComponentTypeEnum.OVERVIEW:
        return (
          overviewCardList && <OverviewList data={overviewCardList} />
        );
      case ComponentTypeEnum.RECOMMEND:
        return (
          recommendSolutionList && <Recommend data={recommendSolutionList} />);
      case ComponentTypeEnum.GOODS:
        return (
          <Goods data={productCardList} />
        );
      case ComponentTypeEnum.FREE:
        return <FreeCards data={freeCardList} />;
      case ComponentTypeEnum.STORY:
        return <Story />;
      case ComponentTypeEnum.INFOS:
        return (
          infoList && <InfoGroup data={infoList} />);
    }

    return (
      <div className="" />
    );
  };

  return (
    <>
      {renderComponent()}
    </>
  );
};

export default FloorContent;
