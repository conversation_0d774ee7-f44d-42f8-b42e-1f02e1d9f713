import React from 'react';
import { AdvantageItem } from '@/solution/utils/dataEngine/dataSchema';
import styles from './index.module.scss';

interface IProps {
  data?: AdvantageItem[];
}

const Advantage = ({ data }: IProps) => {
  return (
    <div className={styles.advantageContainer}>
      {
        data &&
        data?.map((advantageItem: AdvantageItem, index) => (
          <div className={styles.advantageCard} key={index} >
            {
              advantageItem?.iconImg &&
              <div className={styles.logo}>
                <img src={advantageItem?.iconImg} alt="" loading="lazy" />
              </div>
            }
            <div className={styles.content}>
              <div className={styles.cardTitle} title={advantageItem?.title}><b>{advantageItem?.title}</b></div>
              <p className={styles.cardDesc} title={advantageItem?.desc}>{advantageItem?.desc}</p>
            </div>
          </div>
        ))
      }
    </div>
  );
};
export default Advantage;
