@mixin text-line($line-number: 1) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line-number;
}

.advantageContainer {
  display: flex;
  padding-bottom: 40px;

  >*:nth-child(n+2) {
    margin-left: 24px;
  }

  .advantageCard {
    min-height: 125px;
    width: calc(20% - 24px);
    background-color: #fff;
    padding: 24px 20px 24px 24px;
    box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.03);
    flex: 1;

    .logo {
      width: 38px;
      height: 38px;
      margin-bottom: 22px;

      img {
        width: 38px;
        height: 38px;
      }
    }

    .content {
      .cardTitle {
        font-weight: 500;
        font-size: 20px;
        line-height: 24px;
        margin-bottom: 5px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .cardDesc {
        font-size: 14px;
        line-height: 24px;
        @include text-line(3);
      }
    }
  }
}

@media screen and (max-width: 1055px) {
  .advantageContainer {
    flex-direction: column;
    padding-bottom: 20px;

    >*:nth-child(n+2) {
      margin-top: 16px;
      margin-left: 0;
    }

    .advantageCard {
      min-height: auto;
      width: 100%;
      height: auto;
      padding: 16px;
      margin-right: 0;
      display: flex;

      .logo {
        flex: 0 0 42px;
        width: 42px;
        height: 42px;
        margin-bottom: 0;
        margin-right: 16px;

        img {
          width: 42px;
          height: 42px;
          position: absolute;
        }
      }

      .content {
        .cardTitle {
          font-size: 16px;
          font-weight: 500;
          line-height: 22px;
        }

        .cardDesc {
          line-height: 22px;
          color: #81848F;
        }
      }

    }
  }
}