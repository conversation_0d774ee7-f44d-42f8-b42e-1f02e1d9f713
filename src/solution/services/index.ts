import request from '@/help-fe-common/services/apiRequest';
import { ENV, getEnv } from '@/help-fe-common/utils/global/env';

export default {
  // 解决方案首页数据获取
  async getSolutionIndex(
    params: {
      website?: string;
      language?: string;
    },
    options?: { [key: string]: any },
  ) {
    const res = await request('/help/json/solution/getIndexModules.json', {
      method: 'GET',
      params,
      responseType: '*/*',
      ...(options || {}),
    });
    return res;
  },

  /**
   * 获取解决方案免费试用token信息
   * @param params { nodeIdList: string[] } 方案id列表
   * @param options
   * @returns
   */
  async getSolutionFreeToken(
    params: {},
    options?: { [key: string]: any },
  ) {
    const res = await request(`https://${getEnv() === ENV.PROD ? '' : 'pre-'}developer.aliyun.com/adc/api/skillBuilder/listSolutionTokens`, {
      method: 'GET',
      withCredentials: true,
      responseType: '*/*',
      ...(options || {}),
    });
    return res?.data;
  },

  /**
   * 获取解决方案文档方案介绍信息
   * @param params nodeList: string[] 部署文档id
   * @param options
   * @returns
   */
  async getSolutionIntroductionInfo(
    params: {
      nodeIdList: string[];
    },
    options?: { [key: string]: any },
  ) {
    const res = await request(`https://${getEnv() === ENV.PROD ? '' : 'pre-'}developer.aliyun.com/adc/api/skillBuilder/listDeployInfo`, {
      method: 'GET',
      params: {
        ids: params.nodeIdList.join(','),
      },
      withCredentials: true,
      responseType: '*/*',
      ...(options || {}),
    });
    return res?.data;
  },
};

