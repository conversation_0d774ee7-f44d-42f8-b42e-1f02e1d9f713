import { SolutionDetailModel } from '@/solution/models/SolutionDetailModel';

export async function fetchInitialData(
  urlPath: string,
  // CSR 下使用已经new 过的 Store，避免多份 Store 一直在内存中
  storeMap: {
    detailInnerStore: SolutionDetailModel;
  },
) {
  if (!urlPath) {
    return null;
  }

  const url = urlPath.indexOf('?') > -1 ? urlPath.substring(0, urlPath.indexOf('?')) : urlPath;

  const { detailInnerStore } = storeMap;

  await detailInnerStore.getDocumentDetail(url);

  return { storeData: detailInnerStore };
}
