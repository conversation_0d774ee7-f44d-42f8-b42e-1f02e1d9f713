import { getInitialData } from 'ice';
import service from '@/help-fe-common/services/apiService';
import { WEBSITE, LANG, CHANNEL } from '@/help-fe-common/utils/global/website';
import { extractUrlParam } from '@/help-fe-common/utils/global/url/urlExtract';
import solutionDataProcessor from '@/solution/utils/dataEngine/detailDataProcessor';

export class SolutionDetailModel {
  // 文档别名、id
  alias = '';
  nodeId = '';

  data: any = {};
  docContent = '';
  recommendSolutionList = [];

  // 是否显示notfound页
  isNotFound = false;

  // 响应状态码
  helpResponseCode = 200;
  redirectUrl = '';

  // 获取文档详情信息
  // page：页码，per_page：每页条数，search：搜索内容，archived 表示是否归档（也就是删除的意思）, 排序（按第一个字段排序，再按第二个字段排序）：order_by: 筛选类型, sort: 升降顺序。visibility_level：10表示公开仓库，0为私有仓库
  getDocumentDetail = async (path) => {
    const params = {
      nodeId: '',
      alias: '',
      pageNum: 1,
      pageSize: 20,
      website: WEBSITE,
      language: LANG,
      channel: CHANNEL,
    };
    const identifier = extractUrlParam(path);
    if (!identifier) {
      this.isNotFound = true;
      return;
    }
    const { nodeId, alias } = identifier;
    params.nodeId = nodeId;
    params.alias = alias as string;
    this.nodeId = nodeId;
    this.alias = alias as string;

    const docInitialData = getInitialData();

    // 如果有缓存，且为当前文档的数据
    if (docInitialData &&
      JSON.stringify(docInitialData) !== '{}') {
      this.dealWithRes(docInitialData);
    } else {
      const result: any = await service.getDocumentDetail(params);

      this.helpResponseCode = result?.code;

      if (result?.code === 404 && !result?.success) {
        this.isNotFound = true;
      } else if (result?.data?.redirectUrl) {
        this.redirectUrl = result?.data?.redirectUrl;
      } else if (result?.data) {
        this.dealWithRes(result);
      } else {
        this.isNotFound = true;
      }
    }
  };

  // 处理返回数据
  private dealWithRes = (res) => {
    if (!res) return;
    this.data = solutionDataProcessor(res?.data);
    this.docContent = res?.data?.content;
    this.recommendSolutionList = res?.data?.recommendDocs;
  };

  // 初始化detailModel
  initData = () => {
    this.data = {};
  };
}
