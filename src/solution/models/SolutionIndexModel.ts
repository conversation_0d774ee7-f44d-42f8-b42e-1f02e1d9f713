import { getInitialData } from 'ice';
import services from '@/solution/services';
import { WEBSITE, LANG } from '@/help-fe-common/utils/global/website';
import { solutionBasePath } from '@/solution/constants';

export class SolutionIndexModel {
  data: any = [];

  // 响应状态码
  helpResponseCode = 200;

  // 获取首页数据
  getIndex = async () => {
    const params = {
      website: WEBSITE,
      language: LANG,
    };
    const indexInitialData = getInitialData();

    // 如果有缓存，且为首页的数据
    if (indexInitialData &&
      JSON.stringify(indexInitialData) !== '{}' &&
      indexInitialData?.data?.path === solutionBasePath) {
      this.dealWithRes(indexInitialData?.data);
    } else {
      const result: any = await services.getSolutionIndex(params);
      this.helpResponseCode = result?.code;
      if (result) {
        this.dealWithRes(result);
      }
    }
  };

  // 处理返回数据
  dealWithRes = (res) => {
    if (!res) return;
    this.data = res?.data;
  };

  // 初始化
  initData = () => {
    this.data = [];
  };
}
