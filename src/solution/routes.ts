/**
 * 解决方案路由配置
 */
import { IRouterConfig } from 'ice';
import PVWrapper from '@/help-fe-common/RouterWrapper/PVWrapper';
import Index from '@/solution/pages/SolutionIndex';
import Detail from '@/solution/pages/SolutionDetail';

const routerConfig: IRouterConfig[] = [
  {
    path: '/(solution|zh)/tech-solution/',
    exact: true,
    component: Index,
    wrappers: [PVWrapper],
  },
  {
    path: '/(solution|zh)/tech-solution/:docAlias',
    exact: true,
    component: Detail,
    wrappers: [PVWrapper],
  },
];

export default routerConfig;
