import { scrollIntoView as seamlessScrollIntoView } from 'seamless-scroll-polyfill';

export const scrollIntoView = (hashId) => {
  const anchor = document.getElementById(hashId);
  if (anchor) {
    seamlessScrollIntoView(anchor, { behavior: 'smooth' });
    anchor.id = '';
    const newUrl = `${window.location.href.split('#')[0]}#${hashId}`;
    history.pushState('', '', newUrl);
    anchor.id = hashId;
  }
};
