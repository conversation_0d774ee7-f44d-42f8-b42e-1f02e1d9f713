import { ComponentTypeEnum } from '@/solution/constants';
/** 数据结构 */
interface SolutionData {
  // 顶通头部数据
  bannerHeadData: BannerHeadData;
  // 锚点数据
  anchorList: AnchorItemData[];
  // 楼层数据
  floorList: BaseFloorData[];
}

/** ------- 基础组件 ------- */

// 按钮item
interface ButtonItemData {
  text: string;
  url: string;
  onClick?: () => void;
}

// 锚点item
interface AnchorItemData {
  id: string;
  text: string;
}

// 链接
interface LinkData {
  text: string;
  url: string;
}

// 基础楼层数据
interface BaseFloorData {
  id: string;
  anchor?: AnchorItemData;
  title?: string;
  titleType?: string;
  desc?: string;
  data: ComponentData;
  link?: LinkData;
}

// tab组件
interface TabData {
  tabList: TabItemData[];
}
interface TabItemData {
  tabId: string;
  tabTitle: string;
  componentType: ComponentTypeEnum;
  data: SolutionCardList | AdvantageList | ComparisonData | SolutionIntroduction | ProductCardList | FreeCardList | InfoList;
}

/** -------- 业务组件 ------- */
// 顶通头部数据
interface BannerHeadData {
  id: string;
  title: string;
  desc: string;
  buttonGroup: ButtonItemData[];
  info: {
    title: string;
    infoArray: Array<{
      text: string;
    }>;
  };
}

// 各楼层业务数据
interface ComponentData {
  id: string;
  componentType: ComponentTypeEnum;
  advantageList?: AdvantageItem[];
  comparisonData?: ComparisonData;
  solutionIntroduction?: SolutionIntroduction;
  adResource?: AdResource;
  tabList?: TabItemData[];
  basicCard?: BasicCardItem;
  solutionCardList?: SolutionCardItem[];
  recommendSolutionList?: SolutionCardItem[];
  productCardList?: ProductCardItem[];
  freeCardList?: FreeCardItem[];
  solutionConsult?: SolutionConsult;
  overviewCardList?: OverviewCardItem[];
  infoList?: InfoItem[];
}

// 方案要点
interface InfoList {
  infoList: InfoItem[];
}
interface InfoItem {
  title: string;
  infoArray: Array<{
    text: string;
  }>;
}

// 方案优势
interface AdvantageList {
  advantageList: AdvantageItem[];
}
interface AdvantageItem {
  iconImg: string;
  title: string;
  desc: string;
}

// 架构对比
interface ComparisonData {
  headData: {
    leftTitle: string;
    rightTitle: string;
  };
  comparisonList: ComparisonItem[];
}

interface ComparisonItem {
  leftInfo: {
    title: string;
    desc: string;
  };
  rightInfo: {
    title: string;
    desc: string;
  };
  comparisonAttribute: string;
}

// 架构介绍
interface SolutionIntroduction {
  solutionImg?: string;
  solutionDiagramId?: string;
  solutionVideo?: string;
  solutionDesc: string;
  solutionTime: string;
  solutionCost: {
    cost: string;
    desc: string;
  };
  buttonGroup: ButtonItemData[];
  relatedProductList: Array<{
    pipCode: string;
    productName: string;
    productUrl: string;
  }>;
  solutionDeployId?: string;
}

// 广告资源
interface AdResource {
  adImg: string;
  adTitle: string;
  adDesc: string;
  adButton: {
    text: string;
    url: string;
  };
}

// 解决方案卡片
interface SolutionCardList {
  solutionCardList: SolutionCardItem[];
}
interface SolutionCardItem {
  imgUrl?: string;
  title: string;
  desc: string;
  cardType?: number;
  url: string;
}

// 方案概览卡片
interface OverviewCardItem {
  title: string;
  link: LinkData;
  tagList: Array<{
    text: string;
    type: string;
  }>;
  score: Number;
  scoreDesc?: string;
  content: TabData;
}

// 基础图文组件
interface BasicCardItem {
  img?: string;
  solutionDiagramId?: string;
  video?: string;
  content: string;
  buttonGroup: ButtonItemData[];
}

// 优惠云产品
interface ProductCardList {
  productCardList: ProductCardItem[];
}
interface ProductCardItem {
  goodsId: string | number;
}

// 试用卡片
interface FreeCardList {
  freeCardList: FreeCardItem[];
}
interface FreeCardItem {
  freeId: string | number;
}

// 解决方案咨询
interface SolutionConsult {
  solutionImg: string;
  solutionTitle: string;
  solutionDesc: string;
  solutionUrl: {
    text: string;
    url: string;
  };
}

export {
  SolutionData,
  AnchorItemData,
  ButtonItemData,
  BaseFloorData,
  TabData,
  TabItemData,
  BannerHeadData,
  ComponentData,
  AdvantageItem,
  ComparisonData,
  ComparisonItem,
  SolutionIntroduction,
  AdResource,
  SolutionCardItem,
  BasicCardItem,
  ProductCardItem,
  FreeCardItem,
  SolutionConsult,
  OverviewCardItem,
  InfoItem,
};
