import {
  getSelector,
  getDomId,
  getDomText,
  getDomAttribute,
  getDomList,
  getDomNodeList,
  getDomHtml,
} from '@/help-fe-common/utils/global/dom/domProfessor';
import { BaseContainerTypeEnum, ComponentTypeEnum } from '@/solution/constants';
import {
  SolutionData,
  AnchorItemData,
  BaseFloorData,
  TabItemData,
  BannerHeadData,
  AdvantageItem,
  ComparisonItem,
  SolutionIntroduction,
  AdResource,
  SolutionCardItem,
  BasicCardItem,
  ProductCardItem,
  FreeCardItem,
  OverviewCardItem,
  InfoItem,
} from '@/solution/utils/dataEngine/dataSchema';

/**
 * 获取banner数据
 * @param floorItem
 * @returns
 */
const getHeaderData = (floorItem): BannerHeadData => {
  const bannerHeadData: BannerHeadData = {
    id: getDomId(floorItem),
    title: getDomText(floorItem, getSelector('header-title', 'h1')),
    desc: getDomText(floorItem, getSelector('header-desc', 'p')),
    buttonGroup: getDomList(floorItem, getSelector('header-links', 'a')),
    info: {
      title: getDomText(floorItem, getSelector('info-title', 'h2')),
      infoArray: getDomList(floorItem, getSelector('info-item', 'p')),
    },
  };
  return bannerHeadData;
};

/**
 * 获取锚点数据
 * @param contentBodyDom
 * @returns
 */
const getAnchorData = (contentBodyDom): AnchorItemData[] => {
  return getDomList(contentBodyDom, getSelector('anchor-point'));
};

/**
 * 获取楼层数据
 * @param floorItem
 * @returns
 */
const getFloorData = (floorItem) => {
  const floorData: BaseFloorData = {
    id: getDomId(floorItem),
    anchor: {
      id: getDomId(floorItem.querySelector(getSelector('anchor-point'))),
      text: getDomText(floorItem, getSelector('anchor-point')),
    },
    title: getDomText(floorItem, getSelector('floor-title', 'h2')),
    titleType: getDomText(floorItem, getSelector('anchor-point')) ? 'h2' : 'h3',
    desc: getDomHtml(floorItem, getSelector('floor-desc', 'p')),
    data: {
      id: '',
      componentType: ComponentTypeEnum.DEFAULT,
    },
    link: {
      text: getDomText(floorItem, getSelector('floor-link')),
      url: getDomAttribute(floorItem, getSelector('floor-link', 'a'), 'href'),
    },
  };
  const floorContentNode = floorItem?.querySelector(getSelector('floor-content'))?.childNodes?.[0];
  const componentType = floorContentNode?.getAttribute('data-tag');

  const getComponentData: Function = componentDataOperator?.[componentType];

  const data = getComponentData && getComponentData(floorContentNode);
  floorData.data = { id: getDomId(floorContentNode), componentType, ...data };

  return floorData;
};

/**
 * 组件数据操作
 */
const componentDataOperator = {
  [ComponentTypeEnum.ADVANTAGE]: (componentNode) => {
    if (!componentNode) return;
    const advantageList: AdvantageItem[] = getDomNodeList(componentNode, getSelector('img-card'))
      ?.map((cardItem: HTMLElement) => {
        return {
          iconImg: getDomAttribute(cardItem, getSelector('img-card-logo', 'img')),
          title: getDomText(cardItem, getSelector('img-card-title')),
          desc: getDomText(cardItem, getSelector('img-card-desc')),
        };
      });
    return { advantageList };
  },
  [ComponentTypeEnum.COMPARISON]: (componentNode) => {
    if (!componentNode) return;
    const headerSelector = getSelector('comparison-header');
    const leftSelector = getSelector('comparison-left');
    const rightSelector = getSelector('comparison-right');
    const headData = {
      leftTitle: getDomText(componentNode, `${headerSelector} ${leftSelector}`),
      rightTitle: getDomText(componentNode, `${headerSelector} ${rightSelector}`),
    };
    const comparisonList: ComparisonItem[] =
      getDomNodeList(componentNode, getSelector('comparison-item'))
        ?.map((nodeItem) => {
          return {
            leftInfo: {
              title: getDomText(nodeItem, getSelector('comparison-left-title')),
              desc: getDomText(nodeItem, getSelector('comparison-left-desc')),
            },
            rightInfo: {
              title: getDomText(nodeItem, getSelector('comparison-right-title')),
              desc: getDomText(nodeItem, getSelector('comparison-right-desc')),
            },
            comparisonAttribute: getDomText(nodeItem, getSelector('comparison-dimension')),
          };
        });
    return {
      comparisonData: { headData, comparisonList },
    };
  },
  [ComponentTypeEnum.SOLUTION]: (componentNode) => {
    if (!componentNode) return;
    const solutionIntroduction: SolutionIntroduction = {
      solutionImg: getDomAttribute(componentNode, getSelector('solution-introduction-image', 'img')),
      solutionDiagramId: getDomAttribute(componentNode, getSelector('cloud-arch-diagram'), 'data-diagram-id'),
      solutionVideo: getDomAttribute(componentNode, getSelector('solution-introduction-image', 'video')),
      solutionDesc: getDomText(componentNode, getSelector('solution-introduction-desc', 'p')),
      solutionTime: getDomText(componentNode, getSelector('solution-time')),
      solutionCost: {
        cost: getDomText(componentNode, 'span[data-tag="solution-cost-num"]'),
        desc: getDomText(componentNode, 'span[data-tag="solution-cost-desc"]'),
      },
      buttonGroup: getDomList(componentNode, getSelector('solution-introduction-links', 'a')),
      relatedProductList:
        getDomNodeList(componentNode, getSelector('solution-introduction-products', 'span.product-id'))
          ?.map((itemNode: HTMLElement) => {
            return {
              pipCode: itemNode?.getAttribute('pip-code') || '',
              productName: itemNode?.textContent || '',
              productUrl: itemNode?.getAttribute('data-url') || '',
            };
          }),
      solutionDeployId: getDomText(componentNode, getSelector('solution-introduction-config-id')),
    };
    return { solutionIntroduction };
  },
  [ComponentTypeEnum.AD_RESOURCE]: (componentNode) => {
    if (!componentNode) return;
    const adResource: AdResource = {
      adImg: getDomAttribute(componentNode, getSelector('advertising-image', 'img')),
      adTitle: getDomText(componentNode, getSelector('advertising-title', 'p')),
      adDesc: getDomText(componentNode, getSelector('advertising-desc', 'p')),
      adButton: {
        text: getDomText(componentNode, getSelector('advertising-link', 'a')),
        url: getDomAttribute(componentNode, getSelector('advertising-link', 'a'), 'href'),
      },
    };
    return { adResource };
  },
  [ComponentTypeEnum.GOODS]: (componentNode) => {
    if (!componentNode) return;
    const productCardList: ProductCardItem[] =
      getDomNodeList(componentNode, 'p')
        ?.map((item: HTMLElement) => {
          return {
            goodsId: item?.textContent || '',
          };
        });
    return { productCardList };
  },
  [ComponentTypeEnum.FREE]: (componentNode) => {
    if (!componentNode) return;
    const freeCardList: FreeCardItem[] =
      getDomNodeList(componentNode, 'p')
        ?.map((item: HTMLElement) => {
          return {
            freeId: item?.textContent || '',
          };
        });
    return { freeCardList };
  },
  [ComponentTypeEnum.TAB]: (componentNode) => {
    if (!componentNode) return;
    const tabList: TabItemData[] = getDomNodeList(componentNode, 'section[data-tag="tabbed-content-box"] >section')
      ?.map((tabItem: HTMLElement) => {
        const tabItemContentNode = tabItem?.querySelector('div[data-tag]');
        const componentType = tabItemContentNode?.getAttribute('data-tag') as any;
        const getComponentData: Function = componentDataOperator?.[componentType];
        const data = getComponentData && getComponentData(tabItemContentNode);
        return {
          tabId: getDomId(tabItem),
          tabTitle: getDomText(tabItem, 'h2'),
          componentType,
          data,
        };
      });

    return { tabList };
  },
  [ComponentTypeEnum.SOLUTION_CARDS]: (componentNode) => {
    if (!componentNode) return;
    const solutionCardList: SolutionCardItem[] = getDomNodeList(componentNode, getSelector('img-card'))
      ?.map((childItem: HTMLElement) => {
        return {
          title: getDomText(childItem, getSelector('img-card-title')),
          desc: getDomText(childItem, getSelector('img-card-desc')),
          imgUrl: getDomAttribute(childItem, getSelector('img-card-image', 'img')),
          url: getDomAttribute(childItem, getSelector('img-card-links', 'a'), 'href'),
        };
      });
    return {
      solutionCardList,
    };
  },
  [ComponentTypeEnum.BASIC_CARD]: (componentNode) => {
    if (!componentNode) return;
    const basicCard: BasicCardItem = {
      img: getDomAttribute(componentNode, getSelector('basic-introduction-image', 'img')),
      solutionDiagramId: getDomAttribute(componentNode, getSelector('cloud-arch-diagram'), 'data-diagram-id'),
      video: getDomAttribute(componentNode, getSelector('basic-introduction-image', 'video')),
      content: getDomHtml(componentNode, getSelector('basic-introduction-content')),
      buttonGroup: getDomList(componentNode, getSelector('basic-introduction-links', 'a')),
    };
    return {
      basicCard,
    };
  },
  [ComponentTypeEnum.OVERVIEW]: (componentNode) => {
    if (!componentNode) return;
    const overviewCardList: OverviewCardItem[] = getDomNodeList(componentNode, getSelector('img-card'))
      ?.map((childItem: HTMLElement) => {
        const getComponentData: Function = componentDataOperator?.[ComponentTypeEnum.TAB];
        const data = getComponentData && getComponentData(childItem);
        return {
          title: getDomText(childItem, getSelector('img-card-title')),
          link: {
            text: getDomText(childItem, getSelector('img-card-links')),
            url: getDomAttribute(childItem, getSelector('img-card-links', 'a'), 'href'),
          },
          tagList: getDomNodeList(childItem, getSelector('img-card-status', 'span.status'))?.map((item: HTMLElement) => {
            return {
              text: item?.textContent || '',
              type: item?.getAttribute('data-color') || 'default',
            };
          }),
          score: Number(getDomText(childItem, getSelector('img-card-score', 'span.score'))),
          scoreDesc: getDomText(childItem, getSelector('img-card-score', 'span.ph')),
          content: data,
        };
      });
    return {
      overviewCardList,
    };
  },
  [ComponentTypeEnum.INFOS]: (componentNode) => {
    if (!componentNode) return;
    const infoList: InfoItem[] = getDomNodeList(componentNode, getSelector('basic-info'))?.map((item: HTMLElement) => {
      return {
        title: getDomText(item, getSelector('info-title')),
        infoArray: getDomList(item, getSelector('info-item', 'p')),
      };
    });
    return {
      infoList,
    };
  },
};

const solutionDataProcessor = (data) => {
  try {
    const solutionData: SolutionData = {
      bannerHeadData: {} as any,
      anchorList: [] as AnchorItemData[],
      floorList: [] as BaseFloorData[],
    };
    const { content } = data;
    const parser = new DOMParser();
    const dom = parser?.parseFromString(content || '', 'text/html');
    const contentBodyDom = dom.querySelector('div.icms-help-docs-content div[data-tag="body"]');
    if (!contentBodyDom) return null;
    // 解析锚点数据
    solutionData.anchorList = getAnchorData(contentBodyDom);
    // 解析顶部和楼层数据
    contentBodyDom?.childNodes?.forEach((floorItem: any) => {
      const dataTag = floorItem?.getAttribute('data-tag');
      if (dataTag === BaseContainerTypeEnum.HEADER) {
        solutionData.bannerHeadData = getHeaderData(floorItem);
      } else if (dataTag === BaseContainerTypeEnum.FLOOR) {
        solutionData.floorList.push(getFloorData(floorItem));
      }
    });
    return solutionData;
  } catch (error) {
    console.error(error);
    return null;
  }
};

export default solutionDataProcessor;
