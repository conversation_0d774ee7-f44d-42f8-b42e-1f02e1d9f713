import {
  getSelector,
  getDomId,
  getDomText,
  getDomAttribute,
  getDomList,
  getDomNodeList,
  getDomHtml,
} from '@/help-fe-common/utils/global/dom/domProfessor';

import { Floor, ImageCard } from './dataSchema';

export const extractFloorData = (node: HTMLElement): Floor => {
  const floor: Floor = {
    id: node.getAttribute('id') || '',
    title: getDomText(node, '[data-tag="floor-title"] h2'),
    description: getDomText(node, '[data-tag="floor-desc"] p'),
    content: [],
  };

  const cardNodes = node.querySelectorAll('[data-tag="img-card"]');

  floor.content = Array.from(cardNodes).map((cardNode: HTMLElement) => {
    const card: ImageCard = {
      logo: getDomAttribute(cardNode, getSelector('img-card-logo', 'img')) || '',
      title: getDomText(cardNode, '[data-tag="img-card-title"] p'),
      description: getDomText(cardNode, '[data-tag="img-card-desc"] p'),
      link: getDomAttribute(cardNode, '[data-tag="img-card-link"] a', 'href'),
    };
    return card;
  });

  return floor;
};

export default extractFloorData;
