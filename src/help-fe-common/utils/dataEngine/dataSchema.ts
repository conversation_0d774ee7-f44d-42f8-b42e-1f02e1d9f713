// 基础数据类型定义
interface ButtonItemData {
  text: string;
  link: string;
  type?: 'primary' | 'default';
}

interface AnchorItemData {
  id: string;
  title: string;
}

interface LinkData {
  text: string;
  url: string;
}

// Banner数据结构
interface BannerHeadData {
  id: string;
  title: string;
  desc: string;
  buttonGroup: ButtonItemData[];
  info: {
    title: string;
    infoArray: Array<{
      text: string;
    }>;
  };
}

// 基础楼层数据结构
interface BaseFloorData {
  id: string;
  anchor?: AnchorItemData;
  title?: string;
  titleType?: string;
  desc?: string;
  data: any;
  link?: LinkData;
}

// 产品卡片数据
interface ProductCardData {
  id: string;
  name: string;
  desc: string;
  link: string;
  icon?: string;
}

// 工具卡片数据
interface ToolCardData {
  id: string;
  title: string;
  desc: string;
  image: string;
  link: string;
}
interface ImageCard {
  logo: string;
  title: string;
  description: string;
  link?: string;
}

interface Floor {
  id: string;
  title: string;
  description: string;
  content: ImageCard[];
}
export type {
  ButtonItemData,
  AnchorItemData,
  LinkData,
  BannerHeadData,
  BaseFloorData,
  ProductCardData,
  ToolCardData,
  Floor,
  ImageCard,
};
