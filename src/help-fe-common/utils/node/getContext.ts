import { ENV, getEnv } from '@/help-fe-common/utils/global/env';
/**
 * @description: 运行时 判断是否 node 运行环境
 * @return {boolean}
 */
export function isServer() {
  return process.env.__IS_SERVER__;
}

export function ssrSuccess() {
  return !isServer() && window.__ICE_SSR_ENABLED__ === true;
}

/**
 * @description: 运行时获取当前请求的 baseURL
 * @return {*}
 */
let baseURL = '';
export function setReqBaseURL(reqHeader) {
  if (process.env.NODE_ENV === 'development') {
    baseURL = 'https://localhost:8080';
    return;
  }
  // 如果是 CSR，baseURL为空，自动默认使用域名
  if (!isServer()) {
    baseURL = '';
    return;
  }

  // 预发
  const isPre = reqHeader?.host?.includes('pre');
  if (isPre) {
    baseURL = 'https://pre-help.aliyun.com';
    return;
  }

  baseURL = 'https://help.aliyun.com';
}

export function getReqBaseURL() {
  if (process.env.NODE_ENV === 'development') {
    // return `http://localhost:8080`;
    return 'https://pre-help.aliyun.com';
  }
  return `https://${getEnv() === ENV.PROD ? '' : 'pre-'}help.aliyun.com`;
  // return 'https://help.aliyun.com';
}
