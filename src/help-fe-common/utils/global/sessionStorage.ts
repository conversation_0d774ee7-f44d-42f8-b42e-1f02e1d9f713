/**
 * 写入localStorage
 * @param name item Key
 * @param val item值
 */
const setSessionStorage = (name, val) => {
  const obj = { value: val };
  try {
    sessionStorage.setItem(name, JSON.stringify(obj));
  } catch (e) {
    // TODO 埋点
  }
};

const getSessionStorage = (name) => {
  try {
    const storeInfo = sessionStorage.getItem(name);
    if (!storeInfo) {
      return null;
    }
    const parseLogStoreInfo = storeInfo && JSON.parse(storeInfo);
    return parseLogStoreInfo.value;
  } catch (e) {
    // TODO 埋点
  }
  return null;
};

export { setSessionStorage, getSessionStorage };
