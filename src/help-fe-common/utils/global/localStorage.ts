/**
 * 写入localStorage
 * @param name item Key
 * @param val item值
 * @param expire item有效时间
 */
const setLocalStorageItem = (name, val, expire?) => {
  const obj = expire ? {
    value: val,
    time: Date.now(),
    expire,
  } : {
    value: val,
  };
  try {
    localStorage.setItem(name, JSON.stringify(obj));
    // eslint-disable-next-line no-empty
  } catch (e) { }
};

const getLocalStorageItem = (name) => {
  try {
    const logStoreInfo = localStorage.getItem(name);
    if (!logStoreInfo) {
      return null;
    }
    const parseLogStoreInfo = logStoreInfo && JSON.parse(logStoreInfo);
    if (parseLogStoreInfo.expire && (Date.now() - parseLogStoreInfo.time > parseLogStoreInfo.expire)) {
      localStorage.removeItem(name);
      return null;
    }
    return parseLogStoreInfo.value;
  } catch (e) {
    // TODO 埋点
  }
  return null;
};

export { setLocalStorageItem, getLocalStorageItem };
