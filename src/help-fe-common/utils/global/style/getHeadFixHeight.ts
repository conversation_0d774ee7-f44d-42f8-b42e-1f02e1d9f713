import { getCssNumber } from '@/help-fe-common/utils/global/style/cssValue';
import { isCN } from '@/help-fe-common/utils/global/website';

function getHeadFixHeight() {
  const head = document.querySelector('.help-body-head-content');
  if (!head || !isCN) return 0;
  const style = getComputedStyle(head);
  const position = style?.getPropertyValue('position');
  if (position !== 'fixed') return 0;
  return getCssNumber('--default-head-height') - 1;
}

export default getHeadFixHeight;
