/**
 * 处理顶部banner、左侧目录树、html模板等样式
 * @param show 是否展示
 */
const handleGlobalStyle = (globalClassList, show) => {
  const className = globalClassList.join(', ');
  const globalNodeList = document.querySelectorAll(className);
  globalNodeList?.forEach((node) => {
    if (show) {
      node && node?.setAttribute('style', 'display:block');
    } else {
      node && node?.setAttribute('style', 'display:none');
    }
  });
};

const removeAsyncNodeList = (classList) => {
  classList?.forEach((item) => {
    removeNavLoaderNode(item, 0);
  });
};

const removeNavLoaderNode = (className, seconds: number) => {
  let timer;
  if (seconds >= 5) return;
  if (timer) clearTimeout(timer);
  const aliyunNavNode = document.querySelector(className);
  if (aliyunNavNode) {
    aliyunNavNode?.setAttribute('style', 'display:none');
  } else {
    timer = setTimeout(() => {
      removeNavLoaderNode(className, seconds + 0.5);
    }, 500);
  }
};

export { handleGlobalStyle, removeNavLoaderNode, removeAsyncNodeList };
