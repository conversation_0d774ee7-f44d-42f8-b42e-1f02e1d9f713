const getCssValue = (propertyName) => {
  const root = document.querySelector(':root') as HTMLElement;
  const rootStyles = getComputedStyle(root);
  return rootStyles.getPropertyValue(propertyName);
};

const setCssValue = (propertyName, value) => {
  const root = document.querySelector(':root') as HTMLElement;
  if (root && root.style) {
    root.style.setProperty(propertyName, value);
  }
};

const getCssNumber = (propertyName): number => {
  const cssValue = getCssValue(propertyName);
  const resultNum = cssValue.match(/\d+/);
  return Number(resultNum?.[0]);
};

export { getCssValue, setCssValue, getCssNumber };
