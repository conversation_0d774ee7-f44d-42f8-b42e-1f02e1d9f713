/**
 * 拼接selector用于querySelector
 * @param tag data-tag标记
 * @param tagType 标签类型 p/a/img
 * @returns
 */
const getSelector = (dataTag, tagType?): string => {
  return tagType ? `div[data-tag="${dataTag}"] ${tagType}` : `div[data-tag="${dataTag}"]`;
};

/**
 * 获取当前节点id
 * @param node 当前节点
 * @returns
 */
const getDomId = (node: HTMLElement): string => {
  return node?.getAttribute('id') || '';
};

/**
 * 获取dom节点text文本
 * @param parentNode 父节点
 * @param selectorName 待查询的标识
 * @returns
 */
const getDomText = (parentNode: HTMLElement, selectorName: string): string => {
  const targetNode = parentNode?.querySelector(selectorName) as HTMLElement;
  return targetNode?.textContent || '';
};
/**
 * 获取dom节点html内容
 * @param parentNode
 * @param selectorName
 * @returns
 */

const getDomHtml = (parentNode: HTMLElement, selectorName: string): string => {
  const targetNode = parentNode?.querySelector(selectorName) as HTMLElement;
  return targetNode?.innerHTML || '';
};

/**
 * 获取dom节点属性
 * @param parentNode 父节点
 * @param selectorName 待查询的标识
 * @param attributeName 属性名称
 * @returns
 */
const getDomAttribute = (parentNode: HTMLElement, selectorName: string, attributeName?: string): string => {
  const targetNode = parentNode?.querySelector(selectorName) as HTMLElement;
  return targetNode?.getAttribute(attributeName || 'src') || '';
};

/**
 * 获取dom节点信息，如果isLink为true，则返回链接，否则返回文本
 * @param itemNode
 * @param isLink
 * @returns
 */
const getDomLink = (itemNode, isLink) => {
  return isLink ? {
    id: itemNode?.getAttribute('id'),
    text: itemNode?.textContent || '',
    url: itemNode?.getAttribute('href'),
  } : {
    id: itemNode?.getAttribute('id'),
    text: itemNode?.textContent || '',
  };
};

/**
 * 获取dom元素的子节点属性列表
 * @param parentNode
 * @param selectorName
 * @returns childNodeAttribute[]
 */
const getDomList = (parentNode: HTMLElement, selectorName: string): any[] => {
  const targetNode = parentNode?.querySelectorAll(selectorName);
  return Array.from(targetNode).map((item: HTMLElement) => {
    const tagName = item?.tagName;
    if (tagName === 'A') {
      return getDomLink(item, true);
    } else {
      return getDomLink(item, false);
    }
  });
};

/**
 * 获取dom元素的子节点列表
 * @param parentNode
 * @param selectorName
 * @returns HTMLElement[]
 */
const getDomNodeList = (parentNode: HTMLElement, selectorName: string): any[] => {
  const targetNode = parentNode?.querySelectorAll(selectorName);
  return Array.from(targetNode) || [];
};

export { getSelector, getDomId, getDomText, getDomHtml, getDomAttribute, getDomList, getDomNodeList };
