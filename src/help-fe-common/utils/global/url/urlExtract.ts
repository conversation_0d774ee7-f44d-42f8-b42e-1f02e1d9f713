import { isCN } from '@/help-fe-common/utils/global/website';
import { deleteSlash } from '@/help-fe-common/utils/global/isSameAlias';

const regexpMap = {
  cn: {
    idRegexp: new RegExp(/()(product|document_detail|video_detail|knowledge_detail|video_list|knowledge_list|my_favorites)\/(\d+)(.htm(l?))/),
    aliasRegexp: new RegExp(/()(zh|getting-started|solution)\/(.*)$/),
  },
  intl: {
    idRegexp: new RegExp(/(en|zh|tc|ja)?\/(product|doc-detail)\/(\d+)(.htm(l?))/),
    aliasRegexp: new RegExp(/help(\/china-mobile)?\/(en|zh|tc|ja)?\/(.*)$/),
  },
};

/**
 * 从url中获取nodeId、productAlias、docAlias
 * @param url 文档url
 * @returns {object} param
 */
export function extractUrlParam(url) {
  const regexpRules = isCN ? regexpMap.cn : regexpMap.intl;
  // 链接为nodeId类型
  const nodeMatchResult = url.match(regexpRules.idRegexp);
  if (nodeMatchResult) {
    const nodeId = nodeMatchResult?.[3];
    return { nodeId, alias: null };
  }

  // 新版语义化，pathname部分别名
  const aliasMatchResult = url.match(regexpRules.aliasRegexp);
  if (aliasMatchResult) {
    const alias = aliasMatchResult?.[3];
    return { alias: `/${alias}`, nodeId: null };
  }
  return null;
}

export const isProductNode = (url, alias): boolean => {
  if (/product\/(\d+)/.test(url) || deleteSlash(alias)?.split('/')?.length === 2) {
    return true;
  }
  return false;
};
