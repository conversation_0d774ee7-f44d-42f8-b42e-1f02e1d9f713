import _ from 'lodash';
import UrlParse from 'url-parse';
import { generateUrl } from '@/help-fe-common/utils/global/url/generateUrl';
import { WEBSITE, isCN } from '@/help-fe-common/utils/global/website';
import { toggleSpm } from '@/help-fe-common/utils/global/track/spm';

const urlMap = {
  cn:
  {
    hostname: ['help.aliyun.com',
      'pre-help.aliyun.com',
      'help-intl.aliyun-inc.com',
      'pre-help-intl-1.alibabacloud.com'],
    require: ['/zh/',
      '/product/',
      '/document_detail/',
      '/knowledge_list/',
      '/knowledge_detail/',
      '/video_list/',
      '/video_detail/'],
  },
  intl: {
    hostname: ['www.alibabacloud.com',
      'pre-www.alibabacloud.com',
      'alibabacloud.com',
      'help-intl.aliyun-inc.com',
      'pre-help-intl-1.alibabacloud.com'],
    require: ['/help/'], // 国际站站链接不带/help，新开窗口
    allow: ['/'], // 国际站首页，新开窗口
  },
  reseller: {
    hostname: ['partners-intl.aliyun.com'],
    require: ['/help/'], // 虚商站站链接不带/help，新开窗口
    allow: ['/'], // 首页，新开窗口
  },
};

const ALLOWED_BEHAVIOR = ['historyPush', 'replace', 'newOpen'];
enum UrlTypeEnum {
  INNER = 'inner',
  OUTER = 'outer'
}

const urlTrigger = {
  [UrlTypeEnum.INNER]: {
    defaultBehavior: ALLOWED_BEHAVIOR[0],
    allowedBehavior: ALLOWED_BEHAVIOR,
    actions: {
      historyPush: (url, history, state) => {
        history.push(url, state);
      },
      replace: (url, history, state) => {
        history.replace(url, state);
      },
      newOpen: (url, history, state) => {
        window.open(url);
      },
    },
  },
  [UrlTypeEnum.OUTER]: {
    defaultBehavior: ALLOWED_BEHAVIOR[2],
    allowedBehavior: ALLOWED_BEHAVIOR,
    actions: {
      historyPush: (url, history, state) => {
        window.location.href = url;
      },
      replace: (url, history, state) => {
        window.location.href = url;
      },
      newOpen: (url, history, state) => {
        window.open(url);
      },
    },
  },
};

const rules = urlMap?.[WEBSITE];

const getUrlType = (url): UrlTypeEnum => {
  const urlParse = new UrlParse(url);
  const { hostname, pathname } = urlParse;
  let urlType = UrlTypeEnum.INNER;
  // 针对中国站和国际站特性判断，以下情况为外部链接新开窗口：
  // 1、hostname不为帮助中心
  // 2、pathname不为'/'（排除首页）且链接不满足require
  if (!rules.hostname.includes(hostname) ||
    (pathname !== '/' && rules?.require?.every((rule) => pathname.indexOf(rule) === -1)) ||
    (rules?.allow?.some((rule) => pathname === rule))) {
    urlType = UrlTypeEnum.OUTER;
  }
  return urlType;
};

/**
 * 对帮助中心url进行拦截，统一处理跳转行为
 * @param url url链接
 * @param history history对象
 * @param state url来源信息
 * @param targetNode a标签节点，用于处理spm信息
 * @param behavior 跳转行为，默认historyPush
 * @returns {string} 返回处理后的url链接
 */
export function triggerUrlBehavior(url, history, state?, targetNode?, behavior?) {
  let targetUrl = url;
  if (_.isPlainObject(url)) {
    targetUrl = generateUrl(url);
  }

  // 中国站设置spm
  if (isCN) {
    // 对url进行统一处理spm信息
    targetUrl = toggleSpm(targetUrl, targetNode);
  }

  // 获取链接类型、链接行为、链接触发函数
  const urlType = getUrlType(targetUrl);
  const urlBehavior = behavior || urlTrigger[urlType]?.defaultBehavior;
  const urlActionFunction = urlTrigger[urlType]?.actions[urlBehavior];

  // 内部链接需要使用处理后的targetUrl，外部链接直接调用
  if (urlType === UrlTypeEnum.INNER) {
    const urlParse = new UrlParse(targetUrl);
    const { pathname, query, hash } = urlParse;
    targetUrl = pathname + query + hash;
  }
  urlActionFunction(targetUrl, history, state);
}
