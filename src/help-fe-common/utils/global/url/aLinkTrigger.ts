import { triggerUrlBehavior } from '@/help-fe-common/utils/global/url/urlBehavior';

/**
 * 通用的A链接触发器, 将A提取出来，然后调用通用的跳转函数
 * @param e 事件
 * @param history
 * @param historyState history state 信息
 * @param behavior 链接打开行为 (默认当前页打开) 'historyPush', 'replace', 'newOpen'
 */

export function aLinkTrigger(e, history, historyState = {}, behavior?) {
  e?.preventDefault();
  // e?.stopPropagation();
  const node = e?.currentTarget;
  if (node) {
    const href = node?.getAttribute('href') || node?.getAttribute('data-href');
    triggerUrlBehavior(href, history, historyState, node, behavior);
  }
}
