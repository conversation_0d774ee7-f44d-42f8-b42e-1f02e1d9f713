// 引入SDK及插件
import AES from '@ali/aes-tracker';
import AESPluginPV from '@ali/aes-tracker-plugin-pv';
import AESPluginEvent from '@ali/aes-tracker-plugin-event';
import AESPluginJSError from '@ali/aes-tracker-plugin-jserror';
import AESPluginAPI from '@ali/aes-tracker-plugin-api';
import AESPluginResourceError from '@ali/aes-tracker-plugin-resourceError';
import AESPluginPerf from '@ali/aes-tracker-plugin-perf';
import AESPluginEventTiming from '@ali/aes-tracker-plugin-eventTiming';
import AESPluginLongTask from '@ali/aes-tracker-plugin-longtask';
import AESPluginBlank from '@ali/aes-tracker-plugin-blank';
import AESPluginAutolog from '@ali/aes-tracker-plugin-autolog';
import { getEnv } from '@/help-fe-common/utils/global/env';
import <PERSON>ieHelper from '@/help-fe-common/utils/global/cookie';


const aliyunAccountId = CookieHelper.get('login_aliyunid_pk') || '';

// 初始化SDK
const aes = new AES({
  pid: 'aliyun_help',
  user_type: '6',
  env: getEnv(),
  uid: aliyunAccountId,
});

const sendAemLog = () => {
  // 挂载插件
  aes.use(AESPluginPV, {
    // 开启history router的监听
    enableHistory: true,
    // 设置page_id的逻辑规则
    getPageId: (url) => {
      return location.pathname; // 新的 page_id
    },
  });

  aes.use(
    [
      AESPluginEvent,
      AESPluginJSError,
      AESPluginAPI,
      AESPluginResourceError,
      AESPluginPerf,
      AESPluginEventTiming,
      AESPluginBlank,
      AESPluginAutolog,
      AESPluginLongTask],
  );
};
const sendAemEventLog = (eventName, params) => {
  // https://aliyuque.antfin.com/aes/help/xyf16m
  const sendEvent = aes.use(AESPluginEvent);
  sendEvent(eventName, params);
};

export { sendAemLog, sendAemEventLog };

