import SlsWebLogger from 'js-sls-logger';
import <PERSON><PERSON><PERSON><PERSON>per from '@/help-fe-common/utils/global/cookie';
import { getEnv } from '@/help-fe-common/utils/global/env';
import { WEBSITE, WEBSITE_ENUM, LANG } from '@/help-fe-common/utils/global/website';

// SLS 自主上报
const opts = WEBSITE === WEBSITE_ENUM?.CN ? {
  host: 'cn-wulanchabu.log.aliyuncs.com',
  project: 'help-new',
  logstore: 'web-tracking',
  time: 10,
  count: 10,
} : {
  host: 'ap-southeast-1.log.aliyuncs.com',
  project: 'help',
  logstore: 'help-new-intl',
  time: 10,
  count: 10,
};

const logger = new SlsWebLogger(opts);
export const sendSlsLog = (params) => {
  let screenSize = '';
  let availScreen = '';
  let viewport = '';
  let aliyunAccountId = '';
  const cna = CookieHelper.get('cna') || '';
  try {
    screenSize = `${screen?.width}*${screen?.height}`;
    availScreen = `${screen?.availWidth}*${screen?.availHeight}`;
    viewport = `${window.innerWidth}*${window.innerHeight}`;
    aliyunAccountId = CookieHelper.get('login_aliyunid_pk') || '';
  } catch (e) {
    //
  }
  logger.send({
    product: 'help-aliyun-com', // 站点
    ua: navigator?.userAgent, // ua
    cna, // 用户唯一标识
    screenSize,
    availScreen,
    viewport, // 视窗大小
    aliyunAccountId, // 阿里云账号，判断是否登录
    lang: LANG,
    url: window?.location?.href,
    env: getEnv(), // 获取当前环境
    device: 'pc', // 设备
    category: '', // 分类
    page: '', // 页面
    section: '', // 页面区块标识
    action: '', // 行为
    userParams1: '',
    userParams2: '',
    ...params,
  });
};
