// host白名单，白名单内的添加spm参数
const hostWhiteList = ['aliyun.com', 'alibabacloud.com', 'alicdn.com', 'aliyuncs.com', 'alibaba.storylane.io'];

export function getHostWhiteFlag(hostname) {
  if (!hostname) return false;
  const hostWhiteFlag = hostWhiteList.some((host) => hostname.indexOf(host) !== -1);
  return hostWhiteFlag;
}

/**
 * 根据站点判断是否需要添加/删除spm
 */
export function toggleSpm(url, targetNode) {
  const urlObject = new URL(url, window.location.origin);
  if (getHostWhiteFlag(urlObject?.hostname)) {
    // 白名单内站点，不存在spm则进行添加
    url?.indexOf('spm=') === -1 && (url = urlAddSpm(urlObject, targetNode));
  }
  return url;
}

/**
 * 根据目标节点，给url添加spm信息
 * @param url 链接
 * @param targetNode 目标节点
 * @returns
 */
function urlAddSpm(urlObject, targetNode) {
  // 为了确保始终能够拿到一个a,b位信息，使用页面根元素进行兜底处理
  targetNode = targetNode || document.documentElement;
  try {
    // 获取目标节点的spm点位信息
    const spmInfo = window.g_SPM.getParam(targetNode);
    if (spmInfo.a && spmInfo.b) {
      const spm = `${spmInfo.a}.${spmInfo.b}.${spmInfo.c}.${spmInfo.d}`;
      urlObject?.searchParams?.set('spm', spm);
    }
  } catch (e) {
    //
  }
  return urlObject?.href;
}
