const pluses = /\+/g;

function parseCookieValue(s) {
  if (s.indexOf('"') === 0) {
    s = s.slice(1, -1).replace(/\\"/g, '"').replace(/\\\\/g, '\\');
  }
  try {
    return decodeURIComponent(s.replace(pluses, ' '));
  } catch (e) {
    //
  }
}

const CookieHelper = {
  set(key, value, options) {
    if (typeof options.expires === 'number') {
      const d = new Date();
      d.setTime(d.getTime() + options.expires * 24 * 60 * 60 * 1000);
      options.expires = d.toUTCString();
    }

    document.cookie = [
      encodeURIComponent(key),
      '=',
      encodeURIComponent(value),
      options.expires ? `; expires=${options.expires}` : '', // use expires attribute, max-age is not supported by IE
      options.path ? `; path=${options.path}` : '',
      options.domain ? `; domain=${options.domain}` : '',
      options.secure ? '; secure' : '',
    ].join('');
  },
  get(key) {
    const cookies = document.cookie ? document.cookie.split('; ') : [];
    let i = 0;
    const l = cookies.length;
    let result;

    for (; i < l; i++) {
      const parts = cookies[i].split('=');
      const name = decodeURIComponent(parts.shift() || '');
      const cookie = parts.join('=');

      if (key === name) {
        result = parseCookieValue(cookie);
        break;
      }
    }
    return result;
  },
};

export default CookieHelper;
