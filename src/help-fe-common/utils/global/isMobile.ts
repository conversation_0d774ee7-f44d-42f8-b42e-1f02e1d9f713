import { isServer } from '@/help-fe-common/utils/node/getContext';

export default () => {
  const userAgent = navigator?.userAgent || '';
  const width = window?.innerWidth || window?.screen?.width || 0;
  if (isServer()) return false;
  if (
    (userAgent.match(/Android/i) ||
      userAgent.match(/webOS/i) ||
      userAgent.match(/iPhone/i) ||
      // userAgent.match(/iPad/i) || // 保证和官网头部的逻辑一致，他们在ipad上面显示的是pc版的头部
      userAgent.match(/iPod/i) ||
      userAgent.match(/BlackBerry/i) ||
      userAgent.match(/Windows Phone/i))
    || width <= 1055
  ) {
    return true;
  }
  return false;
};

/**
 * 是否是ios设备
 * @returns boolean
 */
export function isIOS() {
  const userAgent = navigator?.userAgent || '';
  if (
    userAgent.match(/iPhone/i) ||
    userAgent.match(/iPad/i) ||
    userAgent.match(/iPod/i)
  ) {
    return true;
  }
  return false;
}
