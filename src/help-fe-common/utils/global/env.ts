export enum ENV {
  LOCAL = 'local',
  PRE = 'pre',
  PROD = 'prod',
}

const hostArr = [
  'pre-help.aliyun.com',
  'pre-www.alibabacloud.com',
  'pre-www.aliyun.com',
];

export function getEnv(): ENV {
  const host = window?.location?.host || '';
  if (host.indexOf('localhost') > -1 || host.indexOf('127.0.0.1') > -1) {
    return ENV.LOCAL;
  } else if (hostArr.includes(host)) {
    return ENV.PRE;
  } else {
    return ENV.PROD;
  }
}
