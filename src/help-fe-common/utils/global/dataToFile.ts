// base64 -> Blob -> File
export const dataURL2Blob2File = (dataURL, fileName) => {
  try {
    const arr = dataURL.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bst = atob(arr[1]);
    const u8arr = new Uint8Array(bst.length);
    let n = bst.length;
    while (n--) {
      u8arr[n] = bst.charCodeAt(n);
    }
    const blob = new Blob([u8arr], { type: mime });
    return new File([blob], fileName, {
      type: mime,
      lastModified: Date.now(),
    });
  } catch (error) {
    return null;
  }
};
