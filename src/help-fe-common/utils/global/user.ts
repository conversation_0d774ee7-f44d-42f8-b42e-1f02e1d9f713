import { debounce } from 'lodash';
import CookieHelper from '@/help-fe-common/utils/global/cookie';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import userService from '@/help-fe-common/services/user';
import isMobile from '@/help-fe-common/utils/global/isMobile';
import { popupDialog } from '@/help-fe-common/utils/global/prompt';
import { isCN } from '@/help-fe-common/utils/global/website';

/**
 * 通过接口判断登录状态
 * @returns {boolean} loginState
 */
export async function getApiLoginState() {
  const state = await userService.checkLogin();
  return state;
}

/**
 * 通过Cookie判断登录状态
 * @returns {boolean} loginState
 */
export function getLoginState() {
  if (CookieHelper.get('login_aliyunid') || CookieHelper.get('login_aliyunid_ticket')) {
    return true;
  } else {
    return false;
  }
}

/**
 * 登录节流检测
 * @param action
 */
export function debounceLoginCheck(action) {
  return debounce(() => loginDetector(action), 100);
}

/**
 * @param {function} action 登录判断，true执行相应方法，false执行登录
 * @param {boolean} isRam 是否需要判断子账号，默认不判断
 */
export async function loginDetector(action, isRam = false) {
  const loginState = await getApiLoginState();
  // 未登录，弹窗提示登录
  if (!loginState?.login) {
    fastLogin(window.location.href);
    return null;
  }
  // 在需要子账号校验场景，子账号登录提示
  if (loginState?.ram && isRam) {
    popupDialog({ title: '该功能不支持子账号，请使用主账号登录', isShowButton: false, isSuccess: false });
  } else {
    return action();
  }
}

/**
 * 弹窗登录
 * @param url callback链接
 */
export function fastLogin(url) {
  // @ts-ignore FastLogin
  if (window.FastLogin && !isMobile()) {
    // @ts-ignore FastLogin
    window.FastLogin('show', [{
      loginCallback: () => {
        // @ts-ignore FastLogin
        window.$WEBSITE_UPDATE_ACCOUNT_STATUS && window.$WEBSITE_UPDATE_ACCOUNT_STATUS();
        window.location.reload();
      },
    }]);
  } else {
    window.location.href = generateLoginUrl(url);
  }
}

// 生成登录链接
export function generateLoginUrl(callbackUrl) {
  const loginUrl = isCN ?
    `https://account.aliyun.com/login/login.htm?oauth_callback=${encodeURIComponent(callbackUrl)}` :
    `https://account.alibabacloud.com/login/login.htm?oauth_callback=${encodeURIComponent(callbackUrl)}`;
  return loginUrl;
}

// 生成注册链接
export function generateRegisterUrl(callbackUrl) {
  const registerUrl = isCN ?
    `https://account.aliyun.com/register/qr_register.htm?oauth_callback=${encodeURIComponent(callbackUrl)}` :
    `https://account.alibabacloud.com/register/intl_register.htm?oauth_callback=${encodeURIComponent(callbackUrl)}`;
  return registerUrl;
}

// 判断用户注册态
export function getRegisterState() {
  if (CookieHelper.get('login_aliyunid_pk') || CookieHelper.get('login_aliyunid')) {
    return true;
  } else {
    return false;
  }
}

// 跳转注册页
export function toRegister(url, slsParam) {
  const isRegisterTrace = getRegisterState();
  // 判断用户是否发生过登录行为，如果有登陆痕迹，则直接跳转对应链接
  if (!isRegisterTrace) {
    window.open(generateRegisterUrl(url));
  } else {
    window.open(url);
  }
  const param = {
    ...slsParam,
    userParams1: isRegisterTrace ? 'direct' : 'register',
  };
  // 打点： section代表来源[head, selection, activityBanner, apiDoc], userParams1: 直跳 或者 去注册
  sendSlsLog(param);
}
