export class LocalStorageCache {
  /**
   * 本地缓存的 index，用于和 limit 配合限制缓存个数
   */
  index: number;

  /**
   * Cache 构造函数
   * @param keyPrefix 缓存 key 的前缀，用于不同场景下区分缓存命名空间
   * @param limit 最大存储条目数
   */
  constructor(public keyPrefix: string = 'help:', public limit: number = 10) {
    const keys: string[] = this.getLocalKeys();
    this.index = keys.length - 1;
  }

  /**
   * 向缓存中写入值
   * @param key 缓存 key
   * @param value 缓存值
   */
  setItem(key: string, value: any) {
    value = `${++this.index}:${value}`;
    localStorage.setItem(this.getKey(key), value);
    this.purge();
  }

  /**
   * 从缓存中获取值
   * @param key 缓存 key
   * @returns 缓存的值
   */
  getItem(key: string): string | null {
    const value = localStorage.getItem(this.getKey(key));
    if (!value) {
      return null;
    }
    return value.substring(value.indexOf(':') + 1);
  }

  /**
   * 删除缓存
   * @param key 缓存 key
   */
  removeItem(key: string): void {
    localStorage.removeItem(this.getKey(key));
  }

  /**
   * @private
   * 获取实际存储时，带前缀的 key
   * @param key cacheKey
   * @returns
   */
  getKey(key: string) {
    return this.keyPrefix + key;
  }

  /**
   * @private
   * 淘汰超过限制的缓存
   * @returns void
   */
  purge(): void {
    const keys: string[] = this.getLocalKeys();
    const diff = keys.length - this.limit;
    if (diff <= 0) {
      return;
    }
    keys.sort((a, b) => {
      return this.getItemIndex(localStorage.getItem(a)) - this.getItemIndex(localStorage.getItem(b));
    });
    keys.slice(0, diff).forEach((key) => {
      localStorage.removeItem(key);
    });
  }

  /**
   * @private
   * 获取 item 的index
   * @param item 缓存的 item
   * @returns index 值
   */
  getItemIndex(item: string | null): number {
    if (!item) {
      return -1;
    }
    return parseInt(item.substring(0, item.indexOf(':')));
  }

  /**
   * @private
   * 获取本地缓存的 keys 数组
   * @returns
   */
  getLocalKeys(): string[] {
    const keys: any[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      if (!localStorage.key(i)) {
        continue;
      }
      if (localStorage.key(i)?.indexOf(this.keyPrefix) === -1) {
        continue;
      }
      keys.push(localStorage.key(i));
    }
    return keys;
  }
}
