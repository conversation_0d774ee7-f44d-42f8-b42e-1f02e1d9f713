// 站点，目前分为中国站、国际站、虚商站
enum WEBSITE_ENUM {
  CN = 'cn',
  INTL = 'intl',
  RESELLER = 'reseller'
}

// 站点标志
const WEBSITE = window?.globalData?.website || getWebsite();
const isCN = WEBSITE === WEBSITE_ENUM.CN;

// 语言标志，中国站默认zh，国际站读取配置数据
// @ts-ignore 忽略window对象报错
const LANG = isCN ? 'zh' : window?.$default_lang || window?.globalData?.lang;

// 渠道来源标志，主要用于区分虚商站渠道商
const CHANNEL = WEBSITE === WEBSITE_ENUM.RESELLER ? window?.globalData?.channel || '' : '';

// NOTFOUND页标志
const NOT_FOUND = window?.globalData?.notfound === 'true' ||
  ['404', '403', '401']?.includes(window?.globalData?.statusCode);

/**
 * 获取当前默认站点
 * @returns WEBSITE_ENUM
 */
function getWebsite(): WEBSITE_ENUM {
  const host = window.location?.host || '';
  if (host === 'alibabacloud.com' || host === 'pre-www.alibabacloud.com') {
    return WEBSITE_ENUM.INTL;
  } else if (host === 'partners-intl.aliyun.com/') {
    return WEBSITE_ENUM.RESELLER;
  } else {
    return WEBSITE_ENUM.CN;
  }
}

/**
 * 获取当前站点、语言、渠道下的主页链接
 * @returns url
 */
function getIndexUrl(): string {
  if (isCN) {
    return '/';
  } else {
    return `/help/${CHANNEL ? `${CHANNEL}/` : ''}${LANG}`;
  }
}

export { NOT_FOUND, LANG, WEBSITE, isCN, WEBSITE_ENUM, CHANNEL, getIndexUrl };
