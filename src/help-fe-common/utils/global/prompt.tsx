import React from 'react';
import { Dialog, Button, Icon } from '@alifd/next';
import styles from '@/help-fe-common/styles/prompt/index.module.scss';

let dialog: any = null;

export const popupDialog = ({ ...param }) => {
  /**
   * @param {string} title 弹窗标题
   * @param {string} message 提示信息
   * @param {boolean} isShowButton 是否显示按钮
   * @param {ReactNode || false} iconNode 自定义icon/为false表示隐藏icon
   * @param {string} url 自定义按钮点击跳转的url
   * @param {boolean} newOpen 是否新开窗口
   * @param {string} label 自定义按钮文案
   * @param {ReactNode} buttonComponent 自定义按钮，替换关闭按钮
   * @param {function} method 自定义按钮点击方法
   * @param {object} methodParam 点击方法入参
   * @param {boolean} hideTime 是否隐藏时间
   */
  const { title, message, isSuccess, isShowButton, iconNode, url, newOpen, label, buttonComponent, method, methodParam, hideTime } = param;
  if (dialog) {
    return;
  }

  dialog = Dialog.show({
    title: false,
    onClose: () => {
      dialog.hide();
      dialog = null;
    },
    content: (
      <>
        <div
          className={styles.dialogContainer}
          style={{
            height: isShowButton ? '114px' : '146px',
            justifyContent: isSuccess && !isShowButton ? 'center' : 'flex-start',
            alignItems: isSuccess && !isShowButton ? 'center' : 'normal',
          }}
        >
          <div className={styles.dialogTitle}>
            {
              iconNode ?
                <></>
                :
                <Icon type={isSuccess ? 'success' : 'warning'} size="xl" style={{ color: isSuccess ? '#63BA4D' : '#F15533' }} />
            }
            {title}
          </div>
          {
            message ?
              <div className={styles.dialogMessage}>
                {message}
              </div>
              :
              null
          }
        </div>
        {
          !isShowButton ?
            <span className={styles.footerTip}>
              {hideTime ? '' : '3秒后自动关闭'}
            </span>
            :
            <div className={styles.buttonGroup}>
              <Button
                type="primary"
                style={{ marginRight: '12px', padding: '0 20px' }}
                onClick={() => {
                  if (method) {
                    methodParam ? method({ ...methodParam }).then((rst) => { rst?.success && window.location.reload(); }) : method();
                  }
                  if (url) {
                    newOpen ? (window.open(url)) : (window.location.href = url);
                  }
                  dialog.hide();
                  dialog = null;
                }}
              >{label}
              </Button>
              {
                buttonComponent ?
                  <></>
                  :
                  <Button
                    type="normal"
                    style={{ padding: '0 20px', border: '1px solid #D8D8D8', color: '#999' }}
                    onClick={() => {
                      dialog.hide();
                      dialog = null;
                    }}
                  >
                    关闭
                  </Button>
              }
            </div>
        }
      </>
    ),
    footer: false,
  });

  if (dialog && !isShowButton && !hideTime) {
    setTimeout(() => {
      dialog && dialog.hide();
      dialog = null;
    }, 3000);
  }
};
