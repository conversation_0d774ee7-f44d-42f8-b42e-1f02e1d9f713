/**
 * 图片初始展示处理方法
 * @param data img数据
 * @param zoomIn img是否处于放大状态
 * @returns
 */
function computeImgStyle(data, zoomIn) {
  let styleObj = {} as any;
  if ((data?.width / data?.height) > (window.innerWidth / window.innerHeight)) {
    styleObj = { width: '100%', height: 'auto' };
  } else {
    styleObj = { width: 'auto', height: '100%' };
  }
  styleObj.cursor = zoomIn ? 'zoom-in' : 'grab';
  return styleObj;
}

/**
 * 给教程文档中的图片添加背景容器
 * @param imgDOM imgDOM
 */
function addImgContainer(imgDOM) {
  const newDiv = document.createElement('div');
  newDiv?.classList?.add('help-tutorial-img-container');
  imgDOM?.parentNode?.insertBefore(newDiv, imgDOM);
  imgDOM?.parentNode?.removeChild(imgDOM);
  newDiv.appendChild(imgDOM);
}

export { computeImgStyle, addImgContainer };
