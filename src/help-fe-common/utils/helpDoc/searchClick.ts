import { triggerUrlBehavior } from '@/help-fe-common/utils/global/url/urlBehavior';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import { addStyle } from '@/help-fe-common/utils/global/style/addStyle';

/**
 * 点击suggest中链接
 * @param e
 * @param url 需要跳转的链接
 * @param history history对象
 * @param behavior 链接类型：document,product,contentpioneer
 * @param resultIndex 当前搜索结果序号
 * @param keywords 当前搜索关键词
 */
export const onUrlClick = (e, url, history, behavior, resultIndex?, keywords?) => {
  e.preventDefault();
  e.stopPropagation();
  triggerUrlBehavior(url, history, { from: 'search' }, e.target);
  let slsParam = {};
  if (behavior === 'document' && resultIndex !== undefined) {
    slsParam = { resultIndex };
  }
  sendSlsLog({ page: '', action: 'click', section: 'SuggestUrl', keywords, userParams1: behavior, userParams2: url, ...slsParam });
};

/**
 * 点击页面其他地方取消搜索区域显示
 * @param e 点击事件
 * @param className 搜索
 * @param onClose 取消搜索区域显示事件
 */
export const onCancelClick = (e, className, onClose) => {
  const target: any = e?.target;
  const searchDOM = document.getElementsByClassName(className)[0];
  if (!(target === searchDOM) && !searchDOM?.contains(target)) {
    if (onClose) {
      onClose();
    }
  }
};

export const onUrlFocus = (e, url, behavior, resultIndex?, keywords?) => {
  e.preventDefault();
  e.stopPropagation();

  let slsParam = {};
  if (resultIndex !== undefined) {
    slsParam = { resultIndex };
  }
  sendSlsLog({ page: '', action: 'focus', section: 'SuggestUrl', keywords, userParams1: behavior, userParams2: url, ...slsParam });
};

/**
 *  显示sideSearch
 */
export const showSideSearch = () => {
  const sideSearchContainer = document.querySelector('.help-side-search');
  addStyle(sideSearchContainer, 'display', 'block');
};

/**
 * 隐藏sideSearch，而非销毁组件
 */
export const hideSideSearch = () => {
  const sideSearchContainer = document.querySelector('.help-side-search');
  addStyle(sideSearchContainer, 'display', 'none');
};
