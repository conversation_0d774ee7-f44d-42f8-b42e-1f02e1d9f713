import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';

const elementTags = [
  'p',
  'li',
  'blockquote',
  'b',
  'span',
  'th',
  'td',
  'h1',
  'h2',
  'h3',
  'h4',
  'h5',
  'h6',
];

/**
 * 向指定选择器下的元素内容中添加中文与非中文字符间的空格标记。
 *
 * @param {Element} rootNode - 根节点，用于查找子元素。
 * @param {string} selectors - CSS 选择器字符串，用于匹配需要处理的元素。
 */
const addSpacing = (rootNode: Element | null, selectors: string): void => {
  if (!rootNode) return;

  // 获取所有符合条件的元素
  const elements = rootNode.querySelectorAll(selectors);

  // 定义空格标记
  const spaceMarker = '<span class="help-letter-space"></span>';

  // 遍历每个元素，处理其中的内容
  elements.forEach((element) => {
    let content = element.innerHTML;

    // https://aliyuque.antfin.com/gcsdev/pclx4o/nwk38gui2ztipoxy
    // 匹配中文字符和b、span、a标签之间的空格
    const chineseToTagRegex = /([\u4e00-\u9fa5])(<(b|span|a)[^>]*>)([a-zA-Z0-9])(?! )/;
    const tagToChineseRegex = /([a-zA-Z0-9])(<\/(b|span|a)>)([\u4e00-\u9fa5])(?! )/;
    // 短语、界面词、a链接中存在中文
    const enToPhRegex = /([a-zA-Z0-9])(<(b|span|a)[^>]*>)([\u4e00-\u9fa5])(?! )/;
    const phToEnRegex = /([\u4e00-\u9fa5])(<\/(b|span|a)>)([a-zA-Z0-9])(?! )/;
    // 短语、界面词连在一起
    const complexTagRegex = /([\u4e00-\u9fa5])(<\/(b|span|a)>)(<(b|span|a)[^>]*>)([a-zA-Z0-9])(?! )/;
    const reverseComplexTagRegex = /([a-zA-Z0-9])(<\/(b|span|a)>)(<(b|span|a)[^>]*>)([\u4e00-\u9fa5])(?! )/;
    // 短语、界面词嵌套
    const nestedTagForRegex = /([\u4e00-\u9fa5])(<(b|span|a)[^>]*>)(<(b|span|a)[^>]*>)([a-zA-Z0-9])(?! )/;
    const nestedTagBackRegex = /([a-zA-Z0-9])(<\/(b|span|a)>)(<\/(b|span|a)>)([\u4e00-\u9fa5])(?! )/;

    // 应用正则表达式替换规则
    content = applyReplacement(content, chineseToTagRegex, `$1${spaceMarker}$2$4`);
    content = applyReplacement(content, tagToChineseRegex, `$1$2${spaceMarker}$4`);
    content = applyReplacement(content, enToPhRegex, `$1${spaceMarker}$2$4`);
    content = applyReplacement(content, phToEnRegex, `$1$2${spaceMarker}$4`);
    content = applyReplacement(content, complexTagRegex, `$1$2${spaceMarker}$4$6`);
    content = applyReplacement(content, reverseComplexTagRegex, `$1$2${spaceMarker}$4$6`);
    content = applyReplacement(content, nestedTagForRegex, `$1$2${spaceMarker}$4$6`);
    content = applyReplacement(content, nestedTagBackRegex, `$1$2${spaceMarker}$4$6`);

    // 处理普通文本中的中文与非中文字符连接
    content = content.replace(/([\u4e00-\u9fa5])([a-zA-Z0-9])/g, (match, p1, p2, offset, str) =>
      (!isInAttribute(str, offset) ? `${p1}${spaceMarker}${p2}` : match));
    content = content.replace(/([a-zA-Z0-9])([\u4e00-\u9fa5])/g, (match, p1, p2, offset, str) =>
      (!isInAttribute(str, offset) ? `${p1}${spaceMarker}${p2}` : match));

    element.innerHTML = content;
  });
};

// 辅助函数：应用替换规则
function applyReplacement(text: string, regex: RegExp, replacement: string): string {
  return text.replace(new RegExp(regex.source, 'g'), replacement);
}

// 辅助函数：判断是否在标签属性中
function isInAttribute(string: string, offset: number): boolean {
  return string.lastIndexOf('<', offset) > string.lastIndexOf('>', offset);
}

/**
 * 判断是否支持前瞻断言
 * @returns
 */
const isRegexpSupported = () => {
  try {
    const regex = new RegExp('a(?!b)');
    return !!regex;
  } catch (e) {
    return false;
  }
};

const addSpace = (content) => {
  try {
    const parser = new DOMParser();
    const dom = parser?.parseFromString(content || '', 'text/html');
    const contentBodyDom = dom.querySelector('div.icms-help-docs-content');
    if (!contentBodyDom) return content;

    if (isRegexpSupported()) {
      // 对 p 和 blockquote 元素应用间距调整
      addSpacing(contentBodyDom, elementTags?.join(','));
    }
    return contentBodyDom?.outerHTML;
  } catch (error) {
    sendSlsLog({ page: 'documentDetail', section: '', action: 'error', userParams1: 'add spacing error' });
    return content;
  }
};

const addInitContentSpace = (data, content) => {
  const start = new Date().getTime();
  content = addSpace(content);
  const end = new Date().getTime();
  sendSlsLog({ page: 'documentDetail', section: '', action: 'addSpace', userParams1: `${end - start}ms` });
  return content;
};

export default addInitContentSpace;
