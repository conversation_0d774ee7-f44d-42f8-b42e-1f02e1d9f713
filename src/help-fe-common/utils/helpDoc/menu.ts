
import { SHOW_TYPE, defaultMenuExpandConfig } from '@/help-fe-common/constants/detail';

/**
 * 判断是否为子节点
 * @param {enum} showType 节点类型
 * @returns {boolean}
 */
export function isSubProduct(showType) {
  return (showType === SHOW_TYPE.SUB_PRODUCT);
}

/**
 * 解析产品信息及目录列表信息
 * @param data 目录树信息
 * @returns {object} productInfo产品信息, treeList 目录列表
 */
export function extractTreeList(data) {
  if (data) {
    const { children, ...rest } = data;
    return {
      productInfo: rest,
      treeList: children,
    };
  }
  return {
    productInfo: null,
    treeList: [],
  };
}

/**
 * 判断当前节点是否包含某个节点
 */
const isNodeInTargets = (node, targetIds) => {
  if (!node) return false;
  if (targetIds?.includes(node?.id)) {
    return true;
  }
  if (node?.children) {
    for (const child of node.children) {
      if (isNodeInTargets(child, targetIds)) {
        return true;
      }
    }
  }
  return false;
};


/**
 * 根据当前点击的id，给每个数据添加一个open字段，控制菜单的展开收起
 * @param {Array} menuListData 目录列表
 * @param {Array} menuStack 高亮的目录栈
 * @param {boolean} expandAll 是否展开全部
 * @param {string} behavior 'click' 点击行为
 * @param {enum} showType 判断是否为子节点
 * @param {string} productId 产品id
 * @returns {Array} 添加展开、高亮信息的目录树
 */
export const changeOpenStatus = (menuListData, menuStack, expandAll, behavior?, showType?, productId?) => {
  const subProductFlag = isSubProduct(showType);
  // 判断是否为初始化调用
  const isInitCall = (typeof expandAll !== 'boolean');
  // 是否是点击行为
  const isClick = (behavior === 'click');

  const shouldExpandNodeArr = defaultMenuExpandConfig?.[productId] || [];

  /**
   * 根据产品配置判断是否展开节点
   * @param {object} item 当前节点
   * @param {number} level 当前节点层级
   * @param {Array} parentIndexArr 父节点索引数组
   * @returns {object} 添加展开信息的节点
   */
  const shouldExpand = (item, level, parentIndexArr) => {
    if (!isInitCall || isClick) return null;

    const isUserGuideNode = item?.alias?.indexOf('/user-guide') !== -1;

    // 如果产品配置存在，则优先走产品配置展开逻辑
    if (shouldExpandNodeArr?.length > 0) {
      if (isNodeInTargets(item, shouldExpandNodeArr)) {
        return {
          ...item,
          pathArr: parentIndexArr,
          open: true,
          children: deep(item?.children, level + 1, parentIndexArr),
        };
      }
    } else if (isUserGuideNode &&
      (item?.level === 4 || (item?.level === 5 && subProductFlag))) {
      // 初始化调用，且不是点击行为时，第一级目录/第二级目录（子产品）为user-guide。操作指南默认展开
      return {
        ...item,
        pathArr: parentIndexArr,
        open: true,
        children: deep(item?.children, level + 1, parentIndexArr),
      };
    }
  };

  /**
   * 深层遍历树，添加open参数控制展开/收起
   * @param list 当前层级data
   * @param {number} level 当前节点层级
   * @param {Array} parentIndexArr 父节点索引数组
   * @returns {Array} 添加展开信息的目录树
   */
  const deep = (list, level, parentIndexArr) => {
    return list?.map((item, index) => {
      const newPathArr = [...parentIndexArr, index];
      if (!item?.children) {
        item.open = false;
        return { ...item, pathArr: newPathArr };
      }

      // 根据产品展开配置选择是否默认展开节点
      const data = shouldExpand(item, level, newPathArr);
      if (data) return data;

      /**
       * 点击行为的展开逻辑
       */
      if (!isInitCall && item?.level - 3 <= 1) {
        return {
          ...item,
          pathArr: newPathArr,
          open: expandAll,
          children: deep(item?.children, level + 1, newPathArr),
        };
      } else if (item?.id == menuStack?.[level - 1]?.id) {
        return {
          ...item,
          pathArr: newPathArr,
          open: level < menuStack?.length ? true : !item?.open,
          children: deep(item?.children, level + 1, newPathArr),
        };
      } else {
        return {
          ...item,
          pathArr: newPathArr,
          children: deep(item?.children, level + 1, newPathArr),
        };
      }
    });
  };
  return deep(menuListData, 1, []);
};

export function getHighlightMenuStack(menuTreeList) {
  if (!menuTreeList || menuTreeList?.length === 0) {
    return [];
  }
  const deep = (data, stack) => {
    if (!data) {
      return [];
    } else if (Array.isArray(data)) {
      let target: any[] = [];
      data?.forEach((item) => {
        if (!item) {
          return;
        }
        const { children, ...nodeInfo } = item;
        if (nodeInfo?.selected) {
          target = [...stack, nodeInfo];
        } else if (children && children?.length > 0) {
          const findRst = deep(children, [...stack, nodeInfo]);
          if (findRst && findRst?.length > 0) {
            target = findRst;
          }
        }
      });
      return target;
    } else if (data?.selected) {
      return [...stack, data];
    } else {
      return [];
    }
  };
  const menuStack = deep(menuTreeList, []);
  return menuStack;
}

export function getPaginationDocs(menuListData) {
  const deepFirstSearch = (data, result) => {
    if (data) {
      const { children, ...nodeInfo } = data;
      result.push(nodeInfo);
      if (children && children?.length) {
        for (let i = 0; i < children.length; i++) {
          deepFirstSearch(children[i], result);
        }
      }
    }
    return result;
  };
  const nodeList = deepFirstSearch(menuListData, []);
  // 由于kb_list，video_list 的详情页在菜单上不显示，无法高亮，通过后端处理，标记当前高亮内容；
  const index = nodeList.findIndex((item) => item?.selected);
  return [nodeList[index - 1], nodeList[index + 1]];
}

/**
 * 从html模版中获取目录树模板内容，作为menu组件初始化展示
 */
export function setMenuTreeInfo() {
  window.menuTreeInfo = {
    productTitle: document.querySelector('.menu-title-text')?.textContent || '',
    menuTreeHtml: document.getElementById('common-menu-container')?.innerHTML || '',
    subProductHtml: document.querySelector('.help-menu-subproduct')?.innerHTML || '',
  };
}

/**
* 生成目录spmd，按照d_[index1]_[index2]规则生成
*/
export const generateSpmd = (spmArray) => {
  return `d_${(spmArray || [])?.join('_')}`;
};

/**
 * 替换scm中的hash值，防止hash值影响链接跳转
 * @param scm
 * @returns
 */
export const replaceScmHash = (scm) => {
  return scm?.replace(/#/g, '-');
};
