/** 固定布局相关方法 */
import { headHeight } from '@/help-fe-common/constants';
import { addStyle, addNodeListStyle } from '@/help-fe-common/utils/global/style/addStyle';
import { getCssNumber } from '@/help-fe-common/utils/global/style/cssValue';

export function followScroll() {
  const docsContainer = document.getElementById('docs-container');
  const clientRect = docsContainer?.getBoundingClientRect();
  const { innerHeight } = window;
  const top = clientRect?.top || 0;
  const aliyunDocsToc = document.querySelector('.aliyun-docs-toc-content'); // 目录树
  const aliyunDocsSynopsis = document.querySelector('.aliyun-docs-side-content'); // 正文右侧区域
  const fixedTop = `${headHeight - innerHeight + Number(clientRect?.bottom)}px`;
  const menuSearchOffset = 100 + headHeight;
  if (top <= 0) {
    docsContainer?.classList?.add('fixed');
    if (Number(clientRect?.bottom) < innerHeight) {
      addNodeListStyle([aliyunDocsToc, aliyunDocsSynopsis], 'top', fixedTop);
    } else {
      addNodeListStyle([aliyunDocsToc, aliyunDocsSynopsis], 'top', `${headHeight}px`);
      // 导读固定时，高度为100vh-子导航高度
      addNodeListStyle([aliyunDocsSynopsis], 'height', `calc(100vh - ${headHeight}px )`);
    }
  } else {
    docsContainer?.classList?.remove('fixed');
    addNodeListStyle([aliyunDocsToc, aliyunDocsSynopsis], 'top', '0px');
    // 导读未固定时，目录树高度为100vh-子导航高度-距离顶部高度
    addNodeListStyle([aliyunDocsSynopsis], 'height', `calc(100vh - ${top + headHeight}px )`);
  }
  // 右侧目录高亮标题始终在可视区域
  const currentSynopsisDOM = document.getElementsByClassName('current-synopsis')[0];
  const scrollDown = currentSynopsisDOM?.getBoundingClientRect()?.top + 32 - document.documentElement.clientHeight;
  const scrollUp = currentSynopsisDOM?.getBoundingClientRect()?.top - 54;
  if (scrollDown >= -10) {
    aliyunDocsSynopsis?.scrollBy({ left: 0, top: scrollDown + 10 });
  } else if (scrollUp < 0) {
    aliyunDocsSynopsis?.scrollBy({ left: 0, top: scrollUp });
  }
}

/**
 * 首页固定布局滚动到产品列表
 * @param { string } { scrollType: 'smooth' | 'instant' }
 * @returns
 */
export const scrollToSearch = (scrollType?) => {
  const anchorElement = document.getElementById('help-index-product-list') as HTMLElement;
  const top = getCssNumber('--default-nav-height');
  document.documentElement.scrollTo({ left: 0, top: anchorElement.offsetTop + top - headHeight, behavior: scrollType || 'smooth' });
};
