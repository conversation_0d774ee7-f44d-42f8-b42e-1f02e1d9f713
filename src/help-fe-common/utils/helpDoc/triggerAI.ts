import { hideSelection } from '@/help-fe-common/utils/helpDoc/selection';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';

export enum EQuestionFrom {
  SUMMARY = 'summary',
  UNDERLINE = 'underline'
}

interface SelectionInfo {
  productCode: string;
  nodeId: number;
  url: string;
  title: string;
  breadCrumb?: string;
  relatedContent?: string;
}

export const onAIClick = async (promptType, selectionInfo: SelectionInfo, prompt) => {
  const { url, title, nodeId, productCode, breadCrumb, relatedContent } = selectionInfo;

  if (promptType === EQuestionFrom.UNDERLINE) {
    // 埋点
    sendSlsLog({ page: 'documentDetail', section: 'selection', action: 'aiClick', userParams1: prompt, userParams2: productCode });
  } else {
    const docTitle = '';
    prompt = `学习并总结《${docTitle}》`;
  }

  hideSelection();

  const aiAssistant = window?.AIAssistant;
  if (!aiAssistant) return;
  aiAssistant.dispatchAction({
    payload: {
      prompt,
      context: {
        questionFrom: promptType,
        bizType: 'doc',
        productCode,
        url,
        title,
        extra: {
          articleId: nodeId,
          category: breadCrumb,
          relatedContent,
        },
      },
    },
    action: 'sendMessage',
  });
};
