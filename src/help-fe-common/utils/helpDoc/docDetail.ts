import { setCssValue } from '@/help-fe-common/utils/global/style/cssValue';

/**
 * 文档详情处理方法
 * @param docData 文档数据
 * @returns
 */
export function extractDocumentDetail(docData) {
  if (!docData) {
    return { breadcrumb: [], docInfo: null };
  }
  const { directoryPath, ...docInfo } = docData;
  const deep = (data, stack) => {
    if (!data) {
      return stack;
    }
    const { children, ...nodeInfo } = data;
    stack = [...stack, nodeInfo];
    if (children && children.length > 0) {
      stack = deep(children[0], stack);
    }
    return stack;
  };
  const stack = deep(directoryPath, []);
  return { breadcrumb: stack, docInfo };
}

/**
 * 从html模版中读取文档正文高度，并设置为style变量
 */
export function setDocumentScrollHeight() {
  const docDOM = document.querySelector('.aliyun-docs-content');
  const scrollHeight = docDOM?.scrollHeight || window?.screen?.height;
  setCssValue('--help-doc-height', `${scrollHeight}px`);
}

/**
 * 一键部署等模式下，控制文档右侧边展示形态
 * @param visible 展开/收起
 * @returns
 */
export function onShowDocSide(visible?: boolean) {
  const docSideContainerDom = document.getElementById('aliyun-docs-side');
  const docSideDom = document.getElementById('aliyun-docs-side-content');
  const docContentDom: any = document.querySelector('.aliyun-docs-content');

  if (!docSideDom || !docContentDom || !docSideContainerDom) return;
  if (visible) {
    docSideDom.style.display = 'none';
    docSideContainerDom.classList.add('aliyun-docs-column-side');
    docContentDom.classList.add('aliyun-docs-column-content');
    onShowMenuSide(0);
  } else {
    docSideDom.style.display = 'block';
    docSideContainerDom.classList.remove('aliyun-docs-column-side');
    docContentDom.classList.remove('aliyun-docs-column-content');
    onShowMenuSide(2);
  }
}

/**
 * 一键部署等模式下，控制左侧目录展示形态
 * @param visible 0收起/1展开/2默认形态
 * @returns
 */
export function onShowMenuSide(visible?: number) {
  const menuContainerDom = document.getElementById('aliyun-docs-menu');
  const menuDom = document.querySelector('.aliyun-docs-toc-content') as HTMLElement;
  if (!menuContainerDom || !menuDom) return;
  if (visible === 0) {
    menuContainerDom.classList.add('aliyun-docs-column-menu');
    menuDom.style.visibility = 'hidden';
  } else if (visible === 1) {
    menuDom.style.visibility = 'visible';
  } else {
    menuContainerDom.classList.remove('aliyun-docs-column-menu');
    menuDom.style.visibility = 'visible';
  }
}
