import { NODE_TYPE } from '@/help-fe-common/constants/detail';

/**
 * 产品筛选公用逻辑
 * @param productList 产品列表
 * @param filterRule 筛选规则
 * @returns
 */
const commonFilterList = (productList, filterRule) => {
  return productList
    .map((mainNodeData) => {
      const { children, ...nodeInfo } = mainNodeData;
      return {
        ...nodeInfo,
        children: children
          .map((subNodeData) => {
            if (subNodeData?.nodeType === NODE_TYPE.PRODUCT_NODE && filterRule(subNodeData)) {
              return subNodeData;
            } else if (subNodeData?.nodeType === NODE_TYPE.CATEGORY_NODE) {
              return {
                ...subNodeData,
                children: subNodeData?.children?.filter(filterRule),
              };
            } else {
              return {};
            }
          })
          .filter((v) => (v?.nodeType === NODE_TYPE.PRODUCT_NODE || v?.children?.length > 0)),
      };
    })
    .filter((v) => v?.children?.length > 0);
};

/**
 * 处理首页产品列表筛选
 * @param val 搜索关键词
 * @param productList 产品列表
 * @returns
 */
const getIndexFilterProductList = (val, productList) => {
  if (!val) {
    return productList;
  }
  const lowerCaseVal = val?.toLowerCase();
  const matchTitle = (item) => item?.title?.toLowerCase().indexOf(lowerCaseVal) > -1;
  // const matchDesc = (item) => item?.desc?.toLowerCase().indexOf(lowerCaseVal) > -1;
  const matchShortName = (item) => item?.shortName?.split('/').join('')?.toLowerCase() === lowerCaseVal;
  const matchSubProduct = (item) => {
    return item?.children?.some((subItem) => {
      return subItem?.title?.toLowerCase().indexOf(lowerCaseVal) > -1;
    });
  };

  const filterRule = (item) =>
    matchTitle(item) || matchShortName(item) || matchSubProduct(item);

  return commonFilterList(productList, filterRule);
};

/**
 * 产品列表筛选
 * @param val 关键词
 * @param productList 产品列表
 * @returns
 */
const getFilterProductList = (val, productList) => {
  if (!val) {
    return productList;
  }
  const lowerCaseVal = val?.toLowerCase();
  const filterRule = (item) =>
    item?.title?.toLowerCase().indexOf(lowerCaseVal) > -1 || item?.alias?.toLowerCase().indexOf(lowerCaseVal) > -1;

  return commonFilterList(productList, filterRule);
};

/**
 * 目录树筛选
 * @param val 关键词
 * @param menuTreeList 目录树
 * @returns
 */
const getFilterMenuList = (val, menuTreeList) => {
  const filterData = [] as any;
  if (!val && menuTreeList?.length) {
    return [];
  }
  const lowerCaseVal = val?.toLowerCase();
  const filterRule = (item) =>
    item?.title?.toLowerCase().indexOf(lowerCaseVal) > -1 || item?.title?.indexOf(val) > -1;
  const deep = (data) => {
    if (!data) {
      return;
    }
    if (Array.isArray(data)) {
      data?.forEach((item) => {
        if (!item) {
          return;
        }
        const { children, ...nodeInfo } = item;
        if (filterRule(item)) {
          nodeInfo.title = nodeInfo?.title?.replace(new RegExp(`(${val}|${val?.toLowerCase()}|${val?.toUpperCase()})`), '<em>$1</em>');
          filterData.push(nodeInfo);
        }
        if (children && children?.length > 0) {
          deep(children);
        }
      });
    }
  };
  deep(menuTreeList);
  return filterData;
};

/**
 * 移动端目录筛选
 * @param menuList 目录列表
 * @param keyword 关键词
 * @returns
 */
const getFilterMobileMenuList = (menuList, keyword) => {
  const lowerCaseVal = keyword?.toLowerCase();
  const filterRule = (item) => item?.title?.toLowerCase().indexOf(lowerCaseVal) > -1;

  if (!keyword) {
    return menuList;
  }
  const deep = (data, stack, hitRst) => {
    if (Array.isArray(data)) {
      data?.forEach((item) => deep(item, [...stack], hitRst));
    } else if (filterRule(data)) {
      const { children, ...nodeInfo } = data;
      hitRst.push([...stack, nodeInfo]);
    } else if (data?.children) {
      const { children, ...nodeInfo } = data;
      deep(children, [...stack, nodeInfo], hitRst);
    }
  };
  const hitRst = [];
  deep(menuList, [], hitRst);
  const finalRst = {};

  hitRst?.forEach((item: any) => {
    finalRst[item[0].id] = item[0];
  });

  return hitRst;
};

export { getFilterProductList, getFilterMenuList, getFilterMobileMenuList, getIndexFilterProductList };
