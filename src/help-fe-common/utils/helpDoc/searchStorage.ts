/**
 * 获取产品信息，包含id、name，用作搜索入参
 * @returns productInfo:{"id":25365,"name":"云服务器 ECS"}
 */
const getProductInfo = () => {
  try {
    const productInfo = JSON.parse(window.localStorage.getItem('productInfo') || '');
    return productInfo;
  } catch (error) {
    return '';
  }
};

const deleteProductInfo = () => {
  if (getProductInfo()) {
    window.localStorage.removeItem('productInfo');
  }
};

/**
 * 设置搜索关键字款村
 * @param keywords 关键字
 * @param from 关键字来源：all/product/practice
 */
const setLocalSearchValue = (keywords, from) => {
  window.localStorage.setItem('searchValue', JSON.stringify({ keywords, from }));
};

/**
 * 获取搜索关键字，包含keywords、from，用作sideSearch默认打开逻辑判断
 * @returns searchValue:{"keywords":"ecs","from":"product"}
 */
const getLocalSearchValue = () => {
  try {
    const searchValue = JSON.parse(window.localStorage.getItem('searchValue') || '');
    return searchValue;
  } catch (error) {
    return '';
  }
};

/**
 * 移除搜索关键字
 */
const removeLocalSearchValue = () => {
  // 首页中搜索时清除缓存
  if (getLocalSearchValue()) {
    window.localStorage.removeItem('searchValue');
  }
};

export { getProductInfo, deleteProductInfo, getLocalSearchValue, setLocalSearchValue, removeLocalSearchValue };
