/**
 * 自定义转码规范，转义html中标签
 * @param str 需要转码的html string
 * @returns
 */
const escapeHtmlTag = (str: string) => {
  if (!str) return '';
  return str
    .replace(/<script/g, '&lt-help;script')
    .replace(/<\/script>/g, '&lt-help;/script&gt-help;');
};

const unescapeHtmlTag = (str: string) => {
  if (!str) return '';
  return str
    .replace(/&lt-help;script/g, '<script')
    .replace(/&lt-help;\/script&gt-help;/g, '</script>');
};


export { escapeHtmlTag, unescapeHtmlTag };
