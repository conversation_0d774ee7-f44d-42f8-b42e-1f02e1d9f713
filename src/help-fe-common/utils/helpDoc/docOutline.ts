/** 本文导读数据处理方法 */
import { deleteNode } from '@/help-fe-common/utils/global/dom/deleteNode';
import { isServer } from '@/help-fe-common/utils/node/getContext';

/**
 * 获取当前节点id，如果有父节点id且唯一，使用父节点id，否则使用自身id
 * 背景：部分文档希望使用section上的语义化id而非h2/h3标题上的随机id
 * @param hArr h标签dom数组
 * @param hNode 需要判断的当前节点
 * @returns 唯一id标识
 */
const getUniqueId = (hArr, hNode) => {
  const parentNode = hNode?.parentNode as HTMLElement;
  const parentNodeId = parentNode?.getAttribute('id');
  let id;
  // 当前节点的父节点id存在且与dom数组中父节点id不重复，使用父节点id
  if (parentNodeId && !hArr?.some((child) =>
    (child?.parentNode as HTMLElement)?.getAttribute('id') === parentNodeId && child?.id !== hNode?.id)
  ) {
    id = parentNodeId;
  } else id = hNode?.getAttribute('id');
  return id;
};

/**
 * 从html中抽取h2、h3构建导读数据
 * @param content 文档html内容
 * @returns {array} 导读数据
 */
function extractSynopsisData(content) {
  if (!content) return [];
  const parser = new DOMParser();
  const dom = parser?.parseFromString(content || '', 'text/html');
  // 需对tabbed-content-box内容过滤
  if (!isServer()) deleteNode(dom, 'tabbed-content-box section');
  const h2DOMArr = Array.from(dom.getElementsByTagName('h2'));
  const h3DOMArr = Array.from(dom.getElementsByTagName('h3'));
  const h2Stack: any[] = [];
  const res: any[] = [];
  let h2Children: any[] = [];
  Array.from(dom.querySelectorAll('h2, h3')).forEach((hItem) => {
    if (hItem?.nodeName === 'H2') {
      if (h2Stack?.length) {
        res.push({ ...h2Stack.pop(), children: h2Children });
      }
      // h2标签父节点id唯一时，使用父节点id
      const h2Id = getUniqueId(h2DOMArr, hItem);
      h2Stack.push({
        id: h2Id,
        text: (hItem as HTMLElement)?.innerText,
      });
      h2Children = [];
    } else if (hItem?.nodeName === 'H3') {
      // h3标签父节点id唯一时，使用父节点id
      const h3Id = getUniqueId([...h2DOMArr, ...h3DOMArr], hItem);
      h2Children.push({
        id: h3Id,
        text: (hItem as HTMLElement)?.innerText,
      });
    }
  });
  if (h2Stack?.length) {
    res.push({ ...h2Stack.pop(), children: h2Children });
  }
  return res?.filter((item) => item?.id && item?.text);
}
/**
 * 查找当前导读高亮id
 * @param synopsis 导读数据
 * @returns
 */
function findCurrentSynopsis(synopsis) {
  const flatData = [] as any;
  synopsis.forEach((item) => {
    const { children, ...info } = item;
    if (children && children?.length) {
      flatData.push({ ...info }, ...children);
    } else flatData.push(item);
  });
  const re: any = flatData?.filter((item, index) => {
    const hDOM = document.getElementById(item?.id) as HTMLElement;
    const client = hDOM?.getBoundingClientRect();
    const nextClient = (document.getElementById(flatData[index + 1]?.id) as HTMLElement)?.getBoundingClientRect();
    if (nextClient) {
      return client?.top > 58 || nextClient?.top - 1 > 58;
    } else {
      return client?.top > 58;
    }
  })?.[0];
  if (re) {
    return re?.id;
  } else {
    return flatData[flatData?.length - 1]?.id;
  }
}

function clickToHash(id) {
  // 点击事件只改变网址不跳转
  const newUrl = `${window.location.href.split('#')[0]}#${id}`;
  const hashDom = document.getElementById(id);
  if (hashDom) {
    hashDom.scrollIntoView();
  }
  history.pushState('', '', newUrl);
}

export { extractSynopsisData, findCurrentSynopsis, clickToHash };
