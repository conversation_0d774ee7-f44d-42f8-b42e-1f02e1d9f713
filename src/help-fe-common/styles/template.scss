// 模板输出，会闪动，考虑seo，故而设置透明度为0
.aliyun-app-layout {
  opacity: 0;
}

// 专门用于详情页服务端html模板样式
.main-layout {
  min-height: 100vh;
  margin: 0 auto;
  position: relative;

  .help-body-head {
    box-shadow: inset 0 -1px 0 0 rgba(61, 61, 61, 0.15);
    height: 40px;
    width: 100%;
  }

  .stick-top-layout {
    margin: 0 auto;
    position: relative;

    .help-layout {
      box-sizing: border-box;
      position: relative;
      overflow: visible !important;
      display: flex;
      min-height: 100vh;

      .aliyun-docs-toc {
        flex: 0 0 300px;
        background: #fff;
        max-height: 100vh;
      }

      .aliyun-docs-view {
        .product-detail {
          position: relative;
          flex: 1;
          display: flex;
          width: 0;

          .aliyun-docs-content {
            .aliyun-docs-view-header {
              .header-topbar {
                display: flex;
                flex-wrap: nowrap;
                justify-content: space-between;
                height: 18px;

                .header-topbar-breadcrumb {
                  font-size: 12px;
                  line-height: 18px;
                  display: flex;
                  align-items: center;
                  flex-wrap: wrap;

                  span {
                    color: $text-second-color;
                    padding: 0 2px;
                  }

                  a {
                    margin-right: 20px;
                    color: $text-color;
                    cursor: pointer;

                    &:hover {
                      text-decoration: none;
                      color: $text-link-color;
                    }
                  }
                }
              }

              .header-title {

                h1 {
                  margin-top: 24px;
                  margin-bottom: 16px;
                  font-weight: 700;
                  font-size: 34px;
                  color: #181818;
                  letter-spacing: 0.4px;
                  line-height: 44px;
                  word-break: break-word;
                  display: flex;
                  align-items: center;
                }

                .header-actionbar {
                  height: 24px;
                  margin-top: 6px;
                  font-size: 12px;
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  position: relative;

                  .update-time {
                    color: #999;
                  }
                }
              }
            }

            .pc-markdown-container {
              .help-video-list {
                display: flex;
                -ms-flex-wrap: wrap;
                -webkit-flex-wrap: wrap;
                flex-wrap: wrap;
                justify-content: flex-start;
                margin-left: -35px;
                padding-bottom: 30px;

                .help-video-item {
                  width: 265px;
                  margin-left: 35px;
                  margin-top: 30px;

                  .video-item-image {
                    position: relative;
                    display: block;
                    width: 100%;
                    height: 145px;
                    cursor: pointer;
                    $scale: 1;

                    .image-container {
                      position: relative;
                      width: 252*$scale+px;
                      height: 165*$scale+px;
                      border: 1px solid rgba(237, 237, 237, 1);

                      .cover-imgage {
                        width: 100%;
                        height: 100%;
                      }

                      .title-container {
                        position: absolute;
                        left: 0;
                        bottom: 0;
                        height: 40%;
                        width: 100%;
                        line-height: 40%;
                        display: flex;
                        align-items: center;

                        .image-title {
                          padding-left: 20*$scale+px;
                          font-size: 14*$scale+px;
                          color: #181818;
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                          line-height: 24px;
                        }
                      }

                      .play-button {
                        position: absolute;
                        transform: translate(0, -50%);
                        top: 65%;
                        right: 20*$scale+px;
                        width: 50*$scale+px;
                        height: 55*$scale+px;
                        background-image: url(https://img.alicdn.com/imgextra/i2/O1CN01DpqkdV1jp13mgn9J5_!!6000000004596-2-tps-100-110.png);
                        background-size: 100% 100%;
                        background-repeat: no-repeat;
                        transition: all .3s ease-in-out;
                      }

                      &:hover {
                        box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.1);

                        .play-button {
                          background-image: url(https://img.alicdn.com/imgextra/i3/O1CN01fjYYXD1lfW4fk4Diq_!!6000000004846-2-tps-100-110.png);
                          transition: all .3s ease-out;
                        }

                        .image-title {
                          color: #ff791a;
                        }
                      }
                    }

                  }
                }
              }

              .help-kb-list {
                display: block;
                width: 100%;
                margin-top: 25px;
                margin-bottom: 35px;

                .kb-list-clearfix::before {
                  content: "";
                  display: table;
                }

                .kb-list-clearfix::after {
                  visibility: hidden;
                  display: block;
                  height: 0;
                  font-size: 0;
                  content: " ";
                  clear: both;
                }

                .kb-list-item {
                  list-style: none;
                  font-size: 14px;
                  margin: 20px 0;
                  height: 17px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  line-height: 17px;
                  margin-bottom: 12px;
                  padding-right: 30px;

                  a {
                    color: $text-link-color;
                    transition: inherit;

                    .kb-list-circle {
                      display: inline-block;
                      margin-right: 6px;
                      position: relative;
                      top: -3px;
                      background: #373d41;
                      width: 4px;
                      height: 4px;
                    }
                  }

                  a:hover {
                    color: $text-link-color;
                  }
                }

                @media screen and (max-width: 1100px) {
                  .kb-list-item {
                    float: none;
                    width: 100%;
                  }
                }

                @media screen and (min-width: 1100px) {
                  .kb-list-item {
                    float: left;
                    width: 50%;
                  }
                }
              }
            }

            // 底部推荐样式
            .help-footer-recommend {
              background: #fbfbfb;
              margin: 100px 0 24px 0;
              padding: 20px;

              &:empty {
                display: none;
              }

              .help-footer-recommend-title {
                color: #181818;
                font-size: 16px;
                height: 26px;
                line-height: 26px;
                font-weight: 600;
              }

              >ul {
                margin-top: 10px;
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;

                >li {
                  width: 22%;
                  margin-bottom: 8px;
                  margin-right: 3%;
                  position: relative;
                  font-size: 14px;
                  line-height: 22px;

                  >a {
                    color: $text-link-color;
                    word-wrap: break-word;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

// 移动端样式
@media screen and (max-width:500px) {
  .main-layout {
    margin: 0 auto;
    position: relative;

    .help-body-head {
      display: none;
    }

    .stick-top-layout {
      margin: 0 auto;
      position: relative;

      .help-layout {
        box-sizing: border-box;
        position: relative;
        overflow: visible !important;
        display: flex;
        min-height: calc(100vh - 56px);

        .aliyun-docs-toc {
          flex: 0 0 0;
        }

        .aliyun-docs-view {
          overflow-y: auto;
          padding: 0 12px;
          min-width: unset;

          .product-detail {
            .aliyun-docs-content {
              padding: 0;
              padding-top: 40px;
              border-right: unset;

              .aliyun-docs-view-header {

                .header-topbar {
                  height: auto;
                  line-height: 18px;
                  margin-top: 16px;

                  .header-topbar-breadcrumb {
                    font-size: 12px;
                    line-height: 18px;
                    display: flex;
                    color: #d7d8d9;
                    align-items: center;
                    flex-wrap: wrap;

                    span {
                      color: #73777a;
                      padding: 0 2px;
                    }

                    a {
                      color: $text-link-color;
                    }
                  }
                }

                .header-title {
                  margin-top: 22px;

                  h1 {
                    font-size: 36px;
                    flex: 1;
                    word-break: break-all;
                  }

                  .header-actionbar {
                    height: 18px;
                    margin-top: 10px;
                    margin-bottom: 52px;
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    align-items: center;
                  }
                }
              }

              .pc-markdown-container {
                font-size: 12px;

                .markdown-body {
                  -webkit-text-size-adjust: 100%;
                  color: #333;
                  overflow: hidden;
                  font-size: 14px;
                  line-height: 24px;
                  word-wrap: break-word;
                  margin: 24px 0;
                  border-bottom: 1px solid #dcdcdc;

                  h2 {
                    font-size: 26px;
                    line-height: 32px;
                    padding-top: 24px;
                    border-top: 1px solid #e9e9e9;
                  }

                  p {
                    font-size: 14px;
                    line-height: 24px;
                  }

                  video {
                    margin-bottom: 25px;
                  }
                }
              }

              // 底部推荐样式
              .help-footer-recommend {
                margin-top: 126px;
                padding: 12px;

                .content,
                >ul {

                  .item,
                  >li {
                    width: 100%;
                    margin-right: 0;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}