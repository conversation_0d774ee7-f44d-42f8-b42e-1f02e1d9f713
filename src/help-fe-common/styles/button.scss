/**
 * 统一button样式文件
 */
@import './variables.scss';

a {
  &:link {
    color: #1366ec;
  }

  &:visited,
  &:link,
  &:active {
    text-decoration: none;
  }
}

// a链接button样式
.help-button {
  display: inline-block;
  padding: 0 4px;
  margin: 0 4px;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  text-decoration: none;
  background-image: none;
  cursor: pointer;
  border: 1px solid transparent;
  user-select: none;

  &:hover,
  &:visited,
  &:focus {
    text-decoration: none;
  }
}

.unionContainer .markdown-body .help-primary-button,
.help-primary-button {
  @extend .help-button;
  color: $theme-color !important;
  border-color: $theme-color;

  &:hover {
    background-color: $theme-hover;
    color: #fff !important;
  }
}

.unionContainer .markdown-body .help-normal-button,
.help-normal-button {
  @extend .help-button;
  color: $text-color !important;
  background-color: #fff;
  border-color: #d8d8d8;

  &:hover {
    background-color: $btn-second-color;
  }
}

.unionContainer .markdown-body .help-secondary-button,
.help-secondary-button {
  display: inline-block;
  height: 24px;
  line-height: 24px;
  font-size: 12px;
  padding: 0 12px;
  margin: 0 4px;
  color: #fff !important;
  background-color: #1366EC;

  &:hover {
    background-color: $theme-hover;
    color: #fff !important;
  }
}

// jupyter文档运行按钮
.unionContainer .markdown-body .help-lab {
  display: inline-block;
  color: $text-link-color;
  cursor: pointer;

  i {
    margin-right: 4px;
  }

  &:hover {
    text-decoration: underline;
  }
}

//一键部署按钮
.help-deploy-button {
  display: inline-block;
  height: 24px;
  line-height: 24px;
  font-size: 12px;
  padding: 0 12px;
  margin: 0 8px;
  color: #ffffff;
  background-color: #1366EC;
  cursor: pointer;

  &:hover {
    background: #1154C0;
  }
}

.help-lab-button {
  display: inline-block;
  min-width: 160px;
  line-height: 24px;
  padding: 0 10px;
  color: #666;
  font-size: 12px;
  font-weight: 400;
  border: 1px solid #E8E8E8;
  margin: 5px 0;
  cursor: pointer;

  i {
    margin-right: 4px;
    font-size: 12px;
  }

  &:hover {
    color: $text-color;
    background-color: #f4f4f4;
  }
}

.help-try-button {
  @extend .help-button;
  height: 24px;
  line-height: 24px;
  width: 106px;
  margin-left: 10px;
  background-color: #F0F0F0;
  border-color: #F1F1F1;
  font-size: 12px;
  font-weight: 400;
  color: #0064C8;

  i {
    margin-left: 5px;
    font-size: 12px;
  }

  &:hover {
    font-weight: 500;
    background-color: #E8E8E8;
  }
}