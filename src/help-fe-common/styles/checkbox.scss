@import './variables.scss';

// 多选框
.next-checkbox-wrapper {

  .next-checkbox-inner {
    border-radius: 0;
  }

  .next-checkbox-label {
    margin-left: 8px;
    font-size: 14px;
    color: #181818;
    letter-spacing: 1px;
  }

  .next-checkbox-input,
  .next-checkbox-label {
    &:hover {
      color: $theme-color;
    }
  }

  &:hover {
    .next-checkbox-inner {
      border-color: $theme-color !important;
      background-color: #fff !important;
    }
  }
}

.checked,
.checked:hover,
.next-checkbox-wrapper.indeterminate {
  color: $theme-color;

  .next-checkbox-inner {
    background-color: $theme-color !important;

    .next-icon {
      transform: scale(0.6) !important;
    }
  }
}