.unionContainer {
  .markdown-body {
    img {
      // 图片增加边框
      max-width: 100%;
      -moz-box-sizing: border-box;
      box-sizing: border-box;
    }

    .image.break {
      display: block;
      max-width: calc(100% - 4px);
      height: auto;
      margin: 16px 2px 24px 2px;
      box-shadow: 0 0 5px #ccc;
      cursor: zoom-in;
    }

    .image.inline {
      display: inline-block;
      margin: 0 2px;
      vertical-align: middle;
    }

    // flowchart流程图样式
    .image.flowchart {
      padding: 30px;
    }

    @keyframes imgBack {
      from {
        background: rgba(0, 0, 0, 0);
      }

      to {
        background: rgba(0, 0, 0, 0.7);
      }
    }

    .img-back {
      animation-name: imgBack;
      animation-duration: 0.2s;
      position: fixed;
      touch-action: none;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100vw;
      height: 100vh;
      padding: 44px 0;
      background: rgba(0, 0, 0, 0.7);
      z-index: 1000;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .img-float {
      animation-name: imgFloat;
      animation-duration: 0.4s;
      touch-action: none;
      border: 0;
      max-height: none;
      max-width: none;
      margin: 0;
      background-color: #fff;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      z-index: 1001;
      cursor: zoom-in;
      position: absolute;
      transform-origin: center;
    }
  }
}