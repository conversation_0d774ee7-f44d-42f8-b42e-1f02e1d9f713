.unionContainer {
  .markdown-body {

    /////////////////////// note ////////////////////
    // 公共note样式
    div.note {
      padding: 12px 16px;
      margin: 16px 0;
      display: flex;
      align-items: flex-start;

      &-icon-wrapper {
        width: 56px;
        height: 24px;
        display: flex;
        align-items: center;

        .icon-note {
          display: inline-block;
          width: 18px;
          height: 18px;
          padding: 0;
          margin-right: 8px;
          background-repeat: no-repeat;
          background-position: center center;
          vertical-align: middle;
          background-size: 18px 18px;
          border: none;

          &.note,
          &.fastpath,
          &.remember,
          &.tip {
            background-image: url(https://img.alicdn.com/imgextra/i1/O1CN01ctvdSc1UIl2t8RRJ8_!!6000000002495-2-tps-200-200.png); // 说明（问号）
          }

          &.notice,
          &.attention,
          &.important,
          &.restriction {
            background-image: url(https://img.alicdn.com/imgextra/i2/O1CN01xQ7dNJ1HSkgKU99AK_!!6000000000757-2-tps-200-200.png); // 重要（感叹号）
          }

          &.warning,
          &.trouble,
          &.caution {
            background-image: url(https://img.alicdn.com/imgextra/i2/O1CN01jFhq1r1zWjDUpxK4E_!!6000000006722-2-tps-200-200.png); // 警告（三角叹号）
          }

          &.danger {
            background-image: url(//img.alicdn.com/tfs/TB1KjUNn9zqK1RjSZPcXXbTepXa-36-32.png); // 红色叹号
          }
        }
      }

      .note-content,
      .noteContentSpan {
        flex: 1;
        margin-left: 12px;
        color: #585858;
        overflow: hidden;

        p,
        ul {
          margin: 0;
        }

        >strong:first-child {
          display: none;
        }

        // ul列表第一个li标签
        ul>li {
          &:first-child {
            margin-top: 0;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .note-fastpath,
    .note-remember,
    .note-note,
    .note-tip {
      // 说明
      border-left: 2px solid #1366ec !important;
      background-color: #f5f5f6;
    }

    .note-attention,
    .note-restriction,
    .note-important,
    .note-notice {
      // 重要
      border-left: 2px solid #ffa400 !important;
      background-color: #fff9f3;
    }

    .note-warning,
    .note-trouble,
    .note-caution,
    .note-danger {
      // 警告
      border-left: 2px solid #f15634 !important;
      background-color: #fef7f6;
    }

    .note-note .note-content {
      overflow: auto;
    }

    table .note {
      padding: 4px 16px;

      &-icon-wrapper {
        width: 30px;

        .icon-note {
          display: none;
        }
      }
    }
  }
}