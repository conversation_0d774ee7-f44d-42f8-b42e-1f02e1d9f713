// helplab cloudshell相关样式
#cloudshell-service {
  .cloudshell-mask {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.helplab-iframe {
  background: #fff;
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1002;
}

.helplab-overlay {
  background: #fff;
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1100;

  .container {
    margin: calc(50vh - 130px) auto;
    text-align: center;

    .run-tip {
      font-size: 16px;
      font-weight: bold;
      color: #666;
      margin-bottom: 20px;
    }

    .spinner {
      margin: 0 auto;
      width: 50px;
      height: 60px;
      text-align: center;
      font-size: 10px;

      &>div {
        background-color: #1366ec;
        height: 100%;
        width: 6px;
        display: inline-block;

        -webkit-animation: stretchdelay 1.2s infinite ease-in-out;
        animation: stretchdelay 1.2s infinite ease-in-out;
      }

      .rect2 {
        -webkit-animation-delay: -1.1s;
        animation-delay: -1.1s;
      }

      .rect3 {
        -webkit-animation-delay: -1.0s;
        animation-delay: -1.0s;
      }

      .rect4 {
        -webkit-animation-delay: -0.9s;
        animation-delay: -0.9s;
      }

      .rect5 {
        -webkit-animation-delay: -0.8s;
        animation-delay: -0.8s;
      }
    }
  }

  @-webkit-keyframes stretchdelay {

    0%,
    40%,
    100% {
      -webkit-transform: scaleY(0.4)
    }

    20% {
      -webkit-transform: scaleY(1.0)
    }
  }

  @keyframes stretchdelay {

    0%,
    40%,
    100% {
      transform: scaleY(0.4);
      -webkit-transform: scaleY(0.4);
    }

    20% {
      transform: scaleY(1.0);
      -webkit-transform: scaleY(1.0);
    }
  }
}