.unionContainer {
  .markdown-body {

    /////////////////// 表格 ///////////////////
    table {
      width: 100%;
      overflow: auto;
      table-layout: fixed; //修复预付费表格样式问题引发其他图标问题，恢复一下。
      display: table;
      font-size: 14px;
      border-collapse: collapse;
      border-spacing: 0;

      p {
        margin: 0;
      }

      @media screen and (max-width: 1055px) {
        display: block;
      }

      &::-webkit-scrollbar {
        display: none;
      }

      // 新版表格表头背景
      tbody tr td[style*="background-color:#e5e5e5"] {
        background-color: #ededed !important;
      }

      // 正常表头
      thead tr th {
        background-color: #ededed;
      }

      thead th {
        background-color: #ededed;
      }

      thead tr td {
        background-color: #ededed !important;
      }

      tr {
        background-color: #fff;
      }

      td,
      th {
        padding: 10px 13px;
        border: 1px solid #D8D8D8;
        word-break: break-word; // 处理表格文字超出换行
        text-align: left; // 表格中文字居左
        min-width: 100px;
      }

      th {
        font-weight: 700;
        color: #333;
        white-space: inherit;

        &[colspan] {
          text-align: center;
        }
      }

      // 表格折叠
      tr.has-child {
        td:first-child {
          >p:first-of-type {
            display: inline-block;
          }
        }
      }

      // 表格中元素可吸顶展示
      .stick-top {
        position: sticky;
        top: calc(var(--default-head-height) + 6px);
      }
    }

    .table-no-border {

      th,
      td {
        border: none;
      }

      tr td {
        &:first-child {
          padding-left: 0;
        }

        &:last-child {
          padding-right: 0;
        }
      }
    }

    .table-wrapper {
      margin: 0;
      padding: 0;
      position: relative;
    }

    .fixed-table {
      position: fixed;
      margin: 0;
      opacity: 1;
      visibility: hidden;
      z-index: 2;
    }

    // 设置了宽度的表格，限定display
    .table-wide {
      display: table;
    }

    .note table {
      margin-bottom: 0;
    }

    // api文档中“返回数据”表格margin处理
    #resultMapping {
      table {
        table-layout: auto;
        word-break: break-word;

        thead th {
          white-space: nowrap;
        }

        tbody tr>td:first-child,
        tbody tr>td:nth-child(2) {
          white-space: nowrap;
        }
      }

      table .table-scrollbar-pc {
        table {
          margin-top: 0;
          margin-bottom: 0;
        }
      }
    }

    // 表格折叠展开+-按钮
    .icms-tree-table-trigger {
      display: inline-block;
      width: 17px;
      height: 17px;
      margin-right: 3px;
      line-height: 14px;
      text-align: center;
      background: #fff;
      border: 1px solid #e8e8e8;
      cursor: pointer;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;

      &::after {
        content: '+';
      }
    }

    .icms-tree-table-trigger-expanded::after {
      content: '-';
    }

    /////////////////// 分栏 ///////////////////
    .column-layout {
      @media (max-width: 1055px) {

        tr,
        td,
        th {
          display: flex;
          flex-direction: column;
          height: auto !important;
          padding: 0;
        }

        td {
          max-width: calc(100vw - 48px);
        }

        colgroup {
          display: none;
        }
      }
    }

    .medium-width {
      max-width: 1018px;
      width: 100%;
    }
  }
}