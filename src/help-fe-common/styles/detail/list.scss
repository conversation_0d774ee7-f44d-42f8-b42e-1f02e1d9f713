.unionContainer {
  .markdown-body {
    /////////////////// 列表 ///////////////////

    ul li {
      list-style: disc;
      margin: 8px 0;

      &::marker {
        color: #808080;
        font-size: 12px;
      }
    }

    ol li {
      list-style: decimal;
      margin: 8px 0;
    }

    ul,
    ol {
      padding-left: 25px;
    }

    // 列表间距原则：段落、ul ol之间为16px，li li之间为8px
    // 嵌套的ul，ol上下margin为16px，保持和段落间距一致
    ol ol,
    ol ul,
    ul ol,
    ul ul {
      margin-top: 16px;
      margin-bottom: 16px;
    }

    // list-style递归嵌套
    @mixin recursive-li-style($start, $end) {

      // 第一层
      @if $start%3 ==1 {
        ul {
          >li {
            list-style-type: disc;
          }
        }

        ol {
          >li {
            list-style-type: decimal;
          }
        }
      }

      // 第二层
      @else if $start%3 ==2 {
        ul {
          >li {
            list-style-type: circle;
          }
        }

        ol {
          >li {
            list-style-type: lower-alpha;
          }
        }
      }

      // 第三层
      @else if $start%3 ==0 {
        ul {
          >li {
            list-style-type: square;
          }
        }

        ol {
          >li {
            list-style-type: lower-roman;
          }
        }
      }

      ul,
      ol {
        @if $start<$end {
          @include recursive-li-style($start+1, $end);
        }
      }
    }

    // 目前支持9层
    @include recursive-li-style(1, 9);

    pre ol li {
      list-style: none;
      margin: 0;
    }

    table ul {
      margin-bottom: 0;
    }

    .note table ul {
      margin-left: 10px;
      margin-bottom: 0;

      li {
        margin: 0;
      }
    }
  }
}