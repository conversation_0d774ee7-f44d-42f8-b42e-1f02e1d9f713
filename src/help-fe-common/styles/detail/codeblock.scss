.unionContainer {
  .markdown-body {

    .scrollbar {
      scrollbar-width: 6px; // firefox滚动条设置

      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #d8d8d8;
        border-radius: 3px;
      }
    }

    ///////////////////// 代码块 ////////////////////////
    .help-code-block {
      margin: 16px 0;
      padding: 16px;
      background-color: #f4f7fa;
      position: relative;

      .code-tools {
        position: absolute;
        top: 0;
        right: 0;
        background-color: #fff;
        box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.10);
        height: 24px;
        display: flex;
        justify-content: space-between;

        .right-tools {

          .help-code-block-success {
            width: 65px;
            height: 24px;
            box-sizing: border-box;
            margin-bottom: 5px;
            line-height: 24px;
            color: #666;
            font-size: 12px;
            text-align: center;
            background-color: #fff;
            box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.10);
            z-index: 10;
            position: absolute;
            top: -30px;
            right: 2px;

            &::after {
              content: '';
              width: 0;
              height: 0;
              border: 4px solid transparent;
              border-top: 4px solid #fff;
              color: #000;
              overflow: hidden;
              pointer-events: none;
              position: absolute;
              right: 6px;
              bottom: -8px;
            }
          }
        }

        .left-tools,
        .right-tools {

          i {
            display: inline-block;
            height: 24px;
            line-height: 24px;
            width: 24px;
            font-size: 12px;
            color: #666;
            text-align: center;
            cursor: pointer;

            &:hover {
              color: #000;
              background-color: rgba(115, 119, 122, 0.35);
            }
          }
        }

        .deactivateBtn,
        .deactivateBtn:hover {
          color: #e9e9e9;
          cursor: not-allowed;
        }
      }

      //代码区添加调试按钮的样式
      &>.tryIt-btn {
        position: absolute;
        width: 66px;
        height: 30px;
        top: 0;
        right: 0;
        background-color: $theme-color;

        a {
          display: inline-block;
          width: 100%;
          height: 100%;
          color: #fff;
          text-decoration: none;
          font-size: 12px;
          text-align: center;
          line-height: 30px;
          text-indent: 18px;
          background-image: url('https://img.alicdn.com/tfs/TB1_ANYXebviK0jSZFNXXaApXXa-12-16.png');
          background-repeat: no-repeat;
          background-position: 15px 8px;
          background-size: 10px;
          position: absolute;
          top: 0;
          left: 0;

          &:hover {
            background-color: $theme-hover;
          }
        }
      }

      pre {
        margin: 0;
        padding: 16px;
      }
    }

    pre {
      word-wrap: normal;

      @media screen and (max-width: 768px) {
        &::-webkit-scrollbar {
          display: none;
        }
      }

      ol li {
        line-height: 27px;
        white-space: pre;
      }

      code {
        padding: 0;
        margin: 0;
        background: 0 0;
        border: 0;
        display: inline;
        max-width: initial;
        overflow: initial;
        word-break: normal;
        word-wrap: normal;
        white-space: pre;
        overflow-x: auto;
        font-family: Menlo, Monaco, Consolas, Courier, monospace;
        font-size: 14px; // code区域字体为14px
        @extend .scrollbar;

        &::after,
        &::before {
          content: normal;
        }
      }
    }

    code {
      padding: 2px 0;
      margin: 0;
      background-color: rgba(0, 0, 0, 0.04);
      border-radius: 3px;
      font-family: Menlo, Monaco, Consolas, Courier, monospace;

      &::after,
      &::before {
        letter-spacing: -2px;
        content: '\00a0';
      }
    }

    .highlight {
      background: #fff;
      margin-bottom: 16px;
    }

    .highlight pre,
    pre {
      margin: 16px 0;
      padding: 32px;
      overflow: auto;
      font-size: $text-size;
      line-height: 22px;
      background-color: #fff;
      @extend .scrollbar;
    }

    .highlight pre {
      margin-bottom: 0;
      word-break: normal;
    }

    pre.prettyprint ol {
      padding: 0;
      margin: 0;
    }

  }
}