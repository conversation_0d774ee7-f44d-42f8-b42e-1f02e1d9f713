.unionContainer {
  .markdown-body {

    ///////////////////// codeTab、contentTab ////////////////////////
    .tabbed-content-box,
    .tabbed-codeblock-box {
      margin-top: 16px;
      margin-bottom: 24px;
    }

    .tabbed-codeblock-box .tab-box,
    .tabbed-content-box .tab-box {
      height: 43px;
      border-bottom: 1px solid #e9e9e9;
      background-color: #fff;
      display: flex;
      justify-content: space-between;

      .tab-item-container {
        max-width: calc(100% - 70px);
        text-align: center;
        white-space: nowrap;
        overflow-x: scroll;
        overflow-y: hidden;
        scrollbar-width: none; // firefox滚动条隐藏

        &::-webkit-scrollbar {
          display: none;
        }

        .tab-item {
          line-height: 40px;
          text-align: center;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          padding: 0 16px;
          border-bottom: 2px solid transparent;
          white-space: nowrap;
          display: inline-block;
          overflow: hidden;
          text-overflow: ellipsis;

          &:hover {
            color: #0070cc;
          }
        }

        .selected-tab-item {
          border-bottom: 2px solid #1366ec;
          color: #1366ec;
        }
      }

      .tab-box-button {
        height: 100%;
        width: 70px;
        padding: 5px 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-shadow: -7px 0 5px -4px rgba(0, 0, 0, 0.1);

        .left {
          transform: rotate(-180deg);
        }

        .left,
        .right {
          color: #979797;

          &:hover {
            cursor: pointer;
            color: #1366ec;
          }

          i {
            font-size: 12px;
            font-weight: 600;
          }
        }

        .deactivateBtn,
        .deactivateBtn:hover {
          color: #e9e9e9;
          cursor: not-allowed;
        }
      }
    }

    ///////////////////// contentTab ////////////////////////
    .tabbed-content-box {
      border: 1px solid #e9e9e9;

      >.section,
      >.sectiondiv {
        padding: 16px;
        display: none;

        // 针对ssr场景下第一个section展示并预留tab-button区域高度
        &:first-child {
          margin-top: 27px;
          display: block;

          >h1:first-of-type,
          >h2:first-of-type,
          >h3:first-of-type,
          >h4:first-of-type,
          >h5:first-of-type,
          >h6:first-of-type {
            display: none;
          }
        }

        // 子tab第一个元素margin-top、最后一个元素margin-bottom设置为0
        >*:first-child {
          margin-top: 0;
        }

        >*:last-child {
          margin-bottom: 0;
        }

        >.section:first-child,
        >.sectiondiv:first-child {
          >*:first-child {
            margin-top: 0;
          }
        }

        >.section:last-child,
        >.sectiondiv:last-child {
          >*:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    ///////////////////// codeTab ////////////////////////
    .tabbed-codeblock-box {
      position: relative;
      border: 1px solid #e9e9e9;

      input[type='radio'] {
        position: absolute;
        opacity: 0;
        top: 0;
      }

      label {
        position: absolute;
        top: 0;
        width: 120px;
        height: 40px;
        color: #73777a;
        line-height: 40px;
        text-align: center;
        font-size: 16px;
        cursor: pointer;
        display: none;
      }

      div.codeblock-item {
        display: none;

        pre {
          border: 0;
        }

        .help-code-block {
          margin: 0;
        }
      }

      input:checked+label+div {
        display: block;
      }
    }
  }
}