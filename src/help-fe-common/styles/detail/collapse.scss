.unionContainer {
  .markdown-body {
    ///////////////////// 折叠面板 ////////////////////////

    .collapse {
      display: block;
      margin-top: 16px;
      margin-bottom: 24px;
      border: 1px solid #e9e9e9;

      >.expandable-title-bold {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      >.expandable-title,
      .expandable-title-bold {
        font-size: 14px;
        font-weight: 700;
        line-height: 24px;
        padding: 8px 16px;
        border: none;
        margin: 0;

        p {
          margin: 0;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          margin: 0;
          padding: 0;
          border: none;
          line-height: 24px;
        }

        .title {
          font-size: 14px;
          line-height: 24px;

          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          p {
            font-weight: 700;
            font-size: 14px;
            border: none;
            color: inherit;
          }
        }

        .icon {
          transform: rotate(90deg);
          transform-origin: center;
          color: #474a52;
        }

        &:hover {
          cursor: pointer;
          background-color: #f5f5f6;
          color: #1366ec;
        }
      }

      >.expandable-content {
        height: 0;
        display: none;

        >* {
          &:first-child {
            margin-top: 0;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .collapse.expanded {
      >.expandable-title-bold {

        .icon {
          transform: rotate(270deg);
        }

        &:hover {
          background-color: #fff;
          color: #181818;
        }
      }

      >.expandable-content {
        display: block;
        height: auto;
        margin: 0 16px;
        padding: 16px 0;
        border-top: 1px solid #e9e9e9;

        >* {
          &:first-child {
            margin-top: 0;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}