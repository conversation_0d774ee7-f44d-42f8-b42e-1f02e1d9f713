/** 布局样式 */

:root {
  --default-nav-height: 64px;
  --default-head-height: 40px;
}

// 除去目录的主容器
.aliyun-docs-view {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: row;
  background: #fff;

  // 文档中正文部分
  .aliyun-docs-content {
    width: 0;
    max-width: calc(100% - 258px);
    flex: 1;
    position: relative;
  }
}

// pc端
@media only screen and (min-width: 1056px) {

  // 固定顶部、左侧目录树、右侧导读
  .fixed {

    // 头部
    .help-body-head-content {
      position: fixed;
      top: 0;
      left: 0;
      z-index: 100;
    }

    // 目录树
    .aliyun-docs-toc-content {
      position: fixed;
      max-height: calc(100vh - var(--default-head-height));
      top: var(--default-head-height);
      left: 0;
      z-index: 1;
    }

    // 右侧区域（导读、右侧分栏容器）
    .aliyun-docs-side-content {
      position: fixed;
      top: var(--default-head-height);
      right: 0;
    }

    .aliyun-docs-side-blank {
      height: 100%;
    }
  }

  // 左侧目录树容器
  .aliyun-docs-menu {
    max-height: 100vh;
  }

  // 文档中正文部分
  .aliyun-docs-content {
    padding: 20px 24px;
    border-right: 1px solid #e9e9e9;
  }

  // 文档右侧容器
  .aliyun-docs-side {
    box-sizing: border-box;
    flex: 0 0 258px;
    position: relative;
    z-index: 1;
  }

  .aliyun-docs-side-content {
    width: 258px;
    height: calc(100vh - var(--default-head-height) - var(--default-nav-height));
  }

  /** 一键部署相关样式 */
  .aliyun-docs-column-menu {
    width: 0;

    >div {
      width: 0 !important;
    }

    .help-menu-drag-box {
      display: none;
    }
  }

  // 文档正文，适配一键部署等场景
  .aliyun-docs-column-content {
    flex: 1;
    width: 50vw;
  }

  // 文档右侧容器，适用于一键部署等更宽的场景
  .aliyun-docs-column-side {
    flex: 0 0 50vw;
    width: 50vw;
  }
}

//移动端
@media screen and (max-width: 1055px) {

  // 响应式隐藏目录、导读
  .aliyun-docs-menu {
    display: none;
  }

  .aliyun-docs-view .aliyun-docs-content,
  .aliyun-docs-content {
    padding: 16px 16px 0;
    max-width: unset;
  }

  .aliyun-docs-side {
    display: none;
  }
}