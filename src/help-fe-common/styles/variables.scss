// 主题色/ 链接色 / 说明色
$theme-color: #1366ec;
//主题色hover状态
$theme-hover: #1154c0;
//主题色click状态
$theme-active: #1366ec;
// 辅助文案色
$text-help-color: #9b9ea0;
// 警告色
$warn-color: #1366ec;
// 其他提示色
$other-tip-color: #00ba6b;

// 主button色
$btn-main-color: #373d41;
// 次button色
$btn-second-color: #f7f7f7;
// 测边栏底色
$sidebar-bg-color: #f7f7f7;
// 表格标题底色
$table-head-bg-color: #f2f2f2;
// 分割线颜色
$splitline-color: #dfdfdf;
// 边框颜色
$border-color: #d7d8d9;
// 提示底色
$tip-bg-color: #e7e7e7;
// 首页Tab栏背景色
$tab-bg-color: #373d41;
// 首页Tab选中色
$tab-current-color: #2b2f32;

// 主标题字号
$main-title-size: 34px;
// 一级标题字号
$first-title-size: 26px;
// 二级标题字号
$second-title-size: 18px;
// 三级标题字号
$third-title-size: 16px;
// 四级标题字号
$fourth-title-size: 15px;
// 五级标题字号
$fifth-title-size: 14px;
// 六级标题字号
$sixth-title-size: 14px;
// 辅助文案字号
$help-text-size: 12px;

$pc-top-bar-height: 64px;
$pc-top-header-height: 40px;

/* 帮助中心主题色 */
// 标题色 / 正文色
$text-color: #181818;
// 正文字号
$text-size: 14px;
// 二级正文字号
$text-second-size: 14px;
// 正文行距
$text-line-height: 24px;
// 正文链接色
$text-link-color: #1366ec;
// 二级正文色
$text-second-color: #73777a;
// 灰色文字
$text-gray-color: #999;