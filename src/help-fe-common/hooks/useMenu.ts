import { useState, useEffect, useMemo } from 'react';
import store from '@/store';
import useCommonRequest from '@/help-fe-common/hooks/useCommonRequest';
import services, { IDocParam } from '@/help-fe-common/services';
import { isSubProduct, extractTreeList, getPaginationDocs } from '@/help-fe-common/utils/helpDoc/menu';

export default function useMenu() {
  const menuDispatchers = store.useModelDispatchers('menu');
  const [menuData, setMenuData] = useState();
  const [productList, setProductList] = useState<any[]>([]);
  const { request: getMenuTreeList, data, loading, requestStatus } = useCommonRequest(services.getMenuTreeList);
  const { request: getSubProductList, data: subProductData } = useCommonRequest(services.getSubProductList);
  /**
   * productInfo: 目录树主产品信息
   * searchProductInfo: 当前目录树产品信息，用作搜索（主产品为level=3节点，子产品为level=4节点）
   */
  const { productInfo, searchProductInfo, treeList: menuTreeList } = useMemo(() => {
    if (!subProductData?.data) {
      const { productInfo: info, treeList } = extractTreeList(menuData);
      return { productInfo: info, searchProductInfo: info, treeList };
    } else {
      // 存在子产品，从menuData中获取子产品根节点信息
      const { productInfo: info } = extractTreeList(subProductData?.data);
      const { productInfo: searchInfo, treeList } = extractTreeList(menuData);
      let newTreeList = treeList;
      // 子产品如果只存在一个且有子产品目录树，则作为根节点数据处理（产品首页展示子产品目录树）
      if (treeList?.length === 1 && isSubProduct(treeList[0].showType) && treeList[0]?.children?.length) {
        newTreeList = [...treeList[0].children];
      }
      return { productInfo: info, searchProductInfo: searchInfo, treeList: newTreeList };
    }
  }, [menuData, subProductData?.data]);

  /**
   * 获取目录树及子产品相关信息
   * @param nodeId 文档id
   * @param alias 文档别名，包含productAlias产品别名/docAlias文档别名/alias语义化别名
   */
  const request = ({ nodeId, alias }: IDocParam) => {
    getMenuTreeList({ nodeId, alias });
    getSubProductList({ nodeId, alias });
  };

  /**
   * 从子产品接口数据中解析出主产品、子产品列表
   */
  useEffect(() => {
    if (!subProductData?.data) {
      setProductList([]);
      return;
    }
    const { productInfo: mainProduct, treeList: subProductList } = extractTreeList(subProductData?.data);
    mainProduct.main = true;
    const allProductList = [mainProduct, ...subProductList];
    if (allProductList?.length === 2) {
      setProductList([{ selected: true, ...subProductList[1] }]);
    } else {
      setProductList(allProductList);
    }
  }, [subProductData?.data]);

  useEffect(() => {
    setMenuData(data?.data);
  }, [data]);

  useEffect(() => {
    if (menuData) {
      const paginationDocs = getPaginationDocs(menuData);
      menuDispatchers.updatePaginationDocs(paginationDocs);
    }
  }, [menuData]);

  return {
    request,
    requestStatus,
    loading,
    data: menuData,
    productInfo,
    searchProductInfo,
    productList,
    menuTreeList,
  };
}
