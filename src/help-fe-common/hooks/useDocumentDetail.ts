import { useMemo } from 'react';
import useCommonRequest from '@/help-fe-common/hooks/useCommonRequest';
import services from '@/help-fe-common/services';
import { extractDocumentDetail } from '@/help-fe-common/utils/helpDoc/docDetail';
import { extractSynopsisData } from '@/help-fe-common/utils/helpDoc/docOutline';

export default function useDocumentDetail({ docId = null, website = 'cn', language = 'zh' }) {
  const { data: documentData, request, loading, requestStatus } = useCommonRequest(services.getDocumentDetail);
  const data = documentData?.data;

  const synopsis = useMemo(() => extractSynopsisData(data?.content), [data?.content]);

  const { breadcrumb, docInfo } = useMemo(() => extractDocumentDetail(data), [data]);
  // 判断是否需要展示警告NoteTip
  const showNoteTip = useMemo(() => /class="note (warning |note )?note-warning"/.test(data?.content), [data?.content]);
  // autoTranslation 为机器翻译的特殊标志
  const isMachineTranslation = useMemo(() => {
    return data?.tags?.find((item) => item?.key === 'autoTranslation')?.value || false;
  }, [data?.tags]);

  return {
    data,
    request,
    loading,
    requestStatus,
    synopsis,
    docInfo,
    breadcrumb,
    isMachineTranslation,
    showNoteTip,
  };
}
