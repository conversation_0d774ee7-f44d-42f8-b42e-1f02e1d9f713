import { useRef, useEffect } from 'react';

function useEventListener(target: any, eventName: string, listener: EventListener, options?: Record<string, any>) {
  const latestListener = useRef(listener);

  useEffect(() => {
    latestListener.current = listener;
  }, [listener]);

  useEffect(() => {

    const element = getTargetElement(target, window);
    if (!element || !element?.addEventListener) return;

    const eventListener = (event: any) => latestListener.current(event);
    element.addEventListener(eventName, eventListener, options);

    return () => {
      element.removeEventListener(eventName, eventListener, options);
    };
  }, [eventName, target, options]);
}

const isFunction = (value: any) => typeof value === 'function';

const getTargetElement = (target: any, defaultElement: any) => {
  if (!target) {
    return defaultElement;
  }
  let targetElement: any;

  if (isFunction(target)) {
    targetElement = target();
  } else if ('current' in target) {
    targetElement = target.current;
  } else {
    targetElement = target;
  }

  return targetElement;
};


export default useEventListener;
