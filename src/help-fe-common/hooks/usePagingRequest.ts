import { useRef } from 'react';
import { useRequest } from 'ice';

export default function usePagingRequest({ service, defaultParams }, opts = {}) {
  const queryRef = useRef<any>(defaultParams);

  const { data, request, loading } = useRequest(service, opts);

  const fetch = async (p) => {
    queryRef.current = p;
    return await request(p);
  };

  return {
    data,
    request,
    loading,
    refetch: async () => {
      await request(queryRef.current);
    },
    fetch: async (params) => {
      const finalParams = {
        ...queryRef.current,
        ...params,
      };
      fetch(finalParams);
    },
    fetchLatest: async (params) => {
      const finalParams = {
        ...queryRef.current,
        ...params,
        pageNumber: 1,
      };
      fetch(finalParams);
    },
    query: {
      ...queryRef.current,
    },
  };
}
