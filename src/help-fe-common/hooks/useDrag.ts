import { useState } from 'react';
import { throttle } from 'lodash';

export default function useDrag() {
  const [dragOffset, setDragOffset] = useState<number>(300);

  /**
   * 拖拽方法
   * @param className 拖拽对象类名
   */
  const onDragging = (className) => {
    const box = document.querySelector(className);
    if (!box) return;
    const maxWidth = document.documentElement.clientWidth || document.body.clientWidth;
    if (!box) return;
    box.onmousedown = function (ev) {
      ev.preventDefault();
      const e = ev || event;
      const x = e.clientX - box.offsetLeft; // 点击坐标距离左边距离
      const throttleMove = throttle((moveEvent) => {
        box.style.cursor = 'ew-resize';
        const offsetX = moveEvent.clientX - x;
        if (offsetX > 180 && offsetX < (maxWidth / 2 || 500)) {
          setDragOffset(offsetX);
          box.style.left = `${offsetX - 1}px`;
        }
        document.onmouseup = function () {
          document.onmousemove = null;
          document.onmouseup = null;
        };
      }, 16);
      document.onmousemove = function (moveEvent) {
        throttleMove(moveEvent);
      };
    };
  };

  return {
    dragOffset,
    onDragging,
  };
}
