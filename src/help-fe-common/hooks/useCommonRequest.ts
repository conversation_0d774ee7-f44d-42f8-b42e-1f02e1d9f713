import { useState, useEffect } from 'react';
import { useRequest } from 'ice';

export enum REQUEST_STATUS {
  READY = 0,
  LOADING = 1,
  FINISHED = 2,
  NODATA = 3,
}

export default function (...args) {
  const [requestStatus, setRequestStatus] = useState(REQUEST_STATUS.READY);
  // @ts-ignore intercept request
  const { data, loading, request, ...rest } = useRequest(...args);

  useEffect(() => {
    if (loading) {
      setRequestStatus(REQUEST_STATUS.LOADING);
    }
  }, [loading]);
  useEffect(() => {
    if (!loading && data) {
      setRequestStatus(data?.success ? REQUEST_STATUS.FINISHED : REQUEST_STATUS.NODATA);
    }
  }, [data, loading]);

  return {
    request,
    requestStatus,
    data,
    loading,
    ...rest,
  };
}
