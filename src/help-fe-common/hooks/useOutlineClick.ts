import { useEffect, useState } from 'react';
import { throttle } from 'lodash';
import { findCurrentSynopsis } from '@/help-fe-common/utils/helpDoc/docOutline';

// 默认定位高度
const FixedTop = 54;

interface IProps {
  /**
   * 导读数据
   */
  synopsis: any;
  /**
   * 是否启用fix设置，仅针对实战派文档导读设置
   */
  fixEnable?: boolean;
  /**
   * 滚动超出scrollY固定导读区域
   */
  scrollY?: number;
}

export default function useOutlineClick({ synopsis, fixEnable = false, scrollY }: IProps) {
  const [currentSynopsisId, setCurrentSynopsisId] = useState<any>(null);

  /**
   * 导读区域点击事件
   * @param currentId 当前点击的导读id
   */
  const onSynopsisClick = (currentId) => {
    setCurrentSynopsisId(currentId);
  };

  /**
   * 获取当前导读id
   */
  const throttleFindCurrentSynopsis = throttle(() => {
    const currSynId = findCurrentSynopsis(synopsis);
    setCurrentSynopsisId(currSynId);
  }, 100);

  const onScroll = () => {
    throttleFindCurrentSynopsis();
  };

  useEffect(() => {
    if (synopsis) {
      window.addEventListener('scroll', onScroll);
    }
    return () => {
      window.removeEventListener('scroll', onScroll);
    };
  }, [synopsis]);

  return {
    currentSynopsisId,
    onSynopsisClick,
  };
}
