import { useState, useEffect } from 'react';

/**
 * 监听屏幕宽度变化，返回是否为移动端
 * @param threshold 移动端最大宽度阈值（默认1055px）
 */
export function useIsMobile(threshold = 1055): boolean {
  const [isMobile, setIsMobile] = useState(window.innerWidth < threshold);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= threshold);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [threshold]);

  return isMobile;
}
