import { useEffect, useState } from 'react';

type InViewport = boolean | undefined;

interface IProps {
  targetClassName: string;
  rootClassName?: string;
  offsetHeight?: number;
}

function isInViewPort(el: HTMLElement, offsetHeight): InViewport {
  if (!el) {
    return undefined;
  }

  const viewPortWidth =
    window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
  const viewPortHeight =
    window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
  const rect = el.getBoundingClientRect();

  if (rect) {
    const { top, bottom, left, right } = rect;
    return bottom > 0 && top <= viewPortHeight && left <= viewPortWidth && right > 0;
  }

  return false;
}

function getTargetElement(targetClassName) {
  return document.querySelector(targetClassName) as any;
}

export function useInViewport({ targetClassName, rootClassName = '', offsetHeight }: IProps): InViewport {
  const [inViewPort, setInViewport] = useState<InViewport>(() => {
    const el = getTargetElement(targetClassName);
    return isInViewPort(el as HTMLElement, offsetHeight);
  });

  useEffect(() => {
    const el = getTargetElement(targetClassName);
    if (!el) {
      return () => { };
    }
    const observer = new IntersectionObserver((entries) => {
      for (const entry of entries) {
        if (entry.isIntersecting) {
          setInViewport(true);
        } else {
          setInViewport(false);
        }
      }
    }, {
      root: null,
      rootMargin: `-${offsetHeight}px`,
      threshold: 0,
    });

    observer.observe(el as HTMLElement);

    return () => {
      observer.disconnect();
    };
  }, [targetClassName, rootClassName]);

  return inViewPort;
}
