const bindEvent = ({ history }) => {
  const docMarkdownBody = document.querySelector('.markdown-body');
  if (!docMarkdownBody) return;

  const aClick = function (e, aNode) {
    e.preventDefault();
    const href = aNode?.getAttribute('href');
    window.open(href);
  };

  docMarkdownBody?.querySelectorAll('a').forEach((aNode) => {
    aNode?.removeEventListener('click', (e) => { aClick(e, aNode); });
    aNode?.addEventListener('click', (e) => { aClick(e, aNode); });
  });
};

export default [null, bindEvent];
