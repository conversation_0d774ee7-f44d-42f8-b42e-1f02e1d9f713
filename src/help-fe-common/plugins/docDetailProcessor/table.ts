const toggleTable = function (event) {
  event.target.classList.toggle('icms-tree-table-trigger-expanded');
  const parentTr = event.target.parentNode.parentNode;
  const parentLevelClass = getLevelClass(parentTr);
  const overlapTr = parentTr.parentNode.querySelectorAll(`[class*="${parentLevelClass}-"]`);
  if (!event.target.classList.contains('icms-tree-table-trigger-expanded')) {
    overlapTr.forEach((item) => {
      item.querySelectorAll('.icms-tree-table-trigger.icms-tree-table-trigger-expanded').forEach((td) => {
        td.classList.remove('icms-tree-table-trigger-expanded');
      });
      item.classList.add('icms-tree-table-row-hidden');
      item.style.display = 'none';
    });
  } else {
    overlapTr.forEach((item) => {
      const levelClass = getLevelClass(item);
      if (levelClass && levelClass.replace(`${parentLevelClass}-`, '').split('-').length === 1) {
        item.classList.remove('icms-tree-table-row-hidden');
        item.style.display = 'table-row';
      }
    });
  }
};
const getLevelClass = function (element) {
  let levelClass;
  element.classList.forEach((className) => {
    if (~className.indexOf('row-level')) {
      levelClass = className;
    }
  });
  return levelClass;
};

const process = () => {
  document.querySelectorAll('#resultMapping table').forEach((tableNode) => {
    tableNode.outerHTML =
      `
      <div class="table-scrollbar-pc" style="overflow: hidden; overflow-x: auto;">
        ${tableNode.outerHTML}
      </div>
      `;
  });

  Array.from(document.querySelectorAll('[class*="row-level-"]')).forEach((item) => {
    let targetClass;

    if (!Array.from(item.classList).some((className) => className === 'new-version')) {
      item.classList.forEach((className) => {
        if (~className.indexOf('row-level-')) {
          targetClass = className;
        }
      });
      if (targetClass) {
        const level = targetClass.split('-').length - 3;
        // @ts-ignore
        item.children[0].style.paddingLeft = `${10 + level * 20}px`;
        // 默认展开表格
        const firstChild = item.children[0]?.children[0];
        if (firstChild && firstChild?.getAttribute('type') === 'checkbox') {
          firstChild?.classList.add('icms-tree-table-trigger-expanded');
        }
        if (level > 0) {
          // @ts-ignore
          item.style.display = 'table-row';
        }
      }
    }
  });
};

const bindEvent = () => {
  Array.from(document.querySelectorAll('.icms-tree-table-trigger')).forEach((item) => {
    item?.addEventListener('click', toggleTable);
  });
};

export default [process, bindEvent];
