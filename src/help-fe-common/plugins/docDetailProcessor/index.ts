import { isFunction } from 'lodash';
import { mobilePlugins, pcPlugins } from '@/utils/helpDoc/docDetailProcessor/config';

export default (params) => {
  let plugins = [] as any[];
  if (params?.plugins) {
    plugins = params?.plugins;
  } else if (params?.device === 'mobile') {
    plugins = mobilePlugins;
  } else {
    plugins = pcPlugins;
  }
  return plugins.map((item) => {
    let plugin = item as any;
    // 如果返回是回调函数，说明有全局前置判断逻辑
    if (isFunction(plugin)) {
      plugin = plugin(params) || [];
    }
    const [process, bindEvent, unbindEvent] = plugin;
    if (process) {
      process(params);
    }
    if (bindEvent) {
      bindEvent(params);
    }
    if (unbindEvent) {
      return unbindEvent;
    }
    return null;
  }).filter((item) => item !== null);
};
