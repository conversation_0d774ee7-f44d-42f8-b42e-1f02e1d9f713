const noteClassArr = ['.note-fastpath', '.note-remember', '.note-note',
  '.note-tip', '.note-attention', '.note-restriction',
  '.note-important', '.note-notice', '.note-warning',
  '.note-trouble', '.note-caution'];

/**
 * 对note中p标签处理，与icon一行展示
 * 提取note的strong标签插入到icon同级
 */
function insertStrong() {
  const containerDom = document.querySelector('.unionContainer') as HTMLElement;
  const noteDomArr = containerDom?.querySelectorAll(noteClassArr.join(','));
  noteDomArr?.forEach((noteDom) => {
    if (!noteDom) return;
    const strongDom = noteDom?.querySelector('strong') as HTMLElement;
    noteDom?.querySelector('.note-icon-wrapper')?.appendChild(strongDom);
  });
}

const process = () => {
  insertStrong();
};

export default [process];
