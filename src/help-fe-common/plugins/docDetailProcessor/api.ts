import { isCN } from '@/help-fe-common/utils/global/website';

const iframeSrc = (domainName, explorerApi, tryIT, otherParams?) => {
  let url = '';
  if (domainName && explorerApi) {
    url = `https://api.${domainName}.com/#/explorer?${explorerApi}`;
    if (domainName === 'aliyun') {
      const { product, api, version } = otherParams;
      url = version
        ? `https://next.api.aliyun.com/api/${product}/${version}/${api}`
        : `https://next.api.aliyun.com/iframe/${product}?workbench-no-header=true&api=${api}`;
    }
  } else if (tryIT) {
    url =
      tryIT == 'try_it'
        ? 'https://shell.aliyun.com?__source=help.aliyun.com&fullscreen=true'
        : 'https://shell.aliyun.com?editor=true';
  }
  return url;
};

const process = () => {
  // 添加调试按钮
  const explorerButtonDOM = document.querySelector('.api_explorer');

  const aButtonDOM = explorerButtonDOM?.querySelector('.explorer-button.debug-btn>a');
  // 新版api文档调试按钮
  if (explorerButtonDOM?.classList?.contains('new-version-debug-btn') && aButtonDOM) return;

  explorerButtonDOM?.classList.add('apiexplorer-click');

  const buttonString = `<div class='explorer-button'>
    <img src='https://img.alicdn.com/tfs/TB16JcyXHr1gK0jSZR0XXbP8XXa-24-26.png' class='explorer-button-img'/>
  调试</div>`;
  explorerButtonDOM?.insertAdjacentHTML('beforeend', buttonString);
};

const bindEvent = () => {
  // explorer-btn
  const explorerButtonDOM = document.querySelector('.api_explorer');
  const aButtonDOM = explorerButtonDOM?.querySelector('.explorer-button.debug-btn>a');
  // 新版api文档调试按钮
  if (explorerButtonDOM?.classList?.contains('new-version-debug-btn') && aButtonDOM) return;
  explorerButtonDOM?.addEventListener('click', () => {
    let urlLink;
    // 获取iframe参数
    const docLink = explorerButtonDOM?.querySelector('a')?.getAttribute('href') || '';
    const nextUrlList = isCN ?
      ['https://api.aliyun.com/api', 'https://next.api.aliyun.com/api'] :
      ['https://next.api.alibabacloud.com/api', 'https://api.alibabacloud.com/api', 'https://api.aliyun.com/api', 'https://next.api.aliyun.com/api'];
    if (nextUrlList.some((item) => docLink?.startsWith(item))) {
      urlLink = docLink;
    }
    if (!urlLink) {
      const iframeApi = docLink?.split('#')[1];
      const domainName = docLink?.split('#')[0].split('/')[2];
      const queryStringDate = iframeApi?.split('&');
      const product = queryStringDate[0]?.split('=')[1];
      const api = queryStringDate[1]?.split('=')[1];
      const version = queryStringDate?.[3]?.split('=')[1];
      const explorerApi = `product=${product}&api=${api}`;
      const nowUrlData = window.location.href?.split('?')[0];

      // 点击时存储localStorage参数
      if (domainName == 'api.aliyun.com') {
        const iframeData = { domainName: 'aliyun', product, api, nowUrl: nowUrlData };
        const iframeStorage = JSON.stringify(iframeData);
        localStorage.setItem('iframeStorage', iframeStorage);
        urlLink = iframeSrc('aliyun', explorerApi, false, { product, api, version });
      } else {
        const iframeData = { domainName: 'alibabacloud', product, api, nowUrl: window.location.href };
        const iframeStorage = JSON.stringify(iframeData);
        localStorage.setItem('iframeStorage', iframeStorage);
        urlLink = iframeSrc('alibabacloud', explorerApi, false);
      }
    }
    window.open(urlLink);
  });

  // tryIt-btn
  const tryButtonDOM = document.querySelector('.tryIt-btn');
  tryButtonDOM?.addEventListener('click', (event) => {
    event.preventDefault();
    const tryItUrl = tryButtonDOM.parentNode?.querySelector('pre')?.getAttribute('cond-deliverytarget');
    const nowUrlData = window.location.href.split('?')[0];
    const iframeData = { tryIT: tryItUrl, nowUrl: nowUrlData };
    const iframeStorage = JSON.stringify(iframeData);
    // localStorage存储参数
    localStorage.setItem('iframeStorage', iframeStorage);
    iframeSrc(false, false, tryItUrl);
  });
};

export default [process, bindEvent];
