
import { throttle } from 'lodash';
import { addStyle } from '@/help-fe-common/utils/global/style/addStyle';
import { findParent } from '@/help-fe-common/utils/global/dom/findParent';
import { isBigDoc } from '@/help-fe-common/utils/helpDoc/isBigDoc';
import getHeadFixHeight from '@/help-fe-common/utils/global/style/getHeadFixHeight';
import isMobile from '@/help-fe-common/utils/global/isMobile';

// 处理表格包裹
const handelWrapperTable = (tableRef) => {
  const newDiv = document.createElement('div');
  newDiv.classList.add('table-wrapper');
  newDiv.style.overflow = 'auto';
  const parent = tableRef.parentNode;
  parent.insertBefore(newDiv, tableRef);
  newDiv.appendChild(tableRef);
};

// 处理表格宽度超出滚动问题
const handelAutoFit = (table: HTMLTableElement) => {
  const autoFit = table.getAttribute('autofit');
  const tableWidth = table.getAttribute('tablewidth');
  if (autoFit === 'true' || !tableWidth) return;
  table.style.width = `${tableWidth}px`;

  const parentNode = table?.parentNode as HTMLElement;
  if (!parentNode) return;
  if (!parentNode?.classList?.contains?.('table-wrapper')) {
    handelWrapperTable(table);
  } else {
    parentNode.style.overflow = 'auto';
  }
};

/**
 * 添加thead
 * @param tableRef 表格DOM
 */
const addNewThead = (tableRef) => {
  // 处理折叠面板、content tab、table及note里的table
  if (findParent(tableRef, ['.collapse', '.tabbed-content-box', '.table', '.note'])) {
    return;
  }
  // 获取新版表头区域
  let thDOMList: any = [];
  const thNodes = tableRef.querySelectorAll(':scope>thead>tr>th');
  if (thNodes?.length > 0) {
    thDOMList = Array.from(thNodes);
  } else {
    const trDOMList = tableRef.querySelectorAll(':scope>tbody>tr');
    thDOMList = Array.from(trDOMList).filter((tr: any) => {
      let tdDOM = tr.querySelector('td');
      if (!tdDOM) tdDOM = tr.previousElementSibling?.querySelector('td');
      const value = tdDOM?.style?.backgroundColor;
      return value === 'rgb(229, 229, 229)';
    });
  }
  if (!thDOMList?.length) return;

  // 创建新的表格包裹元素
  const newDiv = document.createElement('div');
  newDiv.classList.add('table-wrapper');

  // 创建新的表格
  const newTable = document.createElement('table');
  newTable.classList.add('fixed-table');

  // 创建新的表头元素
  const newThead = document.createElement('thead');
  newThead?.classList?.add('fixed-thead');

  // 复制表头元素
  thDOMList.forEach((item: HTMLElement) => {
    const newTh = item.cloneNode(true) as HTMLElement;

    // 给表头内容包裹一层
    Array.from(newTh.querySelectorAll('td')).forEach((td) => {
      const newSpan = document.createElement('span');
      Array.from(td.childNodes).forEach((child) => {
        newSpan.appendChild(child);
      });
      td.appendChild(newSpan);
    });
    newThead.append(newTh);
  });

  // 复制colgroup元素
  Array.from(tableRef.querySelectorAll(':scope>colgroup')).forEach((colgroup: any) => {
    newTable.append(colgroup.cloneNode(true));
  });

  // 插入新元素
  const parent = tableRef.parentNode;
  parent.insertBefore(newDiv, tableRef);
  newTable.append(newThead);
  newDiv.appendChild(newTable);
  newDiv.appendChild(tableRef);
};

/**
 * 非新版表格及教程文档不进行表头固定
 * @returns boolean
 */
const filterCase = (): boolean => {
  // 教程文档不进行表头处理
  const filterMatchTutorialDoc = !!document.querySelector('.help-tutorial-container .markdown-body');
  return !filterMatchTutorialDoc;
};

/**
 * 固定添加的thead
 */
const fixedTh = () => {
  if (!filterCase()) return;
  const tableRef = document.querySelectorAll('.markdown-body table');
  const fixedHeight = getHeadFixHeight();
  const IsMobile = isMobile();
  Array.from(tableRef).forEach((item) => {
    const fixTableRef = item.previousElementSibling as HTMLElement;
    const isFixedTable = fixTableRef && fixTableRef?.classList?.contains('fixed-table');
    if (!isFixedTable) return;
    const rect = item.getBoundingClientRect();
    // 有caption高度
    const captionHeight = item.querySelector('caption')?.getBoundingClientRect().height ?? 0;
    // 设置表头宽度
    addStyle(fixTableRef, 'width', `${rect.width}px`);
    if (rect.top + captionHeight < fixedHeight && rect.bottom > fixedHeight + fixTableRef.getBoundingClientRect().height && !IsMobile) {
      addStyle(fixTableRef, 'visibility', 'visible');
      addStyle(fixTableRef, 'top', `${fixedHeight}px`);
    } else {
      addStyle(fixTableRef, 'visibility', 'hidden');
    }
  });
};

const process = (params) => {
  if (isBigDoc(params?.data?.nodeId) || !filterCase()) return;
  const tableNodeList = document.querySelectorAll('.markdown-body table');
  Array.from(tableNodeList)?.forEach((item: any) => {
    const autoFit = item?.getAttribute('autofit');
    if (autoFit && autoFit === 'false' && item?.classList?.contains('table-wide')) {
      handelAutoFit(item);
    } else {
      addNewThead(item);
    }
  });
};

const throttleFixedTh = throttle(() => fixedTh(), 16);

const bindEvent = (params) => {
  if (isBigDoc(params?.data?.nodeId) || !filterCase()) return;
  window.addEventListener('scroll', throttleFixedTh);
  window.addEventListener('resize', throttleFixedTh);
};

const unbindEvent = () => {
  window.removeEventListener('scroll', throttleFixedTh);
  window.removeEventListener('resize', throttleFixedTh);
};

export default [process, bindEvent, unbindEvent];
