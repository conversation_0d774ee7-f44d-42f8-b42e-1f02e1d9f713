import ClipboardJS from 'clipboard';

const init = (params) => {
  /**
   * 显示代码复制成功提示
   * @param node iconDOM节点
   */
  const showSuccess = (node) => {
    const parentNode = node?.parentNode;
    if (parentNode?.querySelector('.help-code-block-success')) return;
    const tipDiv = document.createElement('div');
    tipDiv.classList.add('help-code-block-success');
    tipDiv.innerText = '复制成功';
    parentNode && parentNode?.append(tipDiv);
    // 1.5秒后提示自动消失
    setTimeout(() => {
      tipDiv?.remove();
    }, 1500);
  };

  // 一键复制代码
  const clipboard = new ClipboardJS('.copy-btn', {
    target: (trigger) => {
      return trigger?.parentElement?.parentElement?.nextElementSibling;
    },
  } as any);

  clipboard.on('success', (e) => {
    const iconDOM = e.trigger;
    showSuccess(iconDOM);
    e.clearSelection();
  });
};

export default [init];
