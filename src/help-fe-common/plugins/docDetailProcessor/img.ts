import { addImgContainer } from '@/help-fe-common/utils/global/previewImg';
import React from 'react';
import ReactDOM from 'react-dom';
import isMobile from '@/help-fe-common/utils/global/isMobile';
import MobileImgPreview from '@/help-fe-common/components/mobile/ImgPreview';
import PcImgPreview from '@/help-fe-common/components/pc/ImgPreview';

const childClass = 'img.image.break';
const rootClass = '.markdown-body';
const tutorialClass = '.help-tutorial-container .markdown-body';

const process = () => {
  const imgDOMList = document.querySelectorAll(`${rootClass} ${childClass}`);
  const tutorialDOM = document.querySelector(tutorialClass);
  const markdownDOM = document.querySelector(rootClass) as HTMLElement;
  if (!imgDOMList?.length) return;
  // 创建图片预览容器
  const newDiv = document.createElement('div');
  newDiv.id = 'img-preview-container';
  markdownDOM?.insertBefore(newDiv, markdownDOM?.firstChild);

  // 关闭图片预览
  const onClose = () => {
    document.getElementsByTagName('body')?.[0]?.removeAttribute('style');
    ReactDOM.render('', document.getElementById('img-preview-container'));
  };

  imgDOMList?.forEach((imgItem: HTMLImageElement) => {
    if (!imgItem) return;
    imgItem?.setAttribute('loading', 'lazy');
    // 如果是调试按钮icon，不能放大
    if (imgItem?.classList?.contains('explorer-button-img')) return;
    imgItem?.addEventListener('click', () => {
      // 根据设备类型选择展示图片预览组件
      ReactDOM.render(React.createElement(isMobile() ? MobileImgPreview : PcImgPreview, { data: imgItem, onClose }),
        document.getElementById('img-preview-container'));
    });
    // 教程文档中图片添加背景容器，className不能为inline行内图片
    if (tutorialDOM?.contains(imgItem) && !imgItem?.classList?.contains('inline')) {
      addImgContainer(imgItem);
    }
  });
};

export default [process];
