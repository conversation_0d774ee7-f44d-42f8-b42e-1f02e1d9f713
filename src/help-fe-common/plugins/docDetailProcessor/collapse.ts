function generateCollapseStruct(scope) {
  const target = Array.from(document.querySelectorAll('.markdown-body .collapse'))
    .reverse();
  target.forEach((item, index) => {
    if (!item) {
      return;
    }
    const children = Array.from(item?.children);
    if (children?.length < 1) {
      return;
    }
    const classname = item?.getAttribute('class');
    const id = item?.getAttribute('id') || '';
    const [title, ...content] = children;
    let contentNode = '';
    const hasContentClassName = content?.[0]?.classList?.contains('expandable-content');
    if (hasContentClassName) {
      contentNode = content?.[0]?.innerHTML;
    } else {
      contentNode = content.map((contentItem) => contentItem?.outerHTML).join('');
    }
    item.outerHTML = `
    <section class="${classname}" id="${id}">
      <div class="expandable-title-bold">
        <span class="title">${title.outerHTML}</span>
        <i class="icon help-iconfont help-icon-zhankai1 smallFont"></i>
      </div>
      <div class="expandable-content">
        ${contentNode}
      </div>
    </section>
  `;
  });
}

const process = (params) => {
  generateCollapseStruct('.markdown-body');
};

const bindEvent = (params) => {
  document.querySelectorAll('.collapse .expandable-title-bold').forEach((titleNode) => {
    titleNode?.addEventListener('click', () => {
      const parent = titleNode.parentNode as HTMLElement;
      if (!parent) {
        return;
      }
      parent?.classList.toggle('expanded');
    });
  });
};

export default [process, bindEvent];
