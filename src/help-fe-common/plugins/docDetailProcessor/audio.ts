const process = () => {
  const audioDomList = document.querySelectorAll('.markdown-body audio');
  if (!audioDomList?.length) return;
  Array.from(audioDomList)
    .forEach((audioDom) => {
      audioDom.setAttribute('controlslist', 'nodownload noplaybackrate');
    });
};

const pauseAll = (audioList: HTMLAudioElement[], currentIndex: number) => {
  Array.isArray(audioList) && audioList.forEach((audio, index) => {
    if (index !== currentIndex) {
      audio.pause();
      if (audio.currentTime > 0) {
        audio.currentTime = 0;
      }
    }
  });
};

const bindEvent = () => {
  // 获取所有的 audio 标签
  const audioNodeList: NodeListOf<HTMLAudioElement> = document.querySelectorAll('.markdown-body audio');
  if (!audioNodeList?.length) return;
  const formatAudioNodeList: HTMLAudioElement[] = Array.from(audioNodeList);
  formatAudioNodeList.forEach((audio, index) => {
    // 当一个 audio 标签开始播放时
    audio.addEventListener('play', () => {
      pauseAll(formatAudioNodeList, index);
    });
  });
};

export default [process, bindEvent];
