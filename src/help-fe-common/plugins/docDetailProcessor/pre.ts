import { isCN } from '@/help-fe-common/utils/global/website';
import { bindDeployButtonClick } from '@/utils/helpDoc/docDetailProcessor/deploy';

// highlight.js 黑夜模式: atom-one-dark 白天模式: atom-one-light
const themeDark = 'code-theme-dark';

const isDark = () => Boolean(JSON.parse(localStorage.getItem(themeDark) || 'false'));

const tips = {
  cn: {
    themeTip: '更改代码主题',
    copyTip: '复制代码',
    quickTip: '快速部署',
  },
  intl: {
    themeTip: 'Change code theme',
    copyTip: 'Copy Code',
    quickTip: '',
  },
};
const buttonTip = isCN ? tips.cn : tips.intl;

/**
 * 插件运行逻辑
 * @param params
 */
const process = (params) => {
  const { themeTip, copyTip, quickTip } = buttonTip;

  // 给代码区加上滚动条
  document.querySelectorAll('.markdown-body pre').forEach((preNode: HTMLElement) => {
    if (!preNode) return;
    const preHeight = preNode.getAttribute('height');
    preHeight && (preNode.style.height = `${preHeight}px`);

    const deployId = preNode.getAttribute('deployId');
    let buttonNode = '';

    if (deployId && quickTip) {
      buttonNode = `<i class="quick-start-btn help-iconfont help-icon-deploy"
      deployId="${deployId}"
      title="${quickTip}"></i>
      <i class="theme-switch-btn help-iconfont help-icon-baitian" title="${themeTip}"></i>
      <i class="copy-btn help-iconfont help-icon-fuzhi" title="${copyTip}"></i>`;
    } else {
      buttonNode = `<i class="theme-switch-btn help-iconfont help-icon-baitian" title="${themeTip}"></i>
      <i class="copy-btn help-iconfont help-icon-fuzhi" title="${copyTip}"></i>`;
    }

    preNode.outerHTML = `
    <div class="help-code-block">
      <div class="code-tools">
        <div class="left-tools">
        </div>
        <div class="right-tools">
          ${buttonNode}
        </div>
      </div>
      ${preNode.outerHTML}
    </div>
    `;
  });

  // 代码主题切换
  if (isDark()) {
    document.querySelectorAll('pre, .help-code-block>.code-tools').forEach((toolNode) => {
      toolNode?.classList.add(themeDark);
    });
  }
};

const bindEvent = (params) => {
  document.querySelectorAll('.theme-switch-btn').forEach((btn: HTMLElement) => {
    btn?.addEventListener('click', () => {
      const buttonDOM = btn;
      buttonDOM.classList.toggle('help-icon-baitian');
      buttonDOM.classList.toggle('help-icon-heiye');
      localStorage.setItem(themeDark, JSON.stringify(!isDark()));
      document.querySelectorAll('pre, .help-code-block>.code-tools').forEach((toolNode) => {
        toolNode?.classList.toggle(themeDark);
      });
    });
  });

  // 代码快速部署
  document.querySelectorAll('.markdown-body .quick-start-btn').forEach((buttonItem: HTMLElement) => {
    if (!buttonItem) return;
    bindDeployButtonClick(buttonItem, params?.data);
  });
};

export default [process, bindEvent];
