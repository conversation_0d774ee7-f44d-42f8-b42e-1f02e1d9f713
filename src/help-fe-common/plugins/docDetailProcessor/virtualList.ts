import { isIOS } from '@/help-fe-common/utils/global/isMobile';
import { debounce } from 'lodash';
import { isBigDoc } from '@/help-fe-common/utils/helpDoc/isBigDoc';

function move(oldParent, newParent) {
  while (oldParent.childNodes.length > 0) {
    newParent.appendChild(oldParent.childNodes[0]);
  }
}
function isInViewPort(el) {
  // viewPortHeight兼容所有浏览器写法
  const viewPortHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
  const { offsetTop } = el;
  const { scrollTop } = document.documentElement;
  const height = el.offsetHeight; // 高度
  return offsetTop - scrollTop <= viewPortHeight + height && scrollTop < offsetTop + height;
}

const skeleton = '<div class="skeleton-screen"></div>';
const dataTag = 'data-children-wrapper';
let cacheList = [] as any; // 缓存节点列表
const childrenWrapperList = [] as any; // 缓存容器节点列表

const showNode = (domNode) => {
  const showFlag = isInViewPort(domNode);

  if (showFlag) {
    const index = domNode.getAttribute(dataTag);
    domNode.innerHTML = '';
    domNode.appendChild(childrenWrapperList[Number(index)]);
  } else {
    domNode.innerHTML = skeleton;
  }
};

const process = () => {
  // 这里要注意，避免出现section 嵌套的情况
  const list = document.querySelectorAll('.icms-help-docs-content section.section');
  // 切片标识，将classList仅有.section的节点进行切片，避免section嵌套问题
  cacheList = Array.from(list)?.filter((item) => {
    return item?.classList?.length === 1;
  });
  cacheList.forEach((domNode, index) => {
    const { height } = domNode.getBoundingClientRect();
    domNode.setAttribute('style', `height: ${height}px;`); // 将高度记录下来
    domNode.setAttribute(dataTag, index);
    const divNode = document.createElement('div');
    move(domNode, divNode);
    if (index < 5) {
      // 保证首屏，不发生滚动的情况下，内容不要出现空白
      domNode.appendChild(divNode);
    } else {
      domNode.innerHTML = skeleton;
    }
    childrenWrapperList.push(divNode);
  });
};

const onScroll = () => {
  cacheList.forEach((wrapperNode) => showNode(wrapperNode));
};
const debounceOnScroll = debounce(onScroll, 50);

const bindEvent = () => {
  window.addEventListener('scroll', debounceOnScroll);
};
const unbindEvent = () => {
  window.removeEventListener('scroll', debounceOnScroll);
};

// export default [process, bindEvent, unbindEvent];
export default (params) => {
  if (isBigDoc(params?.data?.nodeId) && isIOS()) {
    return [process, bindEvent, unbindEvent];
  }
  return [];
};
