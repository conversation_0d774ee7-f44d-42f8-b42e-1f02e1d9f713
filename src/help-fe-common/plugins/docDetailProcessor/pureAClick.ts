import UrlParse from 'url-parse';
import { triggerUrlBehavior } from '@/help-fe-common/utils/global/url/urlBehavior';
import { getHostWhiteFlag } from '@/help-fe-common/utils/global/track/spm';
import { isCN } from '@/help-fe-common/utils/global/website';
import { clickToHash } from '@/help-fe-common/utils/helpDoc/docOutline';

// 白名单产品文档，链接点击触发事件
const whiteProductList = ['/model-studio/'];

const targetOrigins = [
  'https://bailian.aliyun.com',
  'https://bailian.console.aliyun.com',
  'https://pre-bailian.console.aliyun.com',
  'https://pre-bailian.aliyun.com'];

/**
 * 处理 a 标签点击事件
 */
const handleAClick = (e: MouseEvent) => {
  e.preventDefault();
  e.stopPropagation();
  const aNode = e.target as HTMLAnchorElement;

  const href = aNode?.getAttribute('href');
  if (!href) return;

  // ctrl+左键新窗口打开
  const isCtrlClick = e.button === 0 && (e.ctrlKey || e.metaKey);
  const urlParse = new UrlParse(href);

  // 检查是否为白名单产品
  const isWhiteProduct = whiteProductList?.some((item) => href.includes(item));

  // 外部网站拦截提醒
  if (!getHostWhiteFlag(urlParse.hostname)) {
    window.open(`/redirect?targetUrl=${encodeURIComponent(href)}`);
    return;
  }

  // 新窗口打开逻辑
  if (isCtrlClick) {
    triggerUrlBehavior(urlParse.href, history, null, null, 'newOpen');
    return;
  }

  const { hostname, pathname, hash } = urlParse;
  const websitePathName = isCN ? '/' : '/help';

  // 判断是否与当前页面链接相同
  if (
    (window.location.pathname === pathname || pathname === websitePathName) &&
    window.location.hostname === hostname
  ) {
    // 当前文档页的链接，处理锚点跳转
    const anchor = hash.split('#')[1];
    clickToHash(anchor);
  } else if (isWhiteProduct) {
    targetOrigins.forEach((origin) => {
      // 白名单产品，发送消息
      window.parent.postMessage(
        { eventName: 'urlClick', url: href },
        origin, // 指定父页面源
      );
    });
  } else {
    // 其他情况，新开窗口
    window.open(urlParse.href);
  }
};
const handleMouseEnter = (e: MouseEvent) => {
  const aNode = e.target as HTMLAnchorElement;
  const href = aNode.getAttribute('href');

  // 非阿里云的外部链接，不添加 spm 参数
  if (!getHostWhiteFlag(href)) {
    aNode.setAttribute('data-spm-protocol', 'i');
  }

  // 动态设置 title 属性
  if (!aNode.hasAttribute('title')) {
    aNode.setAttribute('title', '');
  }
};

/**
 * 纯净文档模式，a链接点击行为处理
 * @param param0
 * @returns
 */
const bindEvent = ({ history }) => {
  const docMarkdownBody = document.querySelector('.unionContainer .markdown-body');
  if (!docMarkdownBody) return;

  const links = docMarkdownBody.querySelectorAll('a');

  links.forEach((aNode) => {
    // 移除旧的事件监听器（避免重复绑定）
    aNode.removeEventListener('click', handleAClick);
    aNode.removeEventListener('mouseenter', handleMouseEnter);

    // 绑定新的事件监听器
    aNode.addEventListener('click', handleAClick);
    aNode.addEventListener('mouseenter', handleMouseEnter);
  });
};

export default [null, bindEvent];
