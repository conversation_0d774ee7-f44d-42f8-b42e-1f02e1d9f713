const showButtonArr = [] as any;
const tabContainerArr = [] as any;

/**
 * tab高亮及内容展示
 * @param {string} domId 需要高亮的domId
 * @param {boolean} isShow 展示or隐藏
 */
function showTab(domId, isShow) {
  const divList = document.querySelectorAll('div.tab-item');
  const inputList = document.querySelectorAll('input');
  // 筛选id存在的dom元素
  const divDOM = Array.from(divList)?.find((divNode) => divNode.getAttribute('id') === domId) as HTMLDivElement;
  const inputDOM = Array.from(inputList)?.find((inputNode) => inputNode.getAttribute('id') === domId) as HTMLInputElement;
  if (isShow) {
    divDOM?.setAttribute('style', 'color: #1366ec; border-bottom: 2px solid #1366ec');
    inputDOM.checked = true;
  } else {
    divDOM?.setAttribute('style', 'color: #181818; border-bottom: 2px solid transparent');
    inputDOM.checked = false;
  }
}

/**
 * 生成tab展示头组件
 */
function generateTab() {
  const tabContentBoxArr = document.querySelectorAll('.icms-help-docs-content .tabbed-codeblock-box');
  tabContentBoxArr.forEach((tabBoxDOM, index) => {
    const tabArr = [] as any;
    tabBoxDOM.querySelectorAll('label').forEach((itemDOM, index1) => {
      tabArr[index1] = { title: itemDOM.innerText, id: itemDOM.getAttribute('for') };
    });
    tabContainerArr[index] = tabArr;
  });
  document.querySelectorAll('.icms-help-docs-content .tabbed-codeblock-box .tab-box').forEach((tabDOM, index) => {
    const tabChildDOM = tabContainerArr[index]?.map((item) =>
      `<div class='tab-item' id=${item?.id}>${item?.title}</div>`);
    const newDiv = `<div class='tab-item-container'>${tabChildDOM?.join('')}</div>`;
    tabDOM.insertAdjacentHTML('beforeend', newDiv);
    // 添加右侧按钮区域
    const buttonDOM =
      `<div class='tab-box-button'>
      <div class='left'><i class='help-iconfont help-icon-zhankai1'></i></div>
      <div class='right'><i class='help-iconfont help-icon-zhankai1'></i></div>
      </div>`;
    tabDOM.insertAdjacentHTML('beforeend', buttonDOM);
    // 默认第一个tab高亮
    const firstId = tabContainerArr[index][0]?.id;
    showTab(firstId, true);
    // 绑定click事件
    tabDOM.querySelectorAll('.tab-item').forEach((itemNode, index1) => {
      itemNode?.addEventListener('click', () => {
        const id = tabContainerArr[index][index1]?.id;
        showTab(id, true);
        tabContainerArr[index]?.filter((item, idx) => idx !== index1)?.forEach((item) => {
          showTab(item?.id, false);
        });
      });
    });
  });
}

/**
 * 是否显示右侧按钮区域
 */
function onShowButton() {
  const boxWidth = document.querySelector('.icms-help-docs-content .tabbed-codeblock-box')?.scrollWidth || 0;

  tabContainerArr.forEach((tabContainer, index) => {
    const tabWidth = document.querySelectorAll('.icms-help-docs-content .tabbed-codeblock-box .tab-item-container')[index]?.scrollWidth;

    if (tabWidth < boxWidth - 70) {
      showButtonArr[index] = false;
    } else {
      showButtonArr[index] = true;
    }
  });
  document.querySelectorAll('.tabbed-codeblock-box .tab-box-button').forEach((buttonDOM: HTMLElement, index) => {
    if (showButtonArr[index]) {
      buttonDOM.style.visibility = 'visible';
    } else {
      buttonDOM.style.visibility = 'hidden';
    }
  });
  onButtonClick();
}

/**
 * 点击按钮滚动
 */
const onButtonClick = () => {
  const tabContainerDomArr = document.querySelectorAll('.icms-help-docs-content .tabbed-codeblock-box .tab-box .tab-item-container');

  document.querySelectorAll('.tabbed-codeblock-box .tab-box-button .left').forEach((leftNode, index) => {
    const tabContainerDOM = tabContainerDomArr[index];
    if (tabContainerDOM.scrollLeft === 0) {
      leftNode.classList.add('deactivateBtn');
    }
    leftNode?.addEventListener('click', () => {
      tabContainerDOM?.scrollBy({ left: -480, top: 0, behavior: 'smooth' });
    });
  });

  document.querySelectorAll('.tabbed-codeblock-box .tab-box-button .right').forEach((rightNode, index) => {
    const tabContainerDOM = tabContainerDomArr[index];

    rightNode?.addEventListener('click', () => {
      tabContainerDOM?.scrollBy({ left: 480, top: 0, behavior: 'smooth' });
    });
  });
};

/**
 * tab-item区域滚动事件
 */
const handleTabScroll = () => {
  const tabArr = document.querySelectorAll('.icms-help-docs-content .tabbed-codeblock-box');
  tabArr.forEach((tabDOM, index) => {
    const itemContainerDOM = tabDOM.querySelector('.tab-item-container') as HTMLElement;
    itemContainerDOM?.addEventListener('scroll', () => {
      const rightBtn = tabDOM.querySelector(' .tab-box-button .right');
      const leftBtn = tabDOM.querySelector(' .tab-box-button .left');
      // 右侧滑到头
      if (itemContainerDOM.scrollWidth - itemContainerDOM.scrollLeft <= itemContainerDOM.getBoundingClientRect().width) {
        rightBtn?.classList.add('deactivateBtn');
      } else {
        rightBtn?.classList.remove('deactivateBtn');
      }
      // 左侧滑到头
      if (itemContainerDOM.scrollLeft === 0) {
        leftBtn?.classList.add('deactivateBtn');
      } else {
        leftBtn?.classList.remove('deactivateBtn');
      }
    });
  });
};

const resizeObserver = new ResizeObserver(() => {
  onShowButton();
});

const process = () => {
  if (document.querySelectorAll('.icms-help-docs-content .tabbed-codeblock-box')?.length) {
    generateTab();
    onShowButton();
    handleTabScroll();
  }
};

const bindEvent = () => {
  // 监听窗口变化
  resizeObserver.observe(document.body);
};

const unbindEvent = () => {
  // 取消监听窗口变化
  resizeObserver.unobserve(document.body);
};
export default [process, bindEvent, unbindEvent];
