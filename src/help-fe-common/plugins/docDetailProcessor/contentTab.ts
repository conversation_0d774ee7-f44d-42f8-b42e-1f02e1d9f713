import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';

interface TabData {
  title: string;
  id: string | null;
  contentId: string | null;
}
// tab-box容器domList
let tabBoxDomList = [] as any;
// 存放每个tab-box的tab标题和id
const tabDataArr = [] as any;

let hashBoxIndex = -1;
let hashItemIndex = -1;


/** -------------处理tab数据并生成tab节点------------ */
/**
 * 提取每个tab标题及id
 * @param tabBoxDOM tab容器DOM对象
 * @param tabBoxDOMIndex tab容器索引
 * @param hashId 用于定位tab的hash值
 */
const getTabData = (tabBoxDOM, tabBoxDOMIndex, hashId) => {
  if (!tabBoxDOM) return;
  const tabArr: TabData[] = [];
  try {
    tabBoxDOM.querySelectorAll(':scope>section, :scope>div').forEach((childNode, index) => {
      if (!childNode) return;
      const hNode = childNode?.querySelector(':scope>h1, :scope>h2, :scope>h3, :scope>h4, :scope>h5, :scope>h6');
      if (!hNode) return;
      const hNodeId = hNode?.getAttribute('id');
      tabArr[index] = { title: hNode.innerText, id: hNodeId, contentId: childNode?.getAttribute('id') };
      if (hashId === `#${hNodeId}`) {
        // 默认只会有一个hash定位到tab
        hashItemIndex = index;
        hashBoxIndex = tabBoxDOMIndex;
      }
      hNode.remove();
    });
    tabDataArr[tabBoxDOMIndex] = tabArr;
  } catch (error) {
    //
  }
};

/**
 * 生成tab切换组件
 */
const structTabNode = (tabBoxDOM, tabBoxDOMIndex) => {
  const currentTabArr: TabData[] = tabDataArr[tabBoxDOMIndex];
  const { id: defaultItemId, contentId: defaultContentId } = currentTabArr[0];
  const tabChildDOM = currentTabArr?.map((item) =>
    `<div class='tab-item' id=${item?.id}>${item?.title}</div>`);
  const newDiv = `<div class='tab-item-container'>${tabChildDOM?.join('')}</div>`;
  // 添加右侧按钮区域
  const buttonDOM =
    `<div class='tab-box-button'>
  <div class='left'><i class='help-iconfont help-icon-zhankai1'></i></div>
  <div class='right'><i class='help-iconfont help-icon-zhankai1'></i></div>
  </div>`;
  const newBox = `<div class='tab-box'>${newDiv}${buttonDOM}</div>`;
  tabBoxDOM.insertAdjacentHTML('afterbegin', newBox);

  // 根据hash地址高亮tab
  if (tabBoxDOMIndex === hashBoxIndex) {
    const { id: currentItemId, contentId: currentContentId } = currentTabArr[hashItemIndex];
    showTab(tabBoxDOM, currentItemId, currentContentId, true);
  } else {
    showTab(tabBoxDOM, defaultItemId, defaultContentId, true);
  }

  // 绑定click事件（仅对第一个子节点下tab-item绑定）
  tabBoxDOM.querySelectorAll(':scope>.tab-box .tab-item').forEach((itemNode, index1) => {
    itemNode?.addEventListener('click', () => {
      const { id: tabItemId, contentId: contentItemId } = currentTabArr[index1];
      // 展示当前tab，隐藏其他tab
      showTab(tabBoxDOM, tabItemId, contentItemId, true);
      const tabClearStyleArr = currentTabArr?.filter((item, idx) => idx !== index1);
      for (let i = 0; i < tabClearStyleArr.length; i++) {
        const { id, contentId } = tabClearStyleArr[i];
        showTab(tabBoxDOM, id, contentId, false);
      }

      // 点击事件只改变网址不跳转
      const newUrl = `${window.location.href.split('#')[0]}#${tabItemId}`;
      history.pushState('', '', newUrl);

      sendSlsLog({ page: 'documentDetail', section: 'tab', action: 'tabClick', userParams1: itemNode.innerText });
    });
  });
};

/**
 * tab高亮及内容展示
 * @param {HTMLElement} tabBoxDOM tab容器DOM对象
 * @param {string} tabId 标题id
 * @param {string} contentId 内容id
 * @param {boolean} isShow 展示or隐藏
 */
const showTab = (tabBoxDOM, tabId, contentId, isShow) => {
  const tabDOM = tabBoxDOM.querySelector(`div[id="${tabId}"]`);

  const contentDom = tabBoxDOM.querySelector(`section[id="${contentId}"], div[id="${contentId}"]`) as HTMLElement;
  if (!contentDom) return;
  if (isShow) {
    tabDOM?.classList.add('selected-tab-item');
    contentDom.style.display = 'block';
  } else {
    tabDOM?.classList.remove('selected-tab-item');
    contentDom.style.display = 'none';
  }
};

/** 处理tab区域相关逻辑
 * @param tabBoxDOM tab容器DOM对象
 * @param tabBoxDOMIndex tab容器索引
 * @param hashId 用于定位tab的hash值
 */
const handleTab = (tabBoxDOM, tabBoxDOMIndex, hashId) => {
  getTabData(tabBoxDOM, tabBoxDOMIndex, hashId);
  structTabNode(tabBoxDOM, tabBoxDOMIndex);
};

/** ------------------处理tab右侧按钮显示、滚动等相关逻辑----------------- */
/**
 * 判断是否显示右侧按钮区域
 */
const onShowButton = (boxWidth, tabWidth, buttonDOM) => {
  if (tabWidth === 0 || boxWidth === 0 || tabWidth < boxWidth - 70) {
    buttonDOM.style.visibility = 'hidden';
  } else {
    buttonDOM.style.visibility = 'visible';
  }
};

/**
 * 点击按钮滚动
 */
const onButtonClick = (tabItemContainer, leftBtn, rightBtn) => {
  if (tabItemContainer.scrollLeft === 0) {
    leftBtn.classList.add('deactivateBtn');
  }
  leftBtn?.addEventListener('click', () => {
    tabItemContainer?.scrollBy({ left: -480, top: 0, behavior: 'smooth' });
  });
  rightBtn?.addEventListener('click', () => {
    tabItemContainer?.scrollBy({ left: 480, top: 0, behavior: 'smooth' });
  });
};

/**
 * tab-item区域滚动事件
 */
const onTabScroll = (tabItemContainer, leftBtn, rightBtn) => {
  const containerWidth = tabItemContainer?.getBoundingClientRect()?.width;
  tabItemContainer?.addEventListener('scroll', () => {
    const { scrollWidth, scrollLeft } = tabItemContainer;
    // 右侧滑到头
    if (scrollWidth - scrollLeft <= containerWidth) {
      rightBtn?.classList.add('deactivateBtn');
    } else {
      rightBtn?.classList.remove('deactivateBtn');
    }
    // 左侧滑到头
    if (scrollLeft === 0) {
      leftBtn?.classList.add('deactivateBtn');
    } else {
      leftBtn?.classList.remove('deactivateBtn');
    }
  });
};

const handleTabButton = (tabBoxDOM) => {
  const tabItemContainer = tabBoxDOM.querySelector('.tab-item-container') as HTMLElement;
  const tabBtn = tabBoxDOM.querySelector('.tab-box-button') as HTMLElement;
  const rightBtn = tabBtn.querySelector('.right');
  const leftBtn = tabBtn.querySelector('.left');
  onShowButton(tabBoxDOM?.scrollWidth, tabItemContainer?.scrollWidth, tabBtn);
  onButtonClick(tabItemContainer, leftBtn, rightBtn);
  onTabScroll(tabItemContainer, leftBtn, rightBtn);
};

const resizeObserver = new ResizeObserver(() => {
  if (!tabBoxDomList?.[0]) return;
  const tabBoxDOM = tabBoxDomList?.[0];
  const tabItemContainer = tabBoxDOM.querySelector('.tab-item-container') as HTMLElement;
  const tabBtn = tabBoxDOM.querySelector('.tab-box-button') as HTMLElement;
  onShowButton(tabBoxDOM?.scrollWidth, tabItemContainer?.scrollWidth, tabBtn);
});

const process = () => {
  // 获取hashId
  const hashId = window?.location?.hash;
  tabBoxDomList = document.querySelectorAll('.icms-help-docs-content .tabbed-content-box');
  tabBoxDomList.forEach((tabBoxDOM, index) => {
    handleTab(tabBoxDOM, index, hashId);
    handleTabButton(tabBoxDOM);
  });
};

const bindEvent = () => {
  // 监听窗口变化
  resizeObserver.observe(document.body);
};

const unbindEvent = () => {
  // 取消监听窗口变化
  resizeObserver.unobserve(document.body);
};

export default [process, bindEvent, unbindEvent];
