
import React from 'react';
import ReactDOM from 'react-dom';
import VideoPoster from '@/help-fe-common/components/common/ProductDetail/VideoPoster';
import { isChildNode } from '@/help-fe-common/utils/global/dom/isChildNode';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';

const process = () => {
  const videoDOMList = document.querySelectorAll('.unionContainer .markdown-body video');
  // 视频取消自动播放
  if (!videoDOMList?.[0]) return;
  videoDOMList.forEach((videoNode) => {
    videoNode.setAttribute('controlslist', 'nodownload');
    videoNode.removeAttribute('autoplay');
  });
};

/**
 * video添加统一封面及展现更多视频
 * @param videoDOM video dom对象
 * @param nodeId 文档id
 * @param videoUrl video链接
 * @param videoTitle video标题
 */
const addVideoPoster = (videoDOM, nodeId, videoUrl, videoTitle) => {
  const videoContainer = document.createElement('div');
  videoDOM.after(videoContainer);
  ReactDOM.render(React.createElement(VideoPoster, { nodeId, videoUrl, videoTitle }),
    videoContainer);
  videoDOM.remove();
};
/**
 * video标签的poster属性
 * @param videoDOM video dom对象
 */
const removeVideoPoster = (videoDOM) => {
  videoDOM.removeAttribute('poster');
};

const bindEvent = (params) => {
  const videoDOMList = document.querySelectorAll('.unionContainer .markdown-body video');
  videoDOMList.forEach((videoDOM: HTMLElement) => {
    if (isChildNode('table', videoDOM)) {
      removeVideoPoster(videoDOM);
      return;
    }

    const videoUrl = videoDOM?.getAttribute('src');
    const videoTitle = videoDOM?.getAttribute('title') ?? params?.data?.title;
    // 视频封面替换
    addVideoPoster(videoDOM, params?.data?.nodeId, videoUrl, videoTitle);

    // TODO: videoDOM监听事件
    let videoIsPlayed = false;

    videoDOM?.addEventListener('play', () => {
      if (!videoIsPlayed) {
        sendSlsLog({ page: 'documentDetail', section: 'video', action: 'play', userParams1: params?.data?.nodeId, userParams2: videoUrl });
      }
    });
    videoDOM?.addEventListener('pause', () => {
      videoIsPlayed = true;
    });
    videoDOM?.addEventListener('ended', () => {
      sendSlsLog({ page: 'documentDetail', section: 'video', action: 'fullPlay', userParams1: params?.data?.nodeId, userParams2: videoUrl });
    });
  });
};

export default [process, bindEvent];
