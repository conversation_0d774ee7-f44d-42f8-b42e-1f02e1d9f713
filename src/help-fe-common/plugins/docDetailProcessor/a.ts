import UrlParse from 'url-parse';
import { triggerUrlBehavior } from '@/help-fe-common/utils/global/url/urlBehavior';
import { getHostWhiteFlag } from '@/help-fe-common/utils/global/track/spm';
import { isCN } from '@/help-fe-common/utils/global/website';
import { clickToHash } from '@/help-fe-common/utils/helpDoc/docOutline';

// 教程文档、纯净模式文档中链接新窗口打开
const newOpenClassList = ['.help-tutorial-container .markdown-body'];

const bindEvent = ({ history }) => {
  const newOpenNodes = document.querySelectorAll(newOpenClassList?.join(','));
  // 对文档内的链接处理逻辑
  // 1. 当前文档，可能 hash不同，需要锚点，通过 history.pushState 无法达到锚点效果， 直接放行
  // 2. 其他帮助文档，history.pushState
  // 3. 外部链接，新窗口打开
  // 4. 教程文档、纯净pure模式中链接，新窗口打开
  const docMarkdownBody = document.querySelector('.markdown-body');
  if (!docMarkdownBody) return;

  const aClick = function (e, aNode) {
    e.preventDefault();
    const href = aNode?.getAttribute('href');
    // ctrl+左键新窗口打开
    const isCtrlClick = e?.button === 0 && (e?.ctrlKey || e?.metaKey);
    // 链接新窗口打开
    const isNewOpenDoc = Array.from(newOpenNodes)?.some((parentNode) => {
      return parentNode?.contains(aNode);
    });
    const urlParse = new UrlParse(href);
    // 外部网站进行拦截提醒
    if (!getHostWhiteFlag(urlParse?.hostname)) {
      window.open(`/redirect?targetUrl=${encodeURIComponent(href)}`);
    } else if (isCtrlClick || isNewOpenDoc) {
      triggerUrlBehavior(urlParse?.href, history, null, null, 'newOpen');
    } else {
      const { hostname, pathname, hash } = urlParse;
      // 判断是否与当前页面链接相同，a链接示例：/document_detail/xxx.html /#hash.html
      if (window?.location?.pathname === pathname && window?.location?.hostname === hostname) {
        // 是帮助中心当前文档页的链接，需要锚点；
        clickToHash(hash.split('#')[1]);
      } else {
        triggerUrlBehavior(urlParse.href, history, { from: 'detail' });
      }
    }
  };

  docMarkdownBody?.querySelectorAll('a').forEach((aNode) => {
    aNode?.removeEventListener('click', (e) => {
      aClick(e, aNode);
    });
    aNode?.addEventListener('click', (e) => {
      aClick(e, aNode);
    });
    aNode?.addEventListener('mouseenter', (e) => {
      aNode.setAttribute('title', '');
      // 非阿里云的外部链接，不添加spm参数
      if (!getHostWhiteFlag(aNode.getAttribute('href'))) {
        aNode.setAttribute('data-spm-protocol', 'i');
      }
    });
  });
};

export default [null, bindEvent];
