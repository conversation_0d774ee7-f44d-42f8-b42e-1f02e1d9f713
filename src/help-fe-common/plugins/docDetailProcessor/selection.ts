import React from 'react';
import ReactDOM from 'react-dom';
import Selection from '@/help-fe-common/components/common/Selection';
import LocaleProvider from '@/help-fe-common/components/common/LocalProvider';
import { hideSelection } from '@/help-fe-common/utils/helpDoc/selection';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import { getLocale } from '@/help-fe-common/utils/global/locale';
import { extractUrlParam } from '@/help-fe-common/utils/global/url/urlExtract';
import { selectionTooltipId } from '@/help-fe-common/constants';

/**
 * 获取nodeId、productCode、标题、url、面包屑等
 * @param data
 */
const getSelectionInfo = (docContainer) => {
  const title = docContainer?.querySelector('h1')?.innerText || '';
  const breadCrumb = Array.from(docContainer?.querySelector('.help-header-breadcrumb').childNodes)?.map((item: any) => item.innerText).join('>');
  const { nodeId, productCode } = window?.globalData;
  const url = location.href;
  return { title, url, nodeId, productCode, breadCrumb };
};

/**
 * 获取选区的上下文内容
 */
const getRelatedContent = (selection) => {
  // 获取选区集体的父元素
  let selectionEle: any = selection?.getRangeAt(0)?.commonAncestorContainer;
  if (!selectionEle) {
    return;
  }
  if (selectionEle.nodeType !== Node.ELEMENT_NODE) {
    selectionEle = selectionEle.parentNode;
  }

  // 标题区域
  if (selectionEle?.tagName === 'H1') {
    return selectionEle?.textContent;
  }

  // 一句话描述区域
  if (selectionEle?.classList?.contains('shortdesc')) {
    return selectionEle?.textContent;
  }

  // 表格区域
  const table = selectionEle?.closest('table');
  if (table) {
    return table?.innerText;
  }

  // 寻找最近的父级元素中含有 h2 的那一个
  const findParentSection = (element) => {
    const parent = element?.parentNode;
    if (!parent || parent.tagName === 'BODY') {
      return {};
    }

    if (parent.querySelector('h2') !== null) {
      return {
        parent,
        element,
      };
    }

    return findParentSection(parent);
  };
  const { parent: section, element: lastFindDiv } = findParentSection(selectionEle);

  // 块文本内容区域
  if (section && section.tagName === 'section') {
    return section?.innerText;
  }

  // 特殊类型，在内容区，但分段类型不同，取最近两个 h2 之间的内容
  if (section?.closest('.markdown-body')) {
    const getPrevNextH2Content = (element) => {
      const res = [element.textContent];
      const getPrev = (ele) => {
        if (ele) {
          res.unshift(ele.textContent);
          if (ele.tagName !== 'H2') {
            getPrev(ele.previousElementSibling);
          }
        }
      };
      getPrev(element.previousElementSibling);

      const getNext = (ele) => {
        if (ele && ele.tagName !== 'H2') {
          res.push(ele.textContent);
          getNext(ele.nextElementSibling);
        }
      };
      getNext(element.previousElementSibling);

      return res.join('\n');
    };

    return getPrevNextH2Content(lastFindDiv);
  }

  return selectionEle?.textContent;
};

const sliceRelatedContent = (relatedContent: string | undefined, selectionContent: string) => {
  if (selectionContent?.length > 500) {
    return selectionContent;
  }

  if (!relatedContent?.length || relatedContent.length < 500) {
    return relatedContent;
  }

  const matchStart = relatedContent.indexOf(selectionContent.replace(/\n\n/g, ''));
  const matchEnd = matchStart + selectionContent.length;

  if (matchStart === -1) {
    // 兜底逻辑，未从关联内容中找到光标选区文字
    return relatedContent.slice(0, 500);
  }

  let start = matchStart - Math.floor((500 - selectionContent.length) / 2);
  let end = matchEnd + Math.ceil((500 - selectionContent.length) / 2);
  if (end > relatedContent.length) {
    start = relatedContent.length - 500;
    end = relatedContent.length;
  }
  if (start < 0) {
    start = 0;
    end = 500;
  }

  return relatedContent.slice(start, end);
};

/**
 * 处理划词选区属性
 * @param pageX 距离屏幕左上角的x轴距离
 * @param pageY 距离屏幕左上角的y轴距离
 * @param selection 选中的文本
 */
const setSelectionAttribute = (pageX, pageY, selection) => {
  const docContainer = document.getElementById('aliyun-docs-view');
  const { y: containerY } = docContainer?.getBoundingClientRect() as any;

  const selectionFeedback = document.getElementById(selectionTooltipId);
  if (selectionFeedback) {
    selectionFeedback.style.display = 'flex';
  }

  const dom = document.getElementById('aliyun-docs-selection-div');
  const selectionComponent = React.createElement(Selection, {
    identifier: extractUrlParam(location?.pathname),
    selectionInfo: {
      relatedContent: sliceRelatedContent(getRelatedContent(selection), selection?.toString()),
      ...getSelectionInfo(docContainer),
    },
    showAIassistant: true,
    prompt: selection?.toString(),
    selectionDomStyle: {
      left: pageX,
      top: pageY - containerY,
    },
  });
  const providerComponent = React.createElement(LocaleProvider, {
    locale: getLocale(),
  } as any, selectionComponent);

  ReactDOM.render(providerComponent, dom);
};

const resizeObserver = new ResizeObserver(() => {
  hideSelection();
});


const whiteDomList = [''];
const bindEvent = () => {
  const box = document.querySelector('.aliyun-docs-view') as HTMLElement;
  const selection = window?.getSelection();

  box?.addEventListener('mousedown', (e) => {
    if (e?.button === 0) {
      // 左键点击，隐藏反馈按钮并清理所有选区
      hideSelection();
      selection?.removeAllRanges();
    }
  });
  box?.addEventListener('mouseup', () => {
    // 鼠标点击时如果内容为空，不展示划词反馈按钮
    if (selection?.toString()?.trim() === '') return;
    const {
      startContainer, // 起始节点
      startOffset, // 起始节点偏移量
    } = selection?.getRangeAt(0) as any;

    // 创建范围只包含开始位置的文本节点
    const startRange = document.createRange();
    startRange.setStart(startContainer, startOffset);
    startRange.setEnd(startContainer, startOffset);

    // 获取划词开始的位置信息
    const { x: pageX, y: pageY } = startRange.getBoundingClientRect();
    setSelectionAttribute(pageX, pageY, selection);

    sendSlsLog({ page: 'documentDetail', section: 'selection', action: 'underline', userParams1: selection?.toString() });
  });

  // 监听窗口变化
  resizeObserver.observe(document.body);
};


const unbindEvent = () => {
  hideSelection();
  // 取消监听窗口变化
  resizeObserver.unobserve(document.body);
};


export default [null, bindEvent, unbindEvent];
