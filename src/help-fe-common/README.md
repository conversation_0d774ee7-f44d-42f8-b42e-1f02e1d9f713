## 阿里云帮助中心前端公共模块
由于历史原因，阿里云帮助中心中国站、国际站是两套独立代码。
两者重复的通用代码，我们将会持续迁移到 help-portal-fe-common 仓库里，减少重复建设。

## 代码命令
```
//和远程仓库关联
git remote add -f help-fe-common **************************:aliyun-help/help-portal-fe-common.git
// subtree 初始化
git subtree add --prefix=src/help-fe-common **************************:aliyun-help/help-portal-fe-common.git master
// 从远程仓库拉取代码
git subtree pull -P src/help-fe-common help-fe-common master --squash
// 提交至远程仓库
git subtree push -P src/help-fe-common help-fe-common master
```

