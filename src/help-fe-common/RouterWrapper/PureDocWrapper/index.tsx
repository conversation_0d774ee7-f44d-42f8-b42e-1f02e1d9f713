import React from 'react';
import { getSearchParams } from 'ice';
import ProductDetail from '@/pages/ProductDetail';
import { removeAsyncNodeList } from '@/help-fe-common/utils/global/style/handleStyle';
import { isCN } from '@/help-fe-common/utils/global/website';

const PureDocWrapper = (WrappedComponent: React.ComponentType) => {
  const searchParams = getSearchParams();

  const Wrapped = (props) => {
    const isPureMode = searchParams?.mode === 'pure';
    if (isPureMode) {
      // 隐藏页面节点
      const asyncClassList = isCN ?
        ['.aliyun-docs-pagination', 'div[data-role-topbar="help"]', '.ace-homepage-2020-hmod-footer'] :
        ['.aliyun-docs-pagination', '.hmod-alicloud-homon-nav', '.hmod-ace-alicloud-footer'];
      removeAsyncNodeList(asyncClassList);
      return <ProductDetail docDetailData={null} />;
    } else {
      return <WrappedComponent {...props} />;
    }
  };

  return Wrapped;
};

export default PureDocWrapper;
