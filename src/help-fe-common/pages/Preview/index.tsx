import React, { useMemo } from 'react';
import { getSearchParams } from 'ice';
import ProductDetail from '@/pages/ProductDetail';
import { DocDetailTypeEnum } from '@/help-fe-common/constants/detail';
import SolutionDetail from '@/solution/pages/SolutionDetail';
import MainLayout from '@/help-fe-common/layouts/MainLayout';
import PcLayout from '@/help-fe-common/layouts/PcLayout';

export default function Preview() {
  const searchParams = getSearchParams();
  const pageType = useMemo(() => {
    let docType = DocDetailTypeEnum.NORMAL;
    // query参数解析
    if (searchParams?.mode === 'solution-detail') {
      docType = DocDetailTypeEnum.SOLUTION_DETAIL;
    }
    return docType;
  }, [searchParams?.mode]);

  if (pageType === DocDetailTypeEnum.SOLUTION_DETAIL) {
    return (
      <SolutionDetail solutionDetailData={null} />
    );
  }
  return (
    <MainLayout>
      <PcLayout>
        <ProductDetail docDetailData={null} />
      </PcLayout>
    </MainLayout>
  );
}
