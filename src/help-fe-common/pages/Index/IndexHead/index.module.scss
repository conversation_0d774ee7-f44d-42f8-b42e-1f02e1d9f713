.headerContainer {
  width: 100%;
  height: 44px;
  background: #fff;

  .HeaderTop {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 16px;
    height: 100%;
    box-shadow: inset 0 -1px 0 0 rgba(61, 61, 61, 0.15);

    >i {
      font-size: 16px;
    }

    .title {
      flex: 1;
      font-size: 16px;
      font-weight: normal;
      line-height: 24px;
      text-align: center;
    }
  }

  .productDialog {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 200;
    background-color: #fff;
  }
}

.bodyHide {
  position: fixed !important;
  width: 100%;
  height: 100%;
}

// pc端
@media only screen and (min-width: 1056px) {
  .headerContainer {
    display: none;
  }
}