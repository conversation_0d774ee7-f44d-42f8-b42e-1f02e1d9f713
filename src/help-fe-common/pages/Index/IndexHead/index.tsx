import React, { useEffect, useState } from 'react';
import classnames from 'classnames';
import { FormattedMessage } from 'react-intl';
import ProductTree from '@/help-fe-common/pages/Index/ProductTree';
import styles from './index.module.scss';


interface IProps {
  data: any;
}
enum WrapperType {
  ALL_PRODUCT_LIST,
  DEFAULT
}

export default ({ data }: IProps) => {
  const [showWrapperType, setShowWrapperType] = useState<WrapperType>(WrapperType.DEFAULT);

  const openWrapper = (type: WrapperType) => {
    const showType = showWrapperType === type ? WrapperType.DEFAULT : type;
    setShowWrapperType(showType);
  };

  const renderWrapper = () => {
    switch (showWrapperType) {
      case WrapperType.ALL_PRODUCT_LIST:
        return (
          <div className={styles.productDialog}>
            <ProductTree data={data} />
          </div>
        );
      case WrapperType.DEFAULT:
        return null;
    }
  };

  useEffect(() => {
    if (showWrapperType !== WrapperType.DEFAULT) {
      document.body.classList.add(styles.bodyHide);
      document.body.style.top = '0px';
    } else {
      document.body.classList.remove(styles.bodyHide);
    }
    return () => {
      document.body.classList.remove(styles.bodyHide);
    };
  }, [showWrapperType]);

  return (
    <div className={styles.headerContainer}>
      <div className={styles.HeaderTop}>
        <i
          onClick={() => {
            openWrapper(WrapperType.ALL_PRODUCT_LIST);
          }}
          className={
            classnames(
              'help-iconfont',
              showWrapperType === WrapperType.ALL_PRODUCT_LIST
                ? 'help-icon-mulushu-zhankaiicon'
                : 'help-icon-mulushu-shouqiicon',
            )}
          style={{ color: showWrapperType === WrapperType.ALL_PRODUCT_LIST ? '#1366ec' : '' }}
        />
        <div className={styles.title}><FormattedMessage id="help.index.productList" /></div>
      </div>
      {renderWrapper()}
    </div>
  );
};
