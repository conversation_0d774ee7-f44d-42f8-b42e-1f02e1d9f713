.index {
  min-height: 100vh;

  .helpContent {
    display: flex;

    .left {
      width: 284px;
      box-shadow: inset -1px 0px 0px 0px #EAEAEA;
    }

    .right {
      flex: 1;
      min-height: calc(100vh - 94px);
      padding: 44px 46px;
      background-image: url(https://img.alicdn.com/imgextra/i4/O1CN01BfIbke1uu1SliPd9F_!!6000000006096-0-tps-4624-2568.jpg);
      background-repeat: no-repeat;
      background-size: contain;
      overflow: hidden;
    }
  }

  .loading {
    width: 100%;
    height: 100vh;

    :global {
      .next-loading-dot {
        background: #1366ec;
      }
    }
  }
}


// mobile端
@media only screen and (max-width: 1055px) {
  .index {
    .helpContent {
      flex-direction: column;

      .left {
        height: 44px;
        width: 100%;

        .pcProductTree {
          display: none;
        }
      }

      .right {
        padding: 20px 16px;
        width: 100%;
      }
    }
  }
}