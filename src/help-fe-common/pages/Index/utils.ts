import { NODE_TYPE, SHOW_TYPE } from '@/help-fe-common/constants/detail';

interface ITreeItem {
  id: string;
  title: string;
  nodeType?: NODE_TYPE;
  showType?: SHOW_TYPE;
  pipCode?: string;
  url?: string;
  children: ITreeItem[];
}


// 定义输出Map的值类型
interface MapValue extends Omit<ITreeItem, 'pipCode' | 'children'> {
  parentCategory?: string;
  [key: string]: any;
}
/**
   * 计算一级节点所有产品节点数量
   * @param treeData
   * @returns
   */
const computeTreeLength = (treeData: ITreeItem[]) => {
  let length = 0;
  if (!treeData?.length) return length;
  treeData?.forEach((item) => {
    if (item?.nodeType === NODE_TYPE.PRODUCT_NODE) {
      length += 1;
    }
    if (item?.children?.length) {
      length += computeTreeLength(item?.children);
    }
  });
  return length;
};


/**
 * 将树形结构转换为以pipCode为key的Map结构
 * @param tree 树形结构数据
 * @returns 以pipCode为key的Map结构
 */
function convertTreeToMap(tree: ITreeItem[]): Record<string, MapValue> {
  // 结果Map
  const resultMap: Record<string, MapValue> = {};

  /**
   * 递归遍历树结构
   * @param nodes 当前层级的节点数组
   * @param parentTitle 父节点的title
   */
  function traverse(nodes: ITreeItem[] | undefined, parentTitle?: string): void {
    if (!nodes || nodes.length === 0) return;

    for (const node of nodes) {
      if (node.pipCode) {
        // 如果节点有pipCode，添加到结果Map中
        const { pipCode, children, ...restProps } = node;
        resultMap[pipCode] = {
          ...restProps,
          pipCode,
          productLine: parentTitle,
        };
      }

      traverse(node.children, node.pipCode ? parentTitle : node.title);
    }
  }

  // 开始递归遍历
  traverse(tree, '');

  return resultMap;
}


export { ITreeItem, computeTreeLength, convertTreeToMap };
