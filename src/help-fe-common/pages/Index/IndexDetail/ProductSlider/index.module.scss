// 变量定义
$link-color: #1366ec;
$background-color: #f9f9f9;
$text-color-primary: #181818;
$secondary-text-color: #666;
$disabled-color: #ccc;
$card-height: 100px;
$card-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
$transition-duration: 0.5s;

@mixin text-line($line-number: 1) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line-number;
}

.productSlider {
  width: 100%;
  height: 124px;
  background-color: #fff;
  padding: 20px;
  position: relative;

  .sliderContainer {
    width: 100%;
    height: 84px;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;

    a.productCard {
      flex: 0 0 calc(25% - 9px);
      height: 36px;
      line-height: 36px;
      padding: 0 12px;
      font-size: 14px;
      margin-right: 12px;
      background-color: #F7F9FC;
      color: $text-color-primary;
      text-decoration: none;
      transition: background-color 0.3s;
      overflow: hidden;
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      &:nth-child(4n) {
        margin-right: 0;
      }

      &:nth-child(-n+4) {
        margin-bottom: 12px;
      }

      &:hover {
        color: $link-color;
      }

      .title {
        height: 36px;
        line-height: 36px;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .tag {
        height: 20px;
        line-height: 20px;
        margin-left: 8px;
        font-size: 12px;
        border-radius: 4px;
        padding: 0px 5px;
        background: #E6EEFC;
        color: #1366EC;
      }
    }
  }

  // 滑动控制器
  .sliderControls {
    display: flex;
    align-items: center;
    position: absolute;
    left: 0;
    bottom: -44px;

    .sliderButton {
      background: none;
      border: none;
      font-size: 18px;
      cursor: pointer;
      color: $text-color-primary;
      padding: 8px 12px;
      transition: all 0.2s ease;

      &:disabled {
        color: $disabled-color;
        cursor: not-allowed;
      }

      &:hover:not(:disabled) {
        color: $link-color;
      }
    }

    .sliderInfo {
      margin: 0 16px;
      color: $text-color-primary;
      font-size: 12px;
      line-height: 22px;
    }
  }
}


@media only screen and (max-width: 1055px) {
  .productSlider {
    height: auto;

    .sliderContainer {
      height: auto;
      flex-direction: column;
      flex-wrap: nowrap;

      a.productCard {
        flex: 1;
        margin-right: 0;
        margin-bottom: 12px;
      }
    }
  }

}

// 滑动动画
@keyframes slideLeft {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(-100%);
  }
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
  }

  to {
    transform: translateX(0);
  }
}

@keyframes slideRight {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(100%);
  }
}

@keyframes slideInFromLeft {
  from {
    transform: translateX(-100%);
  }

  to {
    transform: translateX(0);
  }
}

// 应用动画类
.slidingNext {
  animation: slideLeft $transition-duration forwards;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 100%;
    width: 100%;
    height: 100%;
    animation: slideInFromRight $transition-duration forwards;
  }
}

.slidingPrev {
  animation: slideRight $transition-duration forwards;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    right: 100%;
    width: 100%;
    height: 100%;
    animation: slideInFromLeft $transition-duration forwards;
  }
}