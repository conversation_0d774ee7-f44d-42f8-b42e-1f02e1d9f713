import React, { useState } from 'react';
import { Slider } from '@alifd/next';
import styles from './index.module.scss';

const ProductSlider = ({ data, pageSize = 8 }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const totalPages = Math.ceil(data.length / pageSize);
  // 获取特定页的数据
  const getPageData = (page) => {
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return data.slice(startIndex, endIndex);
  };

  // 处理页面变化
  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages && newPage !== currentPage) {
      setCurrentPage(newPage);
    }
  };

  // 上一页
  const prevPage = () => {
    const pageNum = currentPage - 1 < 1 ? totalPages : currentPage - 1;
    handlePageChange(pageNum);
  };

  // 下一页
  const nextPage = () => {
    const pageNum = currentPage + 1 > totalPages ? 1 : currentPage + 1;
    handlePageChange(pageNum);
  };

  const renderSliderContainer = () => {
    const elements = Array.from({ length: totalPages }, (_, index) => index);
    return (
      elements?.map((element, index) => {
        return (
          <div
            tabIndex={index}
            className={styles.sliderContainer}
          >
            {getPageData(index + 1).map((item, itemIndex) => (
              <a
                key={itemIndex}
                href={item.url}
                className={styles.productCard}
                target="_blank"
                rel="noreferrer"
              >
                <span className={styles.title}>{item.title}</span>
                <span className={styles.tag}>{item?.productLine}</span>
              </a>
            ))}

          </div>);
      })
    );
  };

  return (
    <div className={styles.productSlider}>
      <Slider
        style={{ height: 84, overflow: 'hidden' }}
        triggerType="click"
        dots={false}
        arrows={false}
        lazyLoad
        onChange={(current) => {
          setCurrentPage(current + 1);
        }}
        activeIndex={currentPage - 1}
      >
        {renderSliderContainer()}
      </Slider>

      <div className={styles.sliderControls}>
        <button
          className={styles.sliderButton}
          onClick={prevPage}
          aria-label="上一页"
        >
          <i className="help-iconfont help-icon-Left-Arrow" />
        </button>
        <span className={styles.sliderInfo}>
          {currentPage}/{totalPages}
        </span>
        <button
          className={styles.sliderButton}
          onClick={nextPage}
          aria-label="下一页"
        >
          <i className="help-iconfont help-icon-Right-Arrow" />
        </button>
      </div>
    </div>
  );
};

export default ProductSlider;
