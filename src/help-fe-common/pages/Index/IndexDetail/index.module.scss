// 变量定义
$text-color-primary: #181818;
$text-color-secondary: #8C8C8C;
$link-color: #1366ec;
$background-color: #FFFFFF;
$card-background: #FFFFFF;
$card-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.05);
$margin-base: 8px;

// 混入
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin card-hover {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);

    .img-card-title {
      >p {
        color: $link-color;
      }
    }
  }
}

@mixin text-line($line-number: 1) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line-number;
}

:global {

  .help-index-detail.unionContainer {
    .markdown-body {
      margin: 0;
      overflow: unset;

      // 通用元素
      img {
        margin: 0;
        max-width: 100%;
        height: auto;
        vertical-align: middle;

        &.break {
          display: block;
        }
      }

      p {
        margin: 0;
      }

      h1,
      h2 {
        margin: 0;
      }

      .header {
        margin-bottom: 60px;
        max-width: 732px;

        .header-title {
          display: block;

          h1 {
            margin: 0 0 28px 0;
            font-size: 44px;
            font-weight: 600;
            height: 60px;
            line-height: 60px;
          }
        }

        .header-desc {
          display: block;
          font-size: 16px;
          line-height: 28px;
          letter-spacing: 0.4px;
        }

        >* {
          display: none;
        }
      }

      // 楼层样式
      .floor {
        margin-bottom: $margin-base * 6;

        &[data-cond-props='hide'] {
          display: none;
        }

        .anchor-point {
          display: none;
        }

        .floor-title {
          h2 {
            border: none;
            padding: 0;
            height: 36px;
            line-height: 36px;
            font-size: 26px;
            font-weight: 600;
            color: $text-color-primary;
            margin-bottom: $margin-base * 1;
          }
        }

        .floor-desc {
          font-size: 14px;
          line-height: 24px;
          color: $text-color-primary;
          margin-bottom: $margin-base * 3;
        }

        .floor-content {
          margin-bottom: $margin-base * 6;
        }
      }

      // 我的文档列表
      .doc-link-list {
        margin-bottom: 88px;
      }

      // 网格布局
      .grid-layout {
        display: grid;
        gap: $margin-base * 3;
        margin-bottom: $margin-base * 3;

        &[data-cols="2"] {
          grid-template-columns: repeat(2, 1fr);
        }

        &[data-cols="3"] {
          grid-template-columns: repeat(3, 1fr);
        }
      }

      .with-link-card {
        @include card-hover;
        cursor: pointer;
      }

      // 卡片基础样式
      .text-card {
        height: 120px;
        padding: 20px;
        background: $card-background;
        box-shadow: $card-shadow;
        border: 1px solid #E9E9E9;
        display: flex;
        flex-wrap: wrap;

        &>*:nth-child(1) {
          flex: 0 0 24px;
          width: 24px;
          height: 24px;
        }

        &>*:nth-child(2) {
          flex: 1 1 50%;
        }

        &>*:nth-child(n+3) {
          flex: 1 1 100%;
        }

        .img-card-logo {
          margin-right: $margin-base*1;

          img {
            width: 24px;
            height: 24px;
            display: block;
            max-width: none;
            margin: 0;
            box-shadow: none;
            cursor: auto;
          }
        }

        .img-card-title {

          >p {
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
            color: $text-color-primary;
            margin-bottom: $margin-base;
            @include text-line(1);
          }
        }

        .img-card-desc {
          font-size: 14px;
          line-height: 1.6;
          color: $text-color-secondary;

          p {
            margin: 0;
            @include text-line(2);
          }
        }

        .img-card-link {
          display: none;

          a {
            color: $link-color;
            text-decoration: none;
            font-size: 14px;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }

      // 图文卡片样式
      .graphic-card {
        @extend .text-card;
        height: 192px;
        background-image: url(https://img.alicdn.com/imgextra/i4/O1CN01bx71dQ1awVIDPHDgP_!!6000000003394-0-tps-2076-768.jpg);
        background-size: cover;
        overflow: hidden;
        position: relative;

        .img-card-desc {
          flex: 1 1 50%;
          padding-right: 40px;

          >p {
            @include text-line(5);
          }
        }

        .img-card-image {
          flex: 1 1 50%;
          overflow: hidden;

          img {
            width: 256px;
            height: auto;
            min-width: calc(50% + 16px);
            display: block;
            max-width: none;
            margin: 0;
            box-shadow: none;
            cursor: auto;
            padding: 19px 16px;
            border-radius: 12px 0px 0px 0px;
            background: rgba(255, 255, 255, 0.2);
            position: absolute;
          }
        }
      }

      // mobile端
      @media only screen and (max-width: 1055px) {
        .header {
          width: 100%;
          max-width: 100%;
          margin-bottom: $margin-base*4;

          .header-title {

            h1 {
              margin-bottom: $margin-base*2;
              font-size: 26px;
              height: 42px;
              line-height: 42px;
            }
          }
        }

        .floor {
          margin-bottom: $margin-base * 4;

          .floor-title {
            h2 {
              font-size: 20px;
            }
          }

          .grid-layout {

            &[data-cols="2"],
            &[data-cols="3"] {
              grid-template-columns: 1fr;
            }
          }
        }
      }
    }
  }
}