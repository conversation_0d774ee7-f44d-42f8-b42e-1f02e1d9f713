import React, { useState, useEffect, useRef } from 'react';
import ReactDOM from 'react-dom';
import classNames from 'classnames';
import { useHistory, useRequest } from 'ice';
import { isEmpty } from 'lodash';
import ProductSlider from './ProductSlider';
import { triggerUrlBehavior } from '@/help-fe-common/utils/global/url/urlBehavior';
import { convertTreeToMap } from '@/help-fe-common/pages/Index/utils';
import styles from './index.module.scss';
import services from '@/help-fe-common/services/apiService';
import { isServer } from '@/help-fe-common/utils/node/getContext';

// 控制是否显示隐藏的元素，设置为false则将使用CSS中的data-cond-props='hide'属性来隐藏元素
const SHOW_HIDDEN_ELEMENTS = false;

interface IProps {
  content: string;
  productsData: any[];
}

const IndexDetail = ({ content, productsData }: IProps) => {
  const history = useHistory();
  const contentRef = useRef<HTMLDivElement>(null);
  const firstFlag = useRef(true);
  const [shownProducts, setShownProducts] = useState([]);
  const { request: getUserProducts } = useRequest(services.getUserProducts);

  useEffect(() => {
    if (!contentRef.current || !firstFlag.current) return;
    firstFlag.current = false;

    contentRef.current.querySelectorAll('.text-card,.graphic-card').forEach((item) => {
      const url = item.querySelector('.img-card-link a')?.getAttribute('href');
      if (!url) return;
      item?.classList.add('with-link-card');
      item.addEventListener('click', () => {
        triggerUrlBehavior(url, history, { from: 'index' });
      });
    });
  }, [contentRef.current, firstFlag.current]);

  useEffect(() => {
    // 如果SHOW_HIDDEN_ELEMENTS为false，则直接跳过所有操作，节省内存消耗
    if (!SHOW_HIDDEN_ELEMENTS || !contentRef.current) return;

    const docListDom = contentRef.current?.querySelector('.doc-link-list');
    if (!docListDom) return;
    if (shownProducts?.length > 0) {
      // 显示隐藏的元素
      docListDom?.parentElement?.parentElement?.setAttribute('style', 'display:block');
      ReactDOM.render(React.createElement(ProductSlider, { data: shownProducts }), docListDom);
    }
  }, [shownProducts, contentRef.current]);

  useEffect(() => {
    // 如果SHOW_HIDDEN_ELEMENTS为false或在服务器端，则跳过获取用户产品的操作
    if (!SHOW_HIDDEN_ELEMENTS || isServer()) return;

    getUserProducts().then((res) => {
      if (res?.code === '200') {
        const productMap = convertTreeToMap(productsData);
        if (isEmpty(productMap)) return;
        const currentData = res?.data?.map((item) => productMap[item])?.filter((item) => item);
        setShownProducts(currentData);
      }
    });
  }, []);

  return (
    <div className={classNames('help-index-detail unionContainer', styles.mainContainer)} >
      <div
        className="markdown-body"
        ref={contentRef}
        dangerouslySetInnerHTML={{ __html: content }}
      />
    </div>
  );
};
export default IndexDetail;
