import React, { useState, useEffect } from 'react';
import { isEmpty } from 'lodash';
import classnames from 'classnames';
import { Loading } from '@alifd/next';
import ProductTree from '@/help-fe-common/pages/Index/ProductTree';
import IndexDetail from '@/help-fe-common/pages/Index/IndexDetail';
import IndexHead from '@/help-fe-common/pages/Index/IndexHead';
import { useIsMobile } from '@/help-fe-common/hooks/useIsMobile';
import { isServer } from '@/help-fe-common/utils/node/getContext';
import { IndexModel } from '@/help-fe-common/models/IndexModel';
import { indexStore } from '@/help-fe-common/models/application/store';
import { storeDataMerge } from '@/help-fe-common/models/common/storeDataMerge';
import styles from './index.module.scss';

async function fetchInitialData(
  // CSR 下使用已经new 过的 Store，避免多份 Store 一直在内存中
  storeMap: {
    indexInnerStore: IndexModel;
  },
) {
  const { indexInnerStore } = storeMap;

  await indexInnerStore.getIndex();

  return { storeData: indexInnerStore };
}

const Index = ({ indexData }) => {
  const [indexModuleData, setIndexModuleData] = useState(indexData?.storeData?.data || []);
  const isMobile = useIsMobile();

  const moduleData = {
    productsData:
      indexModuleData?.find((item) => item.type === 'menu_tree')?.data?.children || [],
    indexContentData:
      indexModuleData?.find((item) => item.type === 'new_index_2025')?.data?.content || '',
  };
  const { productsData, indexContentData } = moduleData;// 初始数据

  useEffect(() => {
    const dataPromise = indexData
      ? Promise.resolve(indexData)
      : fetchInitialData({
        indexInnerStore: new IndexModel(),
      });

    dataPromise
      .then((res) => {
        if (res === null) {
          console.error('csr 渲染问题');
        } else {
          // 初始化数据
          const { storeData } = res;
          setIndexModuleData(storeData?.data);
          storeDataMerge(indexStore, storeData);
        }
      })
      .catch((err) => {
        console.error('[Doc detail] error: ', err);
      });
  }, []);

  useEffect(() => {
    document.body.scrollIntoView();
  }, []);

  return (
    <div className={styles.index} id="index-container">
      {
        isEmpty(indexModuleData) ?
          <Loading className={styles.loading} /> :
          <div className="help-body-full-column">
            <div className="normal-center-container" >
              <div className={classnames(styles.helpContent, 'help-index-product-list')} >
                <div className={classnames(styles.left)} >
                  {
                    (isServer() || !isMobile) ?
                      <div className={styles.pcProductTree}>
                        <ProductTree
                          data={productsData}
                        />
                      </div> :
                      <IndexHead data={productsData} />
                  }
                </div>
                <div className={styles.right}>
                  <IndexDetail content={indexContentData} productsData={productsData} />
                </div>
              </div>
            </div>
          </div>
      }
    </div>
  );
};

Index.getInitialProps = async (ctx) => {
  if (!isServer()) {
    return { indexData: null };
  }

  const res = await fetchInitialData({
    indexInnerStore: new IndexModel(),
  });
  return { indexData: res };
};

Index.pageConfig = {
  spm: '750002',
};

export default Index;

