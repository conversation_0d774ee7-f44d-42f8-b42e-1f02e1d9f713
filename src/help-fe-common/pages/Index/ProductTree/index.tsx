import React, { useEffect, useState, FunctionComponent } from 'react';
import { useHistory } from 'ice';
import classnames from 'classnames';
import { useIntl, FormattedMessage } from 'react-intl';
import { throttle } from 'lodash';
import { triggerUrlBehavior } from '@/help-fe-common/utils/global/url/urlBehavior';
import { getIndexFilterProductList } from '@/help-fe-common/utils/helpDoc/filterList';
import { aLinkTrigger } from '@/help-fe-common/utils/global/url/aLinkTrigger';
import { ITreeItem, computeTreeLength } from '../utils';
import { isSubProduct } from '@/help-fe-common/utils/helpDoc/menu';
import { addStyle } from '@/help-fe-common/utils/global/style/addStyle';
import { headHeight } from '@/help-fe-common/constants';
import { NODE_TYPE } from '@/help-fe-common/constants/detail';
import { getCssNumber } from '@/help-fe-common/utils/global/style/cssValue';
import styles from './index.module.scss';

interface IProps {
  data: ITreeItem[];
}

const FilterTree: FunctionComponent<IProps> = ({ data }) => {
  const intl = useIntl();
  const history = useHistory();
  const initFixedTop = headHeight; // headHeight+paddingTop(40px)
  const [treeFixed, setTreeFixed] = useState(false);
  const [expandedItem, setExpandedItem] = useState([] as string[]);
  const [searchFocus, setSearchFocus] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [searchShow, setSearchShow] = useState(false);
  const [filteredResult, setFilteredResult] = useState<any[]>([]);
  const isTreeNode = (treeItem: ITreeItem) => {
    return treeItem?.children?.length > 0
      && (treeItem?.nodeType === NODE_TYPE.CATEGORY_NODE || treeItem?.nodeType === NODE_TYPE.PRODUCT_NODE)
      && !isSubProduct(treeItem?.children[0]?.showType);
  };

  /**
   * 处理一级产品节点展开逻辑
   * @param item 一级产品分类节点
   */
  const handleExpandClick = (item) => {
    const itemId = item?.id;
    if (expandedItem.includes(itemId)) {
      setExpandedItem(expandedItem.filter((id) => id !== itemId));
    } else {
      setExpandedItem([...expandedItem, itemId]);
    }
  };

  const onTreeItemClick = (treeItem, showArrow) => {
    if (showArrow) {
      handleExpandClick(treeItem);
    } else {
      triggerUrlBehavior(treeItem?.url, history, { from: 'index' });
    }
  };
  /**
   *
   * @param treeItem
   * @returns
   */
  const renderExpandNodeItem = (treeItem: ITreeItem) => {
    const showArrow = isTreeNode(treeItem);
    return (
      <div className={styles.liNodeContainer} onClick={() => onTreeItemClick(treeItem, showArrow)}>
        {showArrow ?
          <>
            {`${treeItem.title}（${computeTreeLength(treeItem?.children)}）`}
            <i className={classnames('help-iconfont',
              expandedItem.includes(treeItem?.id) ? 'help-icon-zhankai1-copy' : 'help-icon-zhankai1')}
            />
          </>
          :
          <a
            id={treeItem?.id}
            href={treeItem?.url}
            onClick={(e) => { e.preventDefault(); }}
          >{treeItem.title}
          </a>}
      </div>
    );
  };

  /**
   * 生成一级类目节点，并处理展开逻辑
   * @param treeData 产品列表树
   * @returns {ReactNode}
   */
  const renderTree = (treeData: ITreeItem[], deepIndex: number) => {
    if (!treeData?.length) return null;

    return (
      <ul className={`${deepIndex === 0 ? styles.treeContainer : styles.childBox}`}>
        {treeData?.length && treeData.map((treeItem: ITreeItem, index) => (
          <li key={index}>
            {renderExpandNodeItem(treeItem)}
            {isTreeNode(treeItem) && expandedItem.includes(treeItem.id) && renderTree(treeItem.children, deepIndex + 1)}
          </li>
        ))}
      </ul>
    );
  };

  /**
   * 处理搜索逻辑
   * @param e 搜索event对象
   */
  const onInputChange = (e) => {
    const value = e?.target?.value;
    setSearchValue(value);
    const cleanValue = value?.trim();
    const filterData = [] as any;
    if (!cleanValue) {
      setFilteredResult(filterData);
      setSearchShow(false);
      return;
    }
    setSearchShow(true);
    const deep = (filterProductList) => {
      if (!filterProductList) {
        return;
      }
      if (Array.isArray(filterProductList)) {
        filterProductList?.forEach((item) => {
          if (!item) {
            return;
          }
          const { children, ...nodeInfo } = item;
          if (isTreeNode(item)) {
            deep(children);
          } else {
            filterData.push(nodeInfo);
          }
        });
      }
    };
    deep(getIndexFilterProductList(cleanValue, data));
    setFilteredResult(filterData);
  };

  const onUrlClick = (e, url) => {
    e.preventDefault();
    aLinkTrigger(e, history);
  };


  /**
   * 固定产品树
   */
  const addFixed = () => {
    const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
    const navHeight = getCssNumber('--default-nav-height');
    // 滚动距离大于导航高度+banner内容高度，固定目录树
    if (scrollTop > navHeight) {
      setTreeFixed(true);
      const contentBottom = document.querySelector('.help-index-product-list')?.getBoundingClientRect()?.bottom || 0;
      const menuDOM = document.querySelector('.help-index-menu');
      const menuFixedTop = contentBottom - window.innerHeight;

      // 底部出现时，保持menu部分跟随滚动。计算逻辑为顶部固定高度和menu区域padding-bottom:40px组成
      if (menuFixedTop < 0) {
        addStyle(menuDOM, 'top', `${initFixedTop + menuFixedTop}px`);
      } else {
        addStyle(menuDOM, 'top', `${initFixedTop}px`);
      }
    } else {
      setTreeFixed(false);
    }
  };

  const throttleAddFixed = throttle(() => addFixed(), 16);

  useEffect(() => {
    window.addEventListener('scroll', throttleAddFixed);
    return () => {
      window.removeEventListener('scroll', throttleAddFixed);
    };
  }, []);

  return (
    <div className={classnames('help-index-menu', treeFixed ? styles.fixed : '')}>
      <div className={styles.container}>
        <div className={styles.head}>
          <h2><FormattedMessage id="help.index.productList" /></h2>
          <div className={styles.searchContainer}>
            <div className={classnames(styles.searchInputWrapper, searchFocus ? styles.searchFocus : '')} >
              <i className="help-iconfont help-icon-sousuoicon" />
              <input
                value={searchValue}
                placeholder={intl.formatMessage({ id: 'help.index.productList.search' })}
                className={classnames(styles.searchInput)}
                onChange={(e) => onInputChange(e)}
                onFocus={() => { setSearchFocus(true); }}
                onBlur={(e) => {
                  e.target.value = '';
                  setSearchFocus(false);
                  setTimeout(() => {
                    setSearchShow(false);
                    setFilteredResult([]);
                  }, 300);
                }}
                autoComplete="off"
              />
              {
                searchValue &&
                <button
                  className={styles.clearButton}
                  onClick={() => { onInputChange(null); setSearchValue(''); }}
                >
                  <i className="help-iconfont help-icon-quxiao" />
                </button>
              }
            </div>
            {
              searchShow ?
                <div className={classnames(styles.resultContainer)}>
                  {
                    filteredResult?.length ?
                      filteredResult?.map((item, index) => {
                        return (
                          <div
                            key={index}
                            className={classnames(styles.resultItem)}
                            data-href={item?.url}
                            onClick={(e) => { onUrlClick(e, item?.url || `/document_detail/${item?.id}.html`); }}
                          >
                            <span dangerouslySetInnerHTML={{ __html: item?.title }} />
                          </div>
                        );
                      }) :
                      <div className={styles.resultNoData}>
                        <FormattedMessage id="help.index.productList.noData" />
                      </div>
                  }
                </div> :
                null
            }
          </div>

        </div>
        {renderTree(data, 0)}
      </div>
    </div>
  );
};

export default FilterTree;
