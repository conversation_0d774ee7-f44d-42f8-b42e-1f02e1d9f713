.fixed {
  position: fixed;
  top: calc(var(--default-head-height) + 40px); // headHeight+paddingTop
}

.container {
  width: 284px;

  .head {
    padding: 40px 24px 0 24px;

    h2 {
      line-height: 22px;
      margin-bottom: 24px;
      font-size: 16px;
    }

    .searchContainer {
      margin-bottom: 24px;
      position: relative;

      .searchInputWrapper {
        height: 30px;
        width: 100%;
        padding: 0 8px;
        font-size: 13px;
        color: #b3b3b3;
        border: 1px solid rgba(212, 214, 219);
        display: flex;
        align-items: center;

        i {
          font-size: 14px;
          color: #181818;
        }

        .searchInput {
          padding-left: 10px;
          height: 30px;
          line-height: 30px;
          width: 100%;
          outline: none;
          border: none;
          background: transparent;
          letter-spacing: 0;

          &::placeholder {
            color: #b3b3b3;
          }
        }

        .clearButton {
          cursor: pointer;
          border: none;
          background: none;

          i {
            color: #999;
            font-size: 10px;

            &:hover {
              color: #1366ec;
            }
          }
        }

        &:hover {
          border-color: #1366ec;

          > i {
            color: #1366ec;
          }
        }
      }

      .searchFocus {
        .searchInput,
        > i {
          color: #1366ec;
        }
      }

      .resultContainer {
        position: absolute;
        width: 100%;
        min-height: 40px;
        max-height: 300px;
        padding: 5px 0;
        overflow: auto;
        background-color: #fff;
        box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.1);
        border-radius: 0 0 2px 2px;
        z-index: 100;

        &::-webkit-scrollbar {
          width: 3px;
          height: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #d8d8d8;
          border-radius: 3px;
        }

        .resultItem {
          width: 100%;
          height: auto;
          line-height: 20px;
          padding: 6px 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          word-break: break-all;
          word-wrap: break-word;

          span {
            font-size: 12px;
          }

          em {
            color: #1366ec;
            font-style: normal;
          }

          &:hover {
            cursor: pointer;
            color: #1366ec;
            background-color: #f5f5f6;
          }
        }

        .highlightResult {
          background-color: #f5f5f6;
        }

        .resultNoData {
          padding: 20px;
          font-size: 12px;
          color: #979797;
          letter-spacing: 0;
          line-height: 24px;

          a {
            color: #1366ec;
          }
        }
      }
    }
  }
}

.treeContainer {
  width: 100%;
  padding: 0 24px 40px 24px;
  max-height: calc(100vh - 180px);
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 5px;
    height: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #dfdfdf;
    border-radius: 5px;
  }

  .liNodeContainer {
    margin-left: 0;
    margin-bottom: 0;
    min-height: 36px;
    line-height: 24px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    font-weight: 600;

    > i {
      height: 36px;
      line-height: 36px;
      width: 16px;
      font-size: 11px;
      font-weight: 500;
      color: #999;
    }

    > a {
      color: #999;
    }

    &:hover {
      color: #1366ec;

      i,
      > a {
        color: #1366ec;
      }
    }
  }

  .childBox {
    padding-left: 8px;
    animation-name: box-expand;
    animation-duration: 0.3s;

    .liNodeContainer {
      color: #999;
      padding-left: 12px;
      border-left: 1px solid #e9e9e9;
      font-weight: normal;

      &:hover {
        color: #1366ec;

        i,
        > a {
          color: #1366ec;
        }
      }
    }

    .childBox {
      padding-left: 20px;
      border-left: 1px solid #e9e9e9;
    }
  }

  @keyframes box-expand {
    0% {
      opacity: 0;
    }

    25% {
      opacity: 0.25;
    }

    50% {
      opacity: 0.5;
    }

    75% {
      opacity: 0.75;
    }

    100% {
      opacity: 1;
    }
  }
}

// mobile端
@media only screen and (max-width: 1055px) {
  .fixed {
    position: relative;
    top: 0;
  }

  .container {
    width: 100%;
    box-shadow: none;

    .head {
      h2 {
        display: none;
      }
    }
  }
}
