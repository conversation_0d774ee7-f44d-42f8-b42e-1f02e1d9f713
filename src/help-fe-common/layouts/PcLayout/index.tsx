import React, { RefObject, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { useLocation } from 'ice';
import MobileHelpBodyHead from '@/help-fe-common/components/mobile/HelpBodyHead';
import CommonMenu from '@/help-fe-common/components/pc/Menu';
import useMenu from '@/help-fe-common/hooks/useMenu';
import { REQUEST_STATUS } from '@/help-fe-common/hooks/useCommonRequest';
import selectionPlugin from '@/help-fe-common/plugins/docDetailProcessor/selection';
import { extractUrlParam } from '@/help-fe-common/utils/global/url/urlExtract';
import { getModuleType, moduleEnum } from '@/utils/getModuleType';
import isMobile from '@/help-fe-common/utils/global/isMobile';
import styles from './index.module.scss';

const Index = ({ children }) => {
  const location = useLocation();
  const docRef: RefObject<any> = useRef();
  const identifier = useMemo(() => extractUrlParam(location?.pathname), [location?.pathname]);
  const [showMenu, setShowMenu] = useState(true);
  const [menuData, setMenuData] = useState<any>({});
  const [isMobileFlag, setIsMobileFlag] = useState(false);

  const {
    request,
    productInfo: newProductInfo,
    searchProductInfo,
    productList,
    menuTreeList: newTreeList,
    requestStatus } = useMenu();

  const onResize = () => {
    setIsMobileFlag(isMobile());
  };

  const highlightStatus: moduleEnum = useMemo(() => {
    return getModuleType(location?.pathname);
  }, [location?.pathname]);

  useEffect(() => {
    if (requestStatus === REQUEST_STATUS.NODATA && setShowMenu) {
      setShowMenu(false);
    }
  }, [requestStatus]);

  useEffect(() => {
    setMenuData({
      newProductInfo,
      searchProductInfo,
      productList,
      newTreeList,
    });
  }, [newProductInfo, searchProductInfo, productList, newTreeList]);

  useEffect(() => {
    if (identifier) {
      const { nodeId, alias } = identifier;
      request({ nodeId, alias });
    }
    // 考虑到一些特殊场景： 当切换到kb页面，又切换到一个非kb页面时，菜单需要感知变化重新获取
    setShowMenu(true);
  }, [identifier]);

  // 解决刷新页面滚动位置跳动
  useLayoutEffect(() => {
    if ('scrollRestoration' in window.history) {
      window.history.scrollRestoration = 'manual';
    }

    // 监听dom变化
    const resize = new ResizeObserver((entries) => {
      if (!Array.isArray(entries) || !entries.length) return;
      onResize();
    });
    if (!docRef?.current) return;
    resize.observe(docRef.current);

    return () => {
      resize.unobserve(docRef?.current);
    };
  }, []);

  useEffect(() => {
    // 执行selection划词插件
    const [, bindSelectionEvent, unbindSelectionEvent] = selectionPlugin;
    bindSelectionEvent && bindSelectionEvent();
    return () => {
      unbindSelectionEvent && unbindSelectionEvent();
    };
  }, []);

  return (
    <div className={styles.docsContainer} ref={docRef}>
      {
        highlightStatus === moduleEnum.DOC_MODULE && location?.pathname !== '/' &&
        <div className={styles.mobileHelpBodyHead}>
          <MobileHelpBodyHead isMobileFlag={isMobileFlag} menuData={menuData} />
        </div>
      }
      <div className={styles.aliyunAppLayout}>
        {
          showMenu &&
          <nav className="aliyun-docs-menu" id="aliyun-docs-menu">
            <CommonMenu menuData={menuData} />
          </nav>
        }
        <main className="aliyun-docs-view" id="aliyun-docs-view">
          {children}
        </main>
      </div>
    </div>
  );
};

export default Index;
