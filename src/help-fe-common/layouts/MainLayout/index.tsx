import React, { useState, useMemo, useEffect, useRef, RefObject } from 'react';
import { throttle } from 'lodash';
import { useLocation, useRequest, useHistory } from 'ice';
import HelpBodyHead from '@/components/Common/HelpBodyHead';
import Tip from '@/help-fe-common/components/common/Tip';
import service from '@/help-fe-common/services/language';
import { followScroll } from '@/help-fe-common/utils/helpDoc/scrollFixed';
import { LANG, isCN } from '@/help-fe-common/utils/global/website';
import { setCssValue } from '@/help-fe-common/utils/global/style/cssValue';
import { extractUrlParam } from '@/help-fe-common/utils/global/url/urlExtract';
import { navTopHeight, headHeight } from '@/help-fe-common/constants/index';
import styles from './index.module.scss';
import './index.scss';

const Index = ({ children }) => {
  const history = useHistory();
  const location = useLocation();
  const [showTip, setShowTip] = useState(false);
  const identifier = useMemo(() => extractUrlParam(location?.pathname), [location?.pathname]);
  const { request: getLanguage } = useRequest(service.getLanguage);

  const docRef: RefObject<any> = useRef();
  const throttleFollowScroll = throttle(() => followScroll(), 16);
  const onScroll = () => {
    throttleFollowScroll();
  };

  const onResize = () => {
    const navHeight = docRef?.current?.offsetTop;
    // 设置navLoader高度（官网头部）
    setCssValue('--default-nav-height', `${navHeight}px`);
  };

  /**
   * 查询兜底语言逻辑，仅针对国际站有效
   */
  useEffect(() => {
    if (identifier && !isCN) {
      setShowTip(false);
      const { nodeId, alias } = identifier;
      getLanguage({ nodeId, alias }).then((res) => {
        if (res?.data && res?.data !== LANG) {
          setShowTip(true);
        }
      });
    }
  }, [identifier]);

  // 监听history 行为，页面地址发生变化时设置滚动条高度
  useEffect(() => {
    const listener = history.listen((historyLocation: any, action) => {
      // 如果是详情页或者hash锚点跳转，不重置滚动条
      if (!['detail', 'menu']?.includes(historyLocation?.state?.from) && !historyLocation?.hash) {
        const offsetTop = docRef?.current?.offsetTop;
        document.body.scrollTop = offsetTop;
        document.documentElement.scrollTop = offsetTop;
      }
    });
    // 清理监听器
    return () => {
      listener();
    };
  }, [history]);

  // 监听hash变化，改变页面高度
  useEffect(() => {
    const { hash } = location;
    if (!hash) return;
    const id = hash.replace('#', '');
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView();
    }
  }, [location?.hash]);

  useEffect(() => {
    // 初始化设置css变量
    setCssValue('--default-nav-height', `${navTopHeight}px`);
    setCssValue('--default-head-height', `${headHeight}px`);
    // 初始化执行一次，避免滚动条定位问题
    onScroll();
    // 监听滚动事件
    window.addEventListener('scroll', onScroll);
    // 监听dom变化
    const resize = new ResizeObserver((entries) => {
      if (!Array.isArray(entries) || !entries.length) return;
      onResize();
    });
    if (!docRef?.current) return;
    resize.observe(docRef.current);

    return () => {
      window.removeEventListener('scroll', onScroll);
      resize.unobserve(docRef?.current);
    };
  }, []);

  return (
    <div className={styles.mainContainer} id="docs-container" ref={docRef}>
      {
        showTip ?
          <Tip onTipClick={() => { setShowTip(false); }} />
          :
          null
      }
      {
        (isCN &&
          <div className={styles.helpBodyHead}>
            <HelpBodyHead showSearch={location?.pathname !== '/'} />
          </div>)
      }
      {children}
      <div id="aliyun-docs-selection-div" />
    </div>
  );
};

export default Index;
