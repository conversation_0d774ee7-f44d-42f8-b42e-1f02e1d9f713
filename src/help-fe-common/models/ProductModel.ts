import { getInitialData } from 'ice';
import service from '@/help-fe-common/services/apiService';
import { WEBSITE, LANG } from '@/help-fe-common/utils/global/website';
import { extractUrlParam } from '@/help-fe-common/utils/global/url/urlExtract';
import { PAGE_TYPE } from '@/help-fe-common/constants/index';

export class ProductModel {
  data: any = [];
  // 别名、id
  alias = '';
  nodeId = '';

  // 是否显示notfound页
  isNotFound = false;

  // 响应状态码
  helpResponseCode = 200;
  redirectUrl = '';

  // 产品首页接口
  getProductInfo = async (path) => {
    const params = {
      productId: '',
      alias: '',
      website: WEBSITE,
      language: LANG,
    };
    const identifier = extractUrlParam(path);
    if (!identifier) {
      this.isNotFound = true;
      return;
    }
    const { nodeId, alias } = identifier;
    params.productId = nodeId;
    params.alias = alias as string;
    this.nodeId = nodeId;
    this.alias = alias as string;

    const productInitialData = getInitialData();

    // 如果有缓存，且为当产品的数据
    if (productInitialData &&
      JSON.stringify(productInitialData) !== '{}' &&
      productInitialData?.pageType === PAGE_TYPE.PRODUCT &&
      productInitialData?.data?.path === path) {
      this.dealWithRes(productInitialData);
    } else {
      const result: any = await service.getProductInfo(params);

      this.helpResponseCode = result?.code;

      if (result?.code === 404 && !result?.success) {
        this.isNotFound = true;
      } else if (result?.data?.redirectUrl) {
        this.redirectUrl = result?.data?.redirectUrl;
      } else if (result?.data) {
        this.dealWithRes(result);
      } else {
        this.isNotFound = true;
      }
    }
  };


  // 处理返回数据
  dealWithRes = (res) => {
    if (!res) return;
    this.data = res?.data;
  };

  // 初始化
  initData = () => {
    this.data = [];
    this.isNotFound = false;
  };
}
