/**
 * @description: SSR 注水覆盖 CSR store 数据，支持CSR下某些字段跳过覆盖
 * @param {Model} rawStore
 * @param {Model} dataStore
 * @param {undefined | string[]} skipFields
 * @return {*}
 */
const storeDataMerge = (rawStore, dataStore, skipFields?) => {
  if (skipFields === undefined) {
    skipFields = [];
  }
  for (const key in dataStore) {
    if (Object.prototype.hasOwnProperty.call(dataStore, key)) {
      if (!skipFields.includes(key)) {
        // 不在skip中的，直接覆盖。在skip中的不做处理
        rawStore[key] = dataStore[key];
      }
    }
  }
};

export {
  storeDataMerge,
};
