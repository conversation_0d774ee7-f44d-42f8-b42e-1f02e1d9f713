import { getInitialData } from 'ice';
import service from '@/help-fe-common/services/apiService';
import { WEBSITE, LANG } from '@/help-fe-common/utils/global/website';
import { PAGE_TYPE } from '@/help-fe-common/constants/index';

export class IndexModel {
  data: any=[];

  // 响应状态码
  helpResponseCode = 200;

  // 获取首页数据
  getIndex = async () => {
    const params = {
      website: WEBSITE,
      language: LANG,
    };
    const indexInitialData = getInitialData();

    // 如果有缓存，且为首页的数据
    if (indexInitialData &&
      JSON.stringify(indexInitialData) !== '{}' &&
      indexInitialData?.pageType === PAGE_TYPE.INDEX) {
      this.dealWithRes(indexInitialData);
    } else {
      const result: any = await service.getIndex(params);
      this.helpResponseCode = result?.code;
      if (result) {
        this.dealWithRes(result);
      }
    }
  };

  // 处理返回数据
  dealWithRes = (res) => {
    if (!res) return;
    this.data = res?.data;
  };

  // 初始化
  initData =() => {
    this.data = [];
  };
}
