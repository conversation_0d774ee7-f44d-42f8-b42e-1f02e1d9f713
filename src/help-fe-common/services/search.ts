import { request } from 'ice';
import { LANG, WEBSITE, CHANNEL } from '@/help-fe-common/utils/global/website';

export default {
  /**
   * 搜索接口
   * @string keywords 搜索关键字
   * @string categoryId 产品id/实战派领域id
   * @string topics 搜索组合类型，PRODUCT/DOCUMENT/PRACTICE
   * @returns
   */
  async getSearch({ website = 'cn', language = 'zh', ...params }) {
    const { pageSize, pageNum, keywords, categoryId, topics, plateCode, level } = params;
    const rst = await request({
      url: 'help/json/search.json',
      params: {
        keywords,
        categoryId,
        topics,
        plateCode,
        level,
        website,
        language,
        pageSize,
        pageNum,
      },
    });
    return rst?.data;
  },

  // 新版suggest接口
  async getSuggest({ keywords, productId, website, language }) {
    website = website || WEBSITE;
    language = LANG;
    const rst = await request({
      url: 'help/json/search/suggest.json',
      params: {
        keywords,
        productId,
        website,
        language,
        channel: CHANNEL,
      },
    });
    return rst?.data;
  },
};

