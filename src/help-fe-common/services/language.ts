import { request } from 'ice';
import { IDocParam } from '@/help-fe-common/services';
import { LANG, WEBSITE } from '@/help-fe-common/utils/global/website';

export default {

  // 文档、产品语言查询接口
  async getLanguage({ alias, nodeId, website, language }: IDocParam) {
    const rst = await request(
      {
        url: '/help/json/language/decide.json',
        params: {
          alias,
          nodeId: nodeId || '',
          website: website || WEBSITE,
          language: language || LANG,
        },
      },
    );
    return rst;
  },
};
