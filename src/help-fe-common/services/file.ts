/**
 * 文件上传相关接口
 */
import { request } from 'ice';
import { getCsrfToken } from '@/help-fe-common/utils/global/getCsrfToken';

const cr_token = getCsrfToken();

export default {
  /**
   * 上传文件到 OSS
   * @param file 文件对象
   * @param fileName 文件名称
   * @return previewUrl 文件访问地址
   */
  async uploadFile(file, fileName = file.name) {
    const credentials = await this.getUploadCredentials(fileName);
    const {
      accessId,
      host,
      signature,
      policy,
      dir,
      previewUrl,
    } = credentials;
    const params = {
      OSSAccessKeyId: accessId,
      policy,
      Signature: signature,
      key: dir,
      file,
      success_action_status: '200',
    };
    const formData = new FormData();
    // eslint-disable-next-line guard-for-in
    for (const key in params) {
      formData.append(key, params[key]);
    }
    try {
      await request({
        url: `https://${host}`,
        method: 'POST',
        data: formData,
      });
    } catch (e) {
      console.error('UploadFileFailed', e);
      throw e;
    }
    return previewUrl;
  },
  /**
   * 获取用于上传文件到 OSS 的凭证
   * @see "https://yuque.antfin-inc.com/gcsdev/zvi1e4/ve6q6m#KeblM"
   * @param fileName 待上传的文件名
   * @return credentials OSS 签名凭证
   */
  async getUploadCredentials(fileName) {
    const rst = await request({
      url: '',
      method: 'POST',
      data: {
        fileName,
      },
      params: {
        cr_token,
      },
    });
    return rst?.data;
  },
};
