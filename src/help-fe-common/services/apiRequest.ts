/**
 * @description api请求封装，主要场景为ssr渲染需要处理baseUrl
 */

import { request } from 'ice';
import { isServer, getReqBaseURL } from '@/help-fe-common/utils/node/getContext';
import { ENV, getEnv } from '@/help-fe-common/utils/global/env';
import { sendSlsLog } from '../utils/global/track/slsLogger';

const apiRequest = async function <T = any>(url, options): Promise<T> {
  options = options || {};

  if (isServer()) {
    const baseURL = getReqBaseURL();
    options.baseURL = baseURL;
  }
  if (process.env.NODE_ENV === 'development') {
    // 本地调试环境

    if (isServer()) {
      options.baseURL = getReqBaseURL();
    } else if (process.env.IS_CSR) {
      options.baseURL = '';
    } else {
      // options.baseURL = `http://localhost:8080`;
      options.baseURL = 'https://pre-help.aliyun.com';
    }
  } else {
    options.baseURL = `https://${getEnv() === ENV.PROD ? '' : 'pre-'}help.aliyun.com`;
  }

  const racePromiseList: Array<Promise<any>> = [];

  const MAX_RETRIES = 3;
  const RETRY_DELAY = 1000; // 1秒

  function requestWithRetry(retries = 0) {
    return request({ ...options, url })
      .then((val) => {
        return val;
      })
      .catch((error) => {
        sendSlsLog({
          page: '',
          action: 'requestError',
          userParams1: JSON.stringify(error),
          useParams2: `Request failed. Retrying... (${retries + 1}/${MAX_RETRIES})`,
        });
        if (retries < MAX_RETRIES) {
          return new Promise((resolve) => setTimeout(() => resolve(requestWithRetry(retries + 1)), RETRY_DELAY));
        } else {
          throw error;
        }
      });
  }

  // 使用示例
  const requestPromise = requestWithRetry(0);

  racePromiseList.push(requestPromise);

  return await Promise.race(racePromiseList);
};
export default apiRequest;
