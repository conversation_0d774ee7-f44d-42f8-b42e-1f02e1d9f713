import { request } from 'ice';
import { parseQuery } from '@/help-fe-common/utils/global/url/parseQuery';

class PreviewService {
  /**
   * @public
   * 是否是预览模式
   */
  isPreviewMode() {
    const query = parseQuery(location.search);
    return !!query?.previewContentUrl;
  }

  /**
   * @public
   * 获取预览内容
   */
  async getDocumentDetailForPreview(): Promise<any | null> {
    const previewContentUrl = this.getPreviewContentUrl();
    if (!previewContentUrl) {
      return null;
    }
    const response = await request({
      url: previewContentUrl,
    });
    const docHtml = response.data;
    const [title, content] = this.convertContent(docHtml);
    return this.createMockDocumentDetail(title, content);
  }

  /**
   * @private
   * 预览时，构造模拟的 documentDetail 数据
   * @param title
   * @param content
   */
  createMockDocumentDetail(title, content) {
    return {
      data: {
        pageType: 'preview',
        authorName: 'icms',
        content,
        language: '',
        lastModifiedTime: new Date().getTime(),
        level: 5,
        nodeId: 444409,
        nodeType: 1,
        productNodeVO: {
          id: 25365,
          level: 3,
          title: '',
        },
        showType: 1,
        taskStatus: 'NO_TASK_AVAILABLE',
        title,
        userVO: {},
        website: '',
      },
    };
  }

  /**
   * @private
   * 获取预览内容获取地址
   */
  getPreviewContentUrl(): string | undefined {
    const query = parseQuery(location.search);
    // @ts-ignore 预览场景，query 中只会有一个 previewContentUrl
    return query?.previewContentUrl;
  }

  /**
   * @private
   * 将 icms 预览的内容转为跟帮助中心最后拿到的 html 一致
   * @param docHtml 文档 html 内容
   */
  convertContent(docHtml) {
    const docDocument = new DOMParser().parseFromString(docHtml, 'text/html');
    const { title, body } = docDocument;
    const lang = docDocument.querySelector('html')?.lang;
    docDocument.querySelector('h1[data-tag=title]')?.remove();
    const content = `<div lang="${lang || 'zh-CN'}" class="icms-help-docs-content">${body.innerHTML}</div>`;
    return [title, content, lang];
  }
}

export default new PreviewService();
