import { request } from 'ice';
import { WEBSITE, LANG, CHANNEL } from '@/help-fe-common/utils/global/website';
import previewService from '@/help-fe-common/services/preview';

interface ICommonParam {
  website?: string;
  language?: string;
}

interface IProductParam {
  alias?: string;
  productId?: string;
  website?: string;
  language?: string;
}

export interface IDocParam {
  alias?: string | null;
  productAlias?: string;
  docAlias?: string;
  nodeId?: string;
  pageNum?: number;
  website?: string;
  language?: string;
}

interface PipCodeParam {
  productId: string;
}

export default {
  // 首页接口
  async getIndex({ website, language }: ICommonParam) {
    const rst = await request(
      {
        url: '/help/json/getIndexModules.json',
        params: {
          website: website || WEBSITE,
          language: language || LANG,
        },
      },
    );
    return rst?.data;
  },

  // 产品列表接口
  async getProductList({ website, language }: ICommonParam) {
    let rst = { data: { children: [] } };
    if (window.menuTreeJSON && window.menuTreeJSON.success) {
      rst = window.menuTreeJSON;
    } else {
      rst = await request(
        {
          url: '/help/json/mainMenu.json',
          params: {
            website: website || WEBSITE,
            language: language || LANG,
            channel: CHANNEL,
          },
        },
      );
      window.menuTreeJSON = rst;
    }
    return rst?.data;
  },

  // 目录树接口
  async getMenuTreeList({ nodeId, alias, website, language }: IDocParam) {
    if (previewService.isPreviewMode()) {
      return null;
    }
    const rst = await request({
      url: '/help/json/menupath.json',
      params: {
        nodeId,
        alias,
        website: website || WEBSITE,
        language: language || LANG,
        channel: CHANNEL,
      },
    });
    return rst;
  },

  // 子产品列表接口
  async getSubProductList({ nodeId, alias, website, language }: IDocParam) {
    if (previewService.isPreviewMode()) {
      return null;
    }
    const rst = await request({
      url: '/help/json/menupath.json',
      params: {
        nodeId,
        alias,
        type: 'sub_module',
        website: website || WEBSITE,
        language: language || LANG,
        channel: CHANNEL,
      },
    });
    return rst;
  },

  // 产品首页接口
  async getProductInfo({ alias, productId, website, language }: IProductParam) {
    const rst = await request({
      url: '/help/json/product.json',
      params: {
        alias,
        productId,
        website: website || WEBSITE,
        language: language || LANG,
        channel: CHANNEL,
      },
    });
    return rst;
  },

  // 文档详情接口
  async getDocumentDetail({ nodeId, alias, pageNum, website, language }: IDocParam) {
    let rst: any = {};
    // 如果有预览参数，则替换为预览数据
    if (previewService.isPreviewMode()) {
      const previewData = await previewService.getDocumentDetailForPreview();
      rst.data = previewData;
      return rst;
    }
    rst = await request({
      url: '/help/json/document_detail.json',
      params: {
        nodeId,
        alias,
        pageNum: pageNum || 1,
        pageSize: 20,
        website: website || WEBSITE,
        language: language || LANG,
        channel: CHANNEL,
      },
    });
    return rst;
  },

  // 查询产品Code
  async getPipCode({ productId }: PipCodeParam) {
    const res = await request({
      url: '/help/json/getPipCode.json',
      params: {
        productId,
      },
    });

    return res?.data;
  },
};

