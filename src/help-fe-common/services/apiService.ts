import request from '@/help-fe-common/services/apiRequest';
import previewService from '@/help-fe-common/services/preview';
import addInitContentSpace from '@/help-fe-common/utils/helpDoc/addSpace';
import { generateFecsUrl } from '@/help-fe-common/utils/global/request';

export default {
  // 首页数据获取
  async getIndex(
    params: {
      website: string;
      language: string;
    },
    options?: { [key: string]: any },
  ) {
    return request('/help/json/getIndexModules.json', {
      method: 'GET',
      params,
      responseType: '*/*',
      ...(options || {}),
    });
  },

  // 获取我的产品列表
  async getUserProducts() {
    const res = await request(generateFecsUrl('/data/call.json'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      withCredentials: true,
      params: {
        product: 'one-console-app-myalicloud',
        action: 'ListUserProduct',
      },
    });
    return res;
  },

  // 产品首页数据获取
  async getProductInfo(
    params: {
      productId: string;
      alias: string;
      website: string;
      language: string;
    },
    options?: { [key: string]: any },
  ) {
    return request('/help/json/product.json', {
      method: 'GET',
      params,
      responseType: '*/*',
      ...(options || {}),
    });
  },

  // 详情页数据获取
  async getDocumentDetail(
    params: {
      nodeId: string;
      alias: string;
      pageNum?: number;
      pageSize?: number;
      website?: string;
      language?: string;
    },
    options?: { [key: string]: any },
  ) {
    let result = {} as any;
    if (previewService.isPreviewMode()) {
      result = await previewService.getDocumentDetailForPreview();
    } else {
      result = await request('/help/json/document_detail.json', {
        method: 'GET',
        params,
        responseType: '*/*',
        ...(options || {}),
      });
    }
    // 接口层统一处理content中英文间距
    if (result?.data) {
      const content = addInitContentSpace(result.data, result.data.content);
      return {
        ...result,
        data:
          { ...result?.data, content },
      };
    } else {
      return result;
    }
  },

  // 详情页动态数据获取
  async getDetailDynamicData(
    params: {
      nodeId: string;
      alias: string;
      website: string;
      language: string;
    },
    options?: { [key: string]: any },
  ) {
    return request('/help/json/ssr/dynamic.json', {
      method: 'GET',
      params,
      responseType: '*/*',
      ...(options || {}),
    });
  },
};
