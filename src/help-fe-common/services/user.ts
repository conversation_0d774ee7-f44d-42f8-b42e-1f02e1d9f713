import request from '@/help-fe-common/services/apiRequest';

export default {
  /**
   * 获取用户详情信息
   */
  async getUserInfo() {
    const rst = await request('/help/json/user/detail.json', {
      method: 'GET',
      params: {},
      withCredentials: true,
    });
    return rst?.data;
  },

  /**
   * 获取登录状态
   */
  async checkLogin() {
    const rst = await request('/help/json/user/login.json', {
      method: 'GET',
      params: {},
      withCredentials: true,
    });
    return rst?.data;
  },
};
