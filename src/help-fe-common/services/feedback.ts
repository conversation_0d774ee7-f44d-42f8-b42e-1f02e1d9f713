import { getReqBaseURL } from '@/help-fe-common/utils/node/getContext';

/**
 * 反馈文件上传相关接口
 */
import { request } from 'ice';
import CookieHelper from '@/help-fe-common/utils/global/cookie';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import { WEBSITE, LANG } from '@/help-fe-common/utils/global/website';

const cr_token = CookieHelper.get('cr_token') || '';

export default {
  /**
    * 新版反馈接口
    * @param nodeId 文档id
    * @param alias 文档别名
    * @param id 反馈id
    * @param score 评分，有效值-1，0，1
    * @param content 反馈内容
    * @param contact 联系方式
    * @param type  问题类型，当content非空时该字段不能为空，枚举值包括：
                   CONTENT_ACCURACY - 内容准确性
                   PRODUCT_USE - 云产品使用
                   SEARCH_ACCURACY - 搜索准确性
                   CONTENT_ILLEGAL - 内容侵权或违法
                   OTHERS - 其他问题
    * @param snapshot 文档截图
    * @param cr_token cr_token，防止csrf攻击
    * @returns
    */
  async sendFeedback(params) {
    const { nodeId, alias, id, title, url, score, snapshot, content, contact, type } = params;
    const rst = await request({
      baseURL: getReqBaseURL(),
      url: '/help/json/addHelpFeedback.json',
      method: 'POST',
      data: {
        nodeId,
        alias,
        id,
        title,
        url,
        website: WEBSITE,
        language: LANG,
        score,
        snapshot,
        content,
        contact,
        type,
        _input_charset: 'utf8',
      },
      params: {
        cr_token,
      },
    });
    return rst;
  },
  /**
   * 获取用于上传文件到 OSS 的凭证
   * @see "https://yuque.antfin.com/docs/share/8f9a8ea7-b567-4b1d-8a5a-b89378307f5c?#bAHyP"
   * @param nodeId 文档id
   * @param alias 文档别名
   * @return credentials OSS 签名凭证
   */
  async getFeedBackUploadInfo({ nodeId, alias }) {
    const rst = await request({
      baseURL: getReqBaseURL(),
      url: '/help/json/oss/feedback/getUploadInfo.json',
      method: 'POST',
      data: {
        nodeId,
        alias,
        website: WEBSITE,
        language: LANG,
      },
      params: {
        cr_token,
      },
    });
    return rst?.data;
  },

  /**
    * 上传文件到 OSS
    * @param file 文件对象
    * @param credentials oss签名信息
    * @param imgUrl 文件资源
    * @return previewUrl 文件访问地址
    */
  async uploadSnapshot(credentials, file, imgUrl) {
    const {
      accessKeyId,
      host,
      signature,
      policy,
      key,
      accessUrl,
    } = credentials;
    const params = {
      OSSAccessKeyId: accessKeyId,
      policy,
      Signature: signature,
      key,
      file,
      success_action_status: '200',
    };
    const formData = new FormData();
    // eslint-disable-next-line guard-for-in
    for (const item in params) {
      formData.append(item, params[item]);
    }
    try {
      await request({
        url: `https://${host}`,
        method: 'POST',
        data: formData,
      });
    } catch (e) {
      sendSlsLog({
        page: 'documentDetail',
        section: 'feedback',
        action: 'uploadError',
        userParams1: JSON.stringify({ credentials, file: imgUrl }),
      });
    }
    return accessUrl;
  },
};
