import { isCN } from '@/help-fe-common/utils/global/website';

export const spmA = isCN ? 'a2c4g' : 'a2c63';
export const selectionTooltipId = 'selection-feedback-tooltip'; // 选区反馈气泡

export const navTopHeight = isCN ? 64 : 108; // 公共头部高度
export const headHeight = isCN ? 40 : 0; // helpBodyHead 高度
export const fixedHeight = isCN ? 64 : 0; // 目录、导读固定top

// 众包项目入口，根据文档决定是否展示捉虫入口及相关提示；
export const TaskAvailabilityStatusEnum = ['NO_TASK_AVAILABLE', 'TASK_AVAILABLE', 'TASK_AVAILABLE_LIMIT_EXCEEDED'];

export const CardType = {
  0: 'getting_started',
};

export const ProductIcon = [
  { icon: 'https://img.alicdn.com/imgextra/i3/O1CN01HO4b1Y1M3cuz8LH8i_!!6000000001379-2-tps-124-122.png' },
  { icon: 'https://img.alicdn.com/imgextra/i3/O1CN01fEzHtG1GnWv4XrR8n_!!6000000000667-2-tps-124-122.png' },
  { icon: 'https://img.alicdn.com/imgextra/i1/O1CN01G8ziQW27YZcatuFQI_!!6000000007809-2-tps-124-122.png' },
  { icon: 'https://img.alicdn.com/imgextra/i2/O1CN018jJVZU1QYY7fAZLE7_!!6000000001988-2-tps-124-122.png' },
  { icon: 'https://img.alicdn.com/imgextra/i3/O1CN01sh5puB1wZz41sqd5J_!!6000000006323-2-tps-122-122.png' },
];

export const IndexStartDataImg = [
  {
    label: '了解云计算',
    url: 'https://img.alicdn.com/imgextra/i2/O1CN01V8BCMW29xstSL9rDw_!!6000000008135-0-tps-1000-288.jpg',
  }, {
    label: '上云安全',
    url: 'https://img.alicdn.com/imgextra/i4/O1CN011dRdUw1NWlz1l3zNN_!!6000000001578-0-tps-1000-288.jpg',
  }, {
    label: '开始构建',
    url: 'https://img.alicdn.com/imgextra/i2/O1CN017SVpnf1agvgIzKGcu_!!6000000003360-0-tps-1000-288.jpg',
  }, {
    label: '企业云采用',
    url: 'https://img.alicdn.com/imgextra/i3/O1CN01BjpCna1IWFMIPpSgP_!!6000000000900-0-tps-1000-288.jpg',
  }, {
    label: '云卓越架构',
    url: 'https://img.alicdn.com/imgextra/i1/O1CN01STJFYE1I5gNu5Nvtq_!!6000000000842-0-tps-1000-288.jpg',
  },
];

// 节点展示类型，当node_type为8时生效
export enum PAGE_TYPE {
  INDEX = 'index',
  PRODUCT = 'product',
  DOC = 'doc-detail',
}
