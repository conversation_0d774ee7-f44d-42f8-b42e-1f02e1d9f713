// 节点类型
export enum NODE_TYPE {
  DOC = 1, // 普通文档
  KB = 2, // KB文档
  VIDEO = 4, // 视频文档
  DIRECTORY = 8, // 目录
  PRODUCT_NODE = 16, // 产品节点
  CATEGORY_NODE = 32 // 分组节点
}

// 节点展示类型，当node_type为8时生效
export enum SHOW_TYPE {
  DOC_LIST = 1, // 普通目录树节点
  KB_LIST = 2, // KB列表
  VIDEO_LIST = 3, // 视频列表
  SUB_PRODUCT = 4, // 子产品节点
}

export enum DocDetailTypeEnum {
  NORMAL = 0, // 普通文档
  TUTORIAL = 1, // 教程文档
  SOLUTION = 2, // 解决方案文档
  PURE = 3, // 纯净模式文档
  SOLUTION_DETAIL = 4, // 解决方案详情页
}

export const DocIdList = [25378];

// 默认目录节点展开配置
interface IExpandConfig {
  // key: productId
  // value: nodeId[]
  [key: string]: number[];
}
export const defaultMenuExpandConfig: IExpandConfig = {
  // 百炼
  2400256: [
    2713044,
    2587460,
  ],
  // 视频直播
  29949: [
    2852838,
  ],
  // 费用与成本
  100369: [
    2400393,
    428752,
  ],
  // 云数据库 SelectDB
  2503500: [
    2504495,
  ],
  // 云原生多模数据库 Lindorm
  172543: [
    2579359,
  ],
};
