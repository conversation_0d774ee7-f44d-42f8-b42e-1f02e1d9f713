import React, { FunctionComponent } from 'react';
import classnames from 'classnames';

export const enum EArrowType {
  UP = 0,
  DOWN = 1
}

interface IProps {
  type: EArrowType;
}

const UpdownArrow: FunctionComponent<IProps> = ({ type }) => {
  return (
    <>
      {
        type === EArrowType?.UP ?
          <i className={classnames(['smallFont', 'normalFont'], 'help-iconfont help-icon-yijizhankai')} /> :
          <i className={classnames(['smallFont', 'normalFont'], 'help-iconfont help-icon-yijishouqi')} />
      }
    </>
  );
};
export default UpdownArrow;
