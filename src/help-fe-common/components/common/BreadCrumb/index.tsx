import React from 'react';
import { useHistory } from 'ice';
import { FormattedMessage } from 'react-intl';
import { aLinkTrigger } from '@/help-fe-common/utils/global/url/aLinkTrigger';
import { getIndexUrl } from '@/help-fe-common/utils/global/website';
import { generateUrl } from '@/help-fe-common/utils/global/url/generateUrl';
import styles from './index.module.scss';

export default ({ data, style }: any) => {
  const history = useHistory();
  const lastCrumbIndex = data?.length - 1;

  return (
    <div className={styles.breadcrumb} style={style}>
      <div className="help-header-breadcrumb">
        <a
          href={getIndexUrl()}
          className={styles.crumb}
          onClick={(e) => aLinkTrigger(e, history)}
        >
          <FormattedMessage id="help.doc.crumb.homePage" />
        </a>
        {
          data?.map((item, index) => (
            <span key={index}>
              <span className={styles.arrow}>
                <i className="help-iconfont help-icon-right-arrow" />
              </span>
              {index < lastCrumbIndex ?
                <a
                  href={item?.url || generateUrl(item)}
                  onClick={(e) => aLinkTrigger(e, history)}
                  className={styles.crumb}
                >
                  {item.title}
                </a> :
                <span>{item.title}</span>
              }
            </span>
          ))
        }
      </div>
    </div>
  );
};
