@import '../../../styles/variables.scss';

.breadcrumb {
  line-height: 18px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  font-size: 12px;

  span {
    color: $text-second-color;
  }

  .arrow {
    padding: 0 4px;

    i {
      font-size: 12px;
    }
  }

  a {
    color: $text-color;
    cursor: pointer;

    &:hover {
      text-decoration: none;
      color: $text-link-color;
    }
  }
}