import React from 'react';
import { IntlProvider } from 'react-intl';

// 引入 locale 配置文件
import localeEnUS from '@/help-fe-common/locales/en_US';
import localeZhCN from '@/help-fe-common/locales/zh_CN';
import localeZhTC from '@/help-fe-common/locales/zh_TC';
import localeJaJP from '@/help-fe-common/locales/ja_JP';

const localeInfo = {
  zh: localeZhCN,
  en: localeEnUS,
  tc: localeZhTC,
  ja: localeJaJP,
};

interface IProps {
  locale: string;
  children: any;
}

export default (props: IProps) => {
  const { locale, children } = props;

  const myLocale = localeInfo[locale]
    ? localeInfo[locale]
    : localeInfo['zh'];

  return (
    <IntlProvider locale="zh" defaultLocale="zh" messages={myLocale}>
      {React.Children.only(children)}
    </IntlProvider>
  );
};
