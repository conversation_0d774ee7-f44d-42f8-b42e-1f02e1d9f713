import React from 'react';
import * as _ from 'lodash';
import { FormattedMessage } from 'react-intl';
import isMobile from '@/help-fe-common/utils/global/isMobile';
import { isCN } from '@/help-fe-common/utils/global/website';
import pcStyles from './index.module.scss';
import mobileStyles from './mobile.module.scss';

const styles = isMobile() ? mobileStyles : pcStyles;

const statusCode = window?.globalData?.statusCode;
// const errorType = window?.globalData?.errorType;

const tipObj = {
  404: {
    title: 'help.notFound.title',
    des: 'help.notFound.des',
  },
  401: {
    title: 'help.login.title',
    des: 'help.login.des',
  },
  403: {
    title: 'help.forbidden.title',
    des: 'help.forbidden.des',
  },
};

export default () => {
  const { title, des } = _.get(tipObj, statusCode, {});

  return (
    <div className={styles.pageDialog}>
      <div className={styles.notFound}>
        <div className={styles.notFoundImg}>
          <img src="https://img.alicdn.com/imgextra/i3/O1CN01CTCbPE1tU539xMNY3_!!6000000005904-2-tps-590-408.png" alt="" className={styles.img} />
        </div>
        <div className={styles.notFoundText}>
          <div className={styles.title}>
            <FormattedMessage id={title} />
          </div>
          <div className={styles.des}>
            <FormattedMessage id={des} />
          </div>
          <ul>
            <li>
              <a href={isCN ? '/' : '/help'}>- <FormattedMessage id="help.index.helpDoc" /></a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};
