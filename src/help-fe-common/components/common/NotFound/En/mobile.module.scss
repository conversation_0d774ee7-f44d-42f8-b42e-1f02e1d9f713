.pageDialog {
  position: relative;
  height: calc(100vh - 2.55rem);
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;

  .notFound {
    .notFoundImg {
      border: 0;

      img {
        width: 2.95rem;
        height: 2.04rem;
      }
    }

    .notFoundText {
      display: flex;
      flex-direction: column;
      justify-content: center;
      text-align: start;
      font-size: 0.13rem;
      color: #3a3a3a;
      padding-left: 0.6rem;

      .title {
        font-size: 0.15rem;
        height: 0.2rem;
        line-height: 0.2rem;
        font-weight: 600;
        margin-bottom: 0.08rem;
      }

      .des {
        height: 0.17rem;
        line-height: 0.17rem;
        margin-bottom: 0.18rem;
      }

      ul {
        list-style: none;
        margin: 0;
        padding: 0;

        li>a {
          color: #ff6a00;
          font-size: 0.12rem;
          height: 0.24rem;
          line-height: 0.24rem;
          text-decoration: none;
          font-weight: 500;
        }
      }
    }
  }
}