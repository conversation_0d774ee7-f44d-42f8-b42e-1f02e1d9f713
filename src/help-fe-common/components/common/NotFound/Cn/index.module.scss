$largeMedia: 'only screen and (min-width: 1969px)';
$mobile: 'only screen and (max-width: 1055px)';

$largePadding: calc((100vw - 1920px) / 2);

@mixin oneLine {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.pageDialog {
  overflow: hidden;

  .notFoundContainer {
    overflow: hidden;
    position: relative;
    background: #f5f5f6 url("https://img.alicdn.com/imgextra/i1/O1CN013Nb0ls1mqneuncI3l_!!6000000005006-0-tps-3840-480.jpg") 50% / 1920px auto no-repeat;

    @media #{$mobile} {
      background: #f5f5f6 url("https://img.alicdn.com/imgextra/i2/O1CN01LwWI1N1SEybUd79A0_!!6000000002216-0-tps-3840-480.jpg") 50% / cover no-repeat;
    }

    &:before {
      content: '';
      position: absolute;
      display: block;
      height: 100%;
      top: 0;
      left: $largePadding;
      width: 300px;
      background: linear-gradient(90deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
    }

    &:after {
      content: '';
      position: absolute;
      display: block;
      height: 100%;
      top: 0;
      right: $largePadding;
      width: 300px;
      background: linear-gradient(-90deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
    }

    .innerContext {
      padding: 38px 24px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;

      @media #{$mobile} {
        padding: 22px 16px 16px;
      }

      .title {
        font-weight: 500;
        font-size: 44px;
        color: #181818;
        letter-spacing: 0;
        line-height: 48px;

        @media #{$mobile} {
          font-weight: 600;
          font-size: 26px;
          color: #262626;
          letter-spacing: 0;
          line-height: 42px;
        }
      }

      .desc {
        font-weight: 400;
        font-size: 16px;
        color: #181818;
        letter-spacing: 0;
        line-height: 28px;
        margin-top: 16px;

        @media #{$mobile} {
          margin-top: 10px;
          font-weight: 400;
          font-size: 14px;
          color: #181818;
          line-height: 22px;
        }
      }

      .actions {
        font-size: 14px;
        height: 44px;
        line-height: 44px;
        margin-top: 28px;

        @media #{$mobile} {
          height: 38px;
          line-height: 38px;
        }

        .primaryBtn {
          display: inline-block;
          font-weight: 700;
          padding: 0 52px;
          color: #fff;
          background-color: #1366ec;

          &:hover {
            background-color: #0f52bd;
          }

          @media #{$mobile} {
            height: 38px;
            line-height: 38px;
            padding: 0;
            text-align: center;
            width: 100%;
          }
        }
      }
    }
  }

  .recommendHeader {
    border-bottom: 1px solid #e9e9e9;

    .recommendTitle {
      font-weight: 600;
      font-size: 34px;
      color: #181818;
      line-height: 58px;
      padding: 40px 24px;

      @media #{$mobile} {
        padding: 20px 16px;
        font-weight: 600;
        font-size: 24px;
        color: #262626;
        line-height: 37px;
      }
    }
  }

  .content {
    display: flex;
    flex-wrap: wrap;
    background-color: #ffffff;
    padding-bottom: 40px;

    @media #{$mobile} {
      padding-bottom: 20px;
    }

    .recommendChannel {
      border-bottom: 1px solid #e9e9e9;
      width: 50%;

      @media #{$mobile} {
        width: 100%;
      }

      .innerContext {
        padding: 26px 0 26px 24px;

        @media #{$mobile} {
          padding: 20px 0;
        }

        .secondTitle {
          font-weight: 600;
          font-size: 14px;
          color: #181818;
          line-height: 24px;
          margin: 0;

          @media #{$mobile} {
            padding: 0 16px;
          }
        }

        .channels {
          display: flex;
          flex-wrap: wrap;
          margin-top: 8px;

          @media #{$mobile} {
            margin-top: 0;
          }

          .channelItem {
            width: 50%;
            padding-right: 24px;
            padding-top: 20px;
            @include oneLine;

            @media #{$mobile} {
              padding: 20px 16px 0;
            }

            .channelLink {
              font-weight: 400;
              font-size: 14px;
              color: #181818;
              letter-spacing: 0;
              line-height: 24px;

              &:hover {
                color: #1366ec;
              }
            }
          }
        }
      }
    }

    .recommendProducts {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;
      align-content: flex-start;
      width: 50%;
      border-left: 1px solid #e9e9e9;

      @media #{$mobile} {
        width: 100%;
        border-left: 0;
      }

      .productItem {
        display: block;
        width: 50%;
        padding: 24px;
        height: fit-content;
        border-bottom: 1px solid #e9e9e9;

        @media #{$mobile} {
          padding: 20px 16px;
        }

        &:nth-child(odd) {
          border-right: 1px solid #e9e9e9;
        }

        &:nth-child(even) {
          width: calc(50% + 24px);
          margin-right: -24px;

          @media #{$largeMedia} {
            width: calc(50% + ((100vw - 1920px) / 2));
            margin-right: calc((1920px - 100vw) / 2);
          }

          @media #{$mobile} {
            width: 50%;
          }
        }

        &:hover {
          background-color: #F5F5F6;

          .productTitle {
            color: #1366ec;
          }
        }

        .productTitle {
          font-weight: 500;
          font-size: 14px;
          color: #181818;
          line-height: 24px;
          display: block;
          @include oneLine;
        }

        .productDesc {
          font-weight: 300;
          font-size: 14px;
          color: #8C8C8C;
          letter-spacing: 0;
          line-height: 24px;
          display: block;
          @include oneLine;
        }
      }
    }
  }
}
