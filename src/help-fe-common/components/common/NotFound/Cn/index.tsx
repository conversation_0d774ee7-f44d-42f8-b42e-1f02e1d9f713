import React, { useEffect } from 'react';
import { useRequest } from 'ice';
import * as _ from 'lodash';
import { FormattedMessage } from 'react-intl';
import Wrapper, { EDirectGroup } from '../Wrapper';
import NotFoundService from '@/services/notFound';
import Loading from '@/help-fe-common/components/common/Loading';
import { isCN } from '@/help-fe-common/utils/global/website';
import { handleGlobalStyle } from '@/help-fe-common/utils/global/style/handleStyle';
import styles from './index.module.scss';

const statusCode = window?.globalData?.statusCode;

const tipObj = {
  404: {
    title: 'help.notFound.title',
    des: 'help.notFound.des',
  },
  401: {
    title: 'help.login.title',
    des: 'help.login.des',
  },
  403: {
    title: 'help.forbidden.title',
    des: 'help.forbidden.des',
  },
  500: {
    title: 'help.serverError.title',
    des: 'help.serverError.des',
  },
};

const extraClassList = ['.help-body-head', '.aliyun-docs-menu'];

export default ({ responseCode }) => {
  const {
    title,
    des,
  } = _.get(tipObj, responseCode || statusCode) || {};

  const { loading, data: recommendData } = useRequest(NotFoundService.getNotFoundRecommend, { manual: false });

  useEffect(() => {
    if (responseCode) {
      handleGlobalStyle(extraClassList, false);
    }
    return () => {
      handleGlobalStyle(extraClassList, true);
    };
  }, [responseCode]);

  return (
    <div className={styles.pageDialog}>
      <Wrapper className={styles.notFoundContainer}>
        <div className={styles.innerContext}>
          {
            Boolean(title) && (
              <div className={styles.title}>
                <FormattedMessage id={title} />
              </div>)
          }
          {
            Boolean(des) && (
              <div className={styles.desc}>
                <FormattedMessage id={des} />
              </div>)
          }
          <div className={styles.actions}>
            <a href={isCN ? '/' : '/help'} className={styles.primaryBtn}>
              <FormattedMessage id="help.redirect.home" />
            </a>
          </div>
        </div>
      </Wrapper>
      <Wrapper className={styles.recommendHeader}>
        <header className={styles.recommendTitle}>
          <FormattedMessage id="help.recommend.page" />
        </header>
      </Wrapper>
      <Loading loading={loading} />
      <section className={styles.content}>
        <Wrapper directions={[EDirectGroup.LEFT]} className={styles.recommendChannel}>
          <div className={styles.innerContext}>
            <h3 className={styles.secondTitle}>
              <FormattedMessage id="help.recommend.mainChannel" />
            </h3>
            <ul className={styles.channels}>
              {
                Array.isArray(recommendData?.channels) && recommendData?.channels?.map?.((item) => (
                  <li className={styles.channelItem} key={item.url}>
                    <a
                      href={item.url}
                      className={styles.channelLink}
                      target="_blank"
                      rel="noreferrer"
                    >{item.title}
                    </a>
                  </li>
                ))
              }
            </ul>
          </div>
        </Wrapper>
        <Wrapper directions={[EDirectGroup.RIGHT]} className={styles.recommendProducts}>
          {
            Array.isArray(recommendData?.products) && recommendData?.products?.map((item) => (
              <a
                className={styles.productItem}
                href={item.url}
                key={item.url}
                target="_blank"
                rel="noreferrer"
              >
                <h5 className={styles.productTitle}>{item.title}</h5>
                <span className={styles.productDesc}>{item.desc}</span>
              </a>
            ))
          }
        </Wrapper>
      </section>
    </div>
  );
};
