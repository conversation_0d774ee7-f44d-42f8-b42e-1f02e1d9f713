$largeMedia: 'only screen and (min-width: 1969px)';
$mobile: 'only screen and (max-width: 1055px)';

$largeMediaPadding: calc((100vw - 1920px) / 2);

.wrapperLeft {
  padding-left: 24px;

  @media #{$largeMedia} {
    padding-left: $largeMediaPadding;
  }

  @media #{$mobile} {
    padding-left: 0;
  }
}

.wrapperRight {
  padding-right: 24px;

  @media #{$largeMedia} {
    padding-right: $largeMediaPadding;
  }

  @media #{$mobile} {
    padding-right: 0;
  }
}
