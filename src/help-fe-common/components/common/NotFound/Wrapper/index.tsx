import React, { FunctionComponent } from 'react';
import classNames from 'classnames';
import styles from './index.module.scss';

export enum EDirectGroup {
  LEFT = 'left',
  RIGHT = 'right',
}

interface IProps {
  className?: string;
  children?: React.ReactNode;
  directions?: EDirectGroup[];
}

/**
 * 公共布局组件
 * @param props
 * @returns
 */
const Index: FunctionComponent<IProps> = (props) => {
  const {
    children,
    className,
    directions = [EDirectGroup.LEFT, EDirectGroup.RIGHT],
  } = props;

  return (
    <div
      className={classNames(
        directions.includes(EDirectGroup.LEFT) ? styles.wrapperLeft : null,
        directions.includes(EDirectGroup.RIGHT) ? styles.wrapperRight : null,
        className,
      )}
    >
      {children}
    </div>
  );
};

export default Index;
