import React from 'react';
import { FormattedMessage } from 'react-intl';
import styles from './index.module.scss';

const NoResult = ({ tip }) => {
  return (
    <div className={styles.container}>
      <div className={styles.tipContainer}>
        <img src="https://img.alicdn.com/imgextra/i2/O1CN01NeFfBT1ykBG7JSQK6_!!6000000006616-2-tps-240-240.png" alt="No result!" />
        <p className={styles.tip}>
          {tip || <FormattedMessage id="help.solution.solutionList.noData" />}
        </p>
      </div>
    </div>
  );
};

export default NoResult;
