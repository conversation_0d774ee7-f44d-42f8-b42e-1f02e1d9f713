$mobile: 1055px;

.allProducts {
  width: 60vw;
  height: 100%;
  background-color: #fff;
  overflow-y: auto;
  overflow-x: hidden;
  overscroll-behavior: none;
  border-left: 1px solid #e3e3e3;

  .allProductsHead {
    width: 100%;
    padding: 14px 30px 20px 30px;

    display: flex;
    justify-content: space-between;
    align-items: center;

    >.allProductsSearchWrapper {
      position: relative;
      width: 100%;
      height: 36px;

      &>.allProductsSearch {
        display: flex;
        align-items: center;
        height: 100%;
        border-bottom: 1px solid #d7d8d9;
        padding: 6px 0;

        &:focus,
        &:hover {
          border-bottom: 1px solid #1366ec;

          .searchIcon {
            i {
              color: #1366ec;
            }
          }
        }

        .searchIcon {
          margin-right: 10px;

          i {
            color: #9b9ea0;
          }
        }

        input {
          flex: 1;
          font-size: 12px;
          color: #373d41;
          height: 100%;
          line-height: 30px;
          outline: none;
          border: none;
        }

        &>.allProductsSearchResult {
          position: absolute;
          display: none;
          width: 100%;
          max-height: 380px;
          left: 0;
          top: 42px;
          background-color: #fff;
          border: 1px solid #d7d8d9;
          box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1);
          overflow-y: auto;

          &>li {
            width: 100%;
            height: 42px;
            line-height: 42px;

            &>a {
              display: inline-block;
              width: 100%;
              height: 100%;
              padding-left: 15px;
              text-decoration: none;
              font-size: 14px;
              color: #373d41;
            }

            &.current {
              background-color: #f7f7f7;

              &>a {
                color: #1366ec;
              }
            }
          }

          &::-webkit-scrollbar {
            width: 4px;
          }

          &::-webkit-scrollbar-thumb {
            background-color: #d7d8d9;
          }
        }
      }
    }
  }

  .allProductsBody {
    width: 100%;
    height: auto;
    padding: 0 32px 10px 32px;
    font-size: 0;
    column-count: 3;
    column-gap: 40px;

    .allProductsBodyBlock {
      display: inline-block;
      width: 100%;
      height: auto;
      margin: 0 40px 8px 0;
      vertical-align: top;
      -moz-break-inside: avoid;
      -webkit-break-inside: avoid;
      break-inside: avoid;

      &>span {
        display: inline-block;
        width: 100%;
        height: 32px;
        line-height: 32px;
        color: #181818;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 8px;
      }

      .subContainer {
        .subNodeTitle {
          height: 32px;
          line-height: 32px;
          font-weight: 500;
          font-size: 12px;
          color: #BDBDBD;
        }

        .subNodeContent {
          margin: 0 0 8px;

          &>li {
            table-layout: fixed;
            word-break: break-word;
            overflow: hidden;
          }
        }


      }

      .subNodeContent,
      .subNodeContent2 {
        & a {
          width: 100%;
          font-size: 12px;
          color: #181818;
          line-height: 30px;
          text-decoration: none;

          &:hover {
            color: #1366ec;
          }
        }
      }
    }
  }

  &::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #d7d8d9;
  }
}

@media screen and (max-width: 1200px) {
  .allProducts {
    width: calc(100vw - 320px);
  }
}

//移动端
@media screen and (max-width:$mobile) {
  .allProducts {
    width: 100%;

    .allProductsBody {
      column-count: 2;
    }
  }
}