import React, { useEffect, useState, FunctionComponent } from 'react';
import classnames from 'classnames';
import services from '@/help-fe-common/services';
import { useIntl } from 'react-intl';
import { useHistory, useRequest } from 'ice';
import { getIndexFilterProductList } from '@/help-fe-common/utils/helpDoc/filterList';
import { aLinkTrigger } from '@/help-fe-common/utils/global/url/aLinkTrigger';
import { NODE_TYPE } from '@/help-fe-common/constants/detail';
import styles from './index.module.scss';

interface IProps {
  onMouseEnter?: () => void;
  onMouseLeave?: (e: any, className?: string) => void;
  style?: React.CSSProperties;
}

const ProductList: FunctionComponent<IProps> = ({ onMouseEnter, onMouseLeave, style }) => {
  const intl = useIntl();
  const { data, request } = useRequest<any>(services.getProductList);
  const [filteredResult, setFilteredResult] = useState<any[]>([]);
  const history = useHistory();

  const onLinkClick = (e) => {
    aLinkTrigger(e, history);
    // 额外执行关闭逻辑
    onMouseLeave?.(e, 'help-menu-product-list');
  };

  useEffect(() => {
    request({});
  }, []);

  useEffect(() => {
    if (data) {
      setFilteredResult(data?.children?.filter((val) => val?.children?.length > 0));
    }
  }, [data]);

  const onInputChange = (e) => {
    const value = e?.target?.value;
    const filtered = getIndexFilterProductList(value, data?.children);
    setFilteredResult(filtered);
  };


  const renderSubNode = (subNodeData) => {
    return subNodeData?.map((subNodeItem, index) => (
      subNodeItem?.children?.length > 0 && subNodeItem.nodeType === NODE_TYPE.CATEGORY_NODE ?
        <div className={styles.subContainer} key={index}>
          <span className={styles.subNodeTitle}>{subNodeItem?.title}</span>
          <ul className={styles.subNodeContent}>
            {
              subNodeItem?.children?.map((child, childIndex) => {
                return (
                  <li key={childIndex}>
                    <a
                      href={child?.url ? child?.url : `/product/${child?.id}.html`}
                      onClick={onLinkClick}
                    >
                      {child.title}
                    </a>
                  </li>
                );
              })
            }
          </ul>
        </div>
        :
        <div className={styles.subNodeContent2} key={index}>
          {renderNode(subNodeItem)}
        </div>
    ));
  };

  const renderNode = (productNodeData) => {
    return (
      productNodeData?.nodeType === NODE_TYPE.PRODUCT_NODE ?
        <a
          href={productNodeData?.url ? productNodeData?.url : `/product/${productNodeData?.id}.html`}
          onClick={onLinkClick}
        >
          {productNodeData.title}
        </a>
        : null);
  };

  return (
    <div
      className={classnames(styles.allProducts)}
      style={style}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <div className={styles.allProductsHead}>
        <div className={styles.allProductsSearchWrapper}>
          <div className={styles.allProductsSearch}>
            <span className={styles.searchIcon}>
              <i className="help-iconfont help-icon-sousuoicon" />
            </span>
            <input
              placeholder={intl.formatMessage({ id: 'help.search.placeholder' })}
              onChange={onInputChange}
            />
          </div>
        </div>
      </div>
      <div className={styles.allProductsBody}>
        {
          filteredResult?.map((item, index) => {
            return (
              <div key={index} className={styles.allProductsBodyBlock}>
                <span>{item?.title}</span>
                {renderSubNode(item?.children)}
              </div>
            );
          })
        }
      </div>
    </div>
  );
};

export default ProductList;
