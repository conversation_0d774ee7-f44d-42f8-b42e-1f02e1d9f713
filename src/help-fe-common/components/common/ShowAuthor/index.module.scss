@import '../../../styles/variables.scss';

.authorName {
  color: $text-gray-color;
  padding-right: 10px;
  position: relative;

  >p {
    display: inline-block;
    font-size: inherit;
  }

  .bubble {
    width: 180px;
    margin-bottom: 10px;
    background-color: #fff;
    box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.10);
    position: absolute;
    bottom: 18px;
    right: -122px;

    .content {
      width: 100%;
      max-height: 110px;
      padding: 16px;
      line-height: 20px;
      color: #474a52;
      font-size: 12px;
    }

    p {
      font-size: inherit;
    }

    &::after {
      content: '';
      width: 0;
      height: 0;
      border: 8px solid transparent;
      border-top: 8px solid #fff;
      color: #000;
      overflow: hidden;
      pointer-events: none;
      position: absolute;
      left: 20px;
      bottom: -16px;
    }
  }
}