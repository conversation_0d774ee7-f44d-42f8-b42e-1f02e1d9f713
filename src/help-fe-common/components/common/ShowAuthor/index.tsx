import React, { useEffect, useRef, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import classNames from 'classnames';
import styles from './index.module.scss';
import { getLocale } from '@/help-fe-common/utils/global/locale';

const locale = getLocale();

// 作者文案配置
const authorTextConfig = {
  isZh: {
    authorChar: ' 等',
    authorUnit: ' 人',
    joinChar: '、',
  },
  notZh: {
    authorChar: ', ',
    authorUnit: '',
    joinChar: ' and ',
  },
};

/**
 * 根据作者列表返回渲染节点
 * @param {Array} authorNameList 作者列表
 * @returns {ReactNode} 作者dom节点
 */
const renderTextNode = function (authorNameList: any[]) {
  const authorConfig = locale === 'zh' ? authorTextConfig.isZh : authorTextConfig.notZh;
  const { authorChar, authorUnit, joinChar } = authorConfig;
  if (authorNameList?.length <= 2) {
    return <span>{authorNameList?.join(joinChar)}</span>;
  } else {
    return (
      <span>
        {authorNameList?.slice(0, 2)?.join(joinChar)}
        {authorChar}
        <span className="omit" style={{ color: '#1366ec', cursor: 'pointer' }}> {locale === 'zh' ? `${authorNameList?.length}` : 'etc.'}</span>
        {authorUnit}
      </span>
    );
  }
};


export default ({ authors }) => {
  const intl = useIntl();
  const [showBubble, setShowBubble] = useState(false);
  const bubbleRef = useRef<any>(null);
  const timeoutRef = useRef<any>();
  const [cnNames, setCnNames] = useState([]) as any;

  const generateAuthors = () => {
    let nameArr = [] as any;
    Array.isArray(authors) && (nameArr = authors?.map((author) => {
      return author.name;
    }).filter((name) => { return !!name; }));
    setCnNames(nameArr);
  };

  useEffect(generateAuthors, [authors]);

  useEffect(() => {
    if (cnNames < 2) return;
    const omitRef = document.querySelector('.omit') as any;
    omitRef?.addEventListener('mouseleave', leaveBubbleEvent);
    omitRef?.addEventListener('mouseover', hoverBubbleEvent);
    omitRef?.addEventListener('click', hoverBubbleEvent);
    return () => {
      clearTimeout(timeoutRef.current);
    };
  }, [showBubble, cnNames]);


  const hoverBubbleEvent = () => {
    clearTimeout(timeoutRef.current);
    timeoutRef.current = null;
    setShowBubble(true);
  };

  const leaveBubbleEvent = () => {
    if (showBubble && !timeoutRef.current) {
      timeoutRef.current = window.setTimeout(() => {
        setShowBubble(false);
      }, 100);
    }
  };

  return (
    <div className={styles.authorName}>
      { showBubble &&
        <div
          className={styles.bubble}
          ref={bubbleRef}
          onMouseOver={hoverBubbleEvent}
          onMouseLeave={leaveBubbleEvent}
        >
          <div className={classNames('code-scroll-thumb', styles.content)}>
            <FormattedMessage id="help.doc.authorName" />
            <p>{cnNames.join(`${intl.formatMessage({ id: 'help.doc.joinCharMore' })}`)}</p>
          </div>
        </div>
      }
      <div>
        <FormattedMessage id="help.doc.authorName" />
        {renderTextNode(cnNames)}
      </div>
    </div>
  );
};
