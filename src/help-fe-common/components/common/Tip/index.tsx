import React from 'react';
import { FormattedMessage } from 'react-intl';
import styles from './index.module.scss';

export default ({ onTipClick }) => {
  return (
    <div className={styles.helpTip} >
      <div className={styles.tipContainer} >
        <div className={styles.left}>
          <span className={styles.tipIcon}>
            <i className="help-iconfont help-icon-shangshouneirongicon" />
          </span>
          <p className={styles.tip}>
            <FormattedMessage id="help.doc.backup.tip" />
          </p>
        </div>
        <span className={styles.tipDelete}>
          <i className="help-iconfont help-icon-delete" onClick={onTipClick} />
        </span>
      </div>

    </div>
  );
};
