.helpTip {
  width: 100%;
  background: #fff9f3;
  box-shadow: 0 0 5px 0 rgba(55, 61, 65, 0.30);
  padding: 0 20px;
  position: absolute;
  top: 0;
  z-index: 600;

  .tipContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 30px;

    .left {
      display: flex;
      align-items: center;

      .tipIcon {
        color: #ff6a00;
        margin-right: 10px;

        i {
          font-size: 20px;
        }
      }

      .tip {
        font-size: 14px;
        line-height: 14px;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }


    .tipDelete {
      cursor: pointer;

      i {
        font-size: 12px;
      }

      &:hover {
        color: #ff6a00;
      }
    }
  }
}