import React, { useState } from 'react';
import classNames from 'classnames';
import FeedbackToolTip from '@/help-fe-common/components/common/Feedback/FeedbackToolTip';
import { isServer } from '@/help-fe-common/utils/node/getContext';
import { hideSelection } from '@/help-fe-common/utils/helpDoc/selection';
import { EQuestionFrom, onAIClick } from '@/help-fe-common/utils/helpDoc/triggerAI';
import { selectionTooltipId } from '@/help-fe-common/constants';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import styles from './index.module.scss';

export default ({ identifier, selectionInfo, showAIassistant, prompt, selectionDomStyle }) => {
  const [showDialog, setShowDialog] = useState(false);

  const onFeedbackClick = (e) => {
    e.stopPropagation();
    setShowDialog(true);
    hideSelection();
    // 埋点
    sendSlsLog({ page: 'documentDetail', section: 'selection', action: 'feedbackClick' });
  };

  if (isServer()) {
    return (
      null
    );
  }

  return (
    <div>
      {/* 划词选区 */}
      <div id={selectionTooltipId} className={styles.selectionTooltip} style={selectionDomStyle}>
        <div className={classNames(styles.box, styles.feedbackBox)} onClick={onFeedbackClick}>
          <i className="help-iconfont help-icon-bianji" />
        </div>
        {
          showAIassistant &&
          <div
            className={classNames(styles.box, styles.aiBox)}
            onClick={() => {
              onAIClick(EQuestionFrom.UNDERLINE, selectionInfo, prompt);
            }}
          >
            <i className={styles.aiIcon} />
          </div>
        }
      </div>
      <FeedbackToolTip identifier={identifier} showDialog={showDialog} setShowDialog={setShowDialog} />
    </div>
  );
};
