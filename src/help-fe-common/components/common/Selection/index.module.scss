.selectionTooltip {
  display: flex;
  padding: 0 4px;
  font-size: 12px;
  background-color: #fff;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.10);
  position: absolute;

  .box {
    height: 32px;
    line-height: 30px;
    width: 32px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    &:hover {
      background-color: #F4F4F4;
    }

    &::before {
      height: 28px;
      line-height: 20px;
      padding: 4px 10px;
      font-size: 12px;
      color: #fff;
      background-color: #000;
      position: absolute;
      top: -40px;
      white-space: nowrap;
      opacity: 0;
      transition: opacity 0.2s;
      pointer-events: none;
    }

    &::after {
      content: '';
      width: 0;
      height: 0;
      border: 6px solid transparent;
      border-top: 6px solid #000;
      color: #fff;
      overflow: hidden;
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.2s;
      position: absolute;
      top: -12px;
      left: 10px;
    }

    .aiIcon {
      width: 16px;
      height: 16px;
      background-image: url(https://img.alicdn.com/imgextra/i2/O1CN01XJLOkl1qHlpgFV2QD_!!6000000005471-2-tps-512-512.png);
      background-size: cover;
    }

    i {
      font-size: 18px;
    }
  }

  .feedbackBox {
    &::before {
      content: '文档反馈';
      left: -15px;
    }

    &:hover {

      &::before,
      &::after {
        opacity: 1;
      }
    }
  }

  .aiBox {
    &::before {
      content: '咨询 AI 助理';
      left: -20px;
    }

    &:hover {

      &::before,
      &::after {
        opacity: 1;
      }
    }
  }
}
