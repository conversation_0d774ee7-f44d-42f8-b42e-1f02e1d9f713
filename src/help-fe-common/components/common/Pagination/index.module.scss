.paginationContainer {
  margin-top: 30px;
  margin-bottom: 40px;
  padding-right: 180px;
  position: relative;
  text-align: right;

  .totalCount {
    right: 0;
    position: absolute;
    top: 8px;

    span {
      margin: 0 10px;
    }
  }

  .list {
    display: inline-block;

    >li {
      height: 30px;
      line-height: 30px;
      border: 1px solid #d7d8d9;
      display: inline-block;
      float: left;
      font-size: 12px;
      text-align: center;
      margin-left: -1px;
      color: #333;
      padding: 0 10px;
      cursor: pointer;
      margin: 0 4px;
    }

    li:hover {
      color: #fff;
      border: 1px solid #1366ec;
      background-color: #1366ec;
    }

    .currentPage {
      color: #fff;
      border: 1px solid #1366ec;
      background-color: #1366ec;
    }

    .disabled {
      background: #efefef;
      border-color: #efefef;
      color: #ccc;
      cursor: default;
    }

    .disabled:hover {
      background: #efefef;
      border-color: #efefef;
      color: #ccc;
      cursor: default;
    }
  }

  .changeNum {
    display: inline-block;
    position: absolute;
    margin-left: 5px;

    .jumpInput {
      height: 30px;
      width: 40px;
      text-align: center;
      border: 1px solid #d7d8d9;
      margin: 0px 5px;
    }

    .go {
      display: inline-block;
      height: 30px;
      line-height: 30px;
      padding: 0 10px;
      text-align: center;
      border: 1px solid #d7d8d9;
      cursor: pointer;
    }
  }

}