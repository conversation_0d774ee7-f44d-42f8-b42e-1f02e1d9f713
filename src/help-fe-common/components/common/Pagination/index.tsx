import React, { useMemo, useState } from 'react';
import { FormattedMessage } from 'react-intl';
import styles from './index.module.scss';

interface IProps {
  totalPage: number;// 分页总页数
  totalCount: number;// 总数
  pageNum: number;// 分页当前页码
  pageSize?: number;// 单页显示数量
  currentSize: number;// 当前显示数量
  onChange: (val: number) => void;
}

export default ({
  totalPage,
  totalCount,
  pageNum,
  pageSize,
  currentSize,
  onChange,
}: IProps) => {
  const [currentPage, setCurrentPage] = useState(pageNum);
  const [defaultPageNum, setDefaultPageNum] = useState(5); // 默认显示的页码
  const [startPageNum, setStartPageNum] = useState(1); // 默认开始页码
  const [jumpBtnNum, setJumpBtnNum] = useState<Number>();

  const changePageNum = (num) => {
    setCurrentPage(num);
    // 当前页码>defaultPageNum重新设置起始页码
    if (num >= defaultPageNum) {
      setStartPageNum(num - 2);
    } else {
      setStartPageNum(1);
    }
    onChange(num);
  };

  const createPagination = (current) => {
    const pages: any = [];
    // 上一页
    // totalPage < 10 全部显示
    if (totalPage < 10) {
      for (let i = 1; i <= totalPage; i++) {
        pages.push(<li key={i} className={current === i ? styles.currentPage : ''} onClick={() => changePageNum(i)}>{i}</li>);
      }
    } else {
      // totalPage大于10显示形式为：12345...10，6-9隐藏
      // 第一页一直显示
      pages.push(<li key={1} className={current === 1 ? styles.currentPage : ''} onClick={() => changePageNum(1)}>1</li>);
      let pageLength = 0;
      if (defaultPageNum + startPageNum > totalPage) {
        pageLength = totalPage;
      } else {
        pageLength = defaultPageNum + startPageNum;
      }
      if (current >= defaultPageNum) {
        pages.push(<li key={startPageNum - 1}>...</li>);
      }
      // page添加分组内容
      for (let i = startPageNum; i < pageLength; i++) {
        if (i <= totalPage - 1 && i > 1) {
          pages.push(<li key={i} className={current === i ? styles.currentPage : ''} onClick={() => changePageNum(i)}>{i}</li>);
        }
      }
      // 倒数第二位展示页数/...
      if (totalPage - startPageNum >= defaultPageNum + 1) {
        pages.push(<li key={totalPage - 1}>...</li>);
      }
      // 最后一页一直显示
      pages.push(
        <li key={totalPage} className={current === totalPage ? styles.currentPage : ''} onClick={() => changePageNum(totalPage)}>{totalPage}</li>,
      );
    }

    return pages;
  };

  const changeCurrentPageNum = (e) => {
    setJumpBtnNum(e.target.value);
  };
  const listData = useMemo(() => createPagination(currentPage), [currentPage, totalPage]);

  return (
    <div className={styles.paginationContainer}>
      <ul className={styles.list}>
        <li
          className={currentPage === 1 ? styles.disabled : ''}
          onClick={() => (currentPage === 1 ? null : changePageNum(currentPage - 1))}
        >
          <FormattedMessage id="help.common.pagination.pre" />
        </li>
        {listData}
        <li
          className={currentPage === totalPage ? styles.disabled : ''}
          onClick={() => (currentPage === totalPage ? null : changePageNum(currentPage + 1))}
        >
          <FormattedMessage id="help.common.pagination.next" />
        </li>
      </ul>
      <div className={styles.totalCount}>
        <span>当前{currentSize}条</span>
        <span>总共{totalCount}条</span>
      </div>
      {/* <div className={styles.changeNum}>
        <span><FormattedMessage id="help.common.pagination.jump" /></span>
        <input className={styles.jumpInput} onChange={(e) => changeCurrentPageNum(e)} />
        <span className={styles.go} onClick={() => {
          if (typeof jumpBtnNum === 'number') {
            setCurrentPage(jumpBtnNum);
          }
        }}>GO</span>
      </div> */}
    </div>
  );
};
