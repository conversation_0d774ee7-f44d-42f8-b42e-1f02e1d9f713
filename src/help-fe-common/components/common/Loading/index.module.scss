.loadingOverlay {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 9;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fff;

  .loadingIcon {
    animation-name: spin;
    animation-duration: 0.5s;
    animation-timing-function: linear;
    animation-delay: 0s;
    animation-iteration-count: infinite;
    animation-direction: normal;
    animation-fill-mode: none;
    animation-play-state: running;
    border: 2px solid transparent;
    border-top: 2px solid #1366ec;
    border-left: 2px solid #1366ec;
    border-radius: 50px;
    opacity: 1;
    width: 32px;
    height: 32px;
    cursor: wait;
    display: block;
    transition: all 400ms ease !important;
    animation: spin 0.6s linear infinite;
  }
}

@keyframes spin {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}