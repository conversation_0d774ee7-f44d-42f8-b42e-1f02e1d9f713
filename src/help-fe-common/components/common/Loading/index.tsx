import React from 'react';

import styles from './index.module.scss';

interface IProps {
  loading: boolean;
  style?: React.CSSProperties;
  iconStyle?: React.CSSProperties;
}

export default ({ loading, style, iconStyle }: IProps) => {
  if (!loading) {
    return null;
  }

  return (
    <div className={styles.loadingOverlay} style={style}>
      <i className={styles.loadingIcon} style={iconStyle} />
    </div >
  );
};
