import React, { FunctionComponent } from 'react';
import { getSearchParams } from 'ice';
import { Overlay } from '@alifd/next';
import { useIntl } from 'react-intl';
import NotFound from '@/help-fe-common/components/common/NotFound';
import Logo from '@/help-fe-common/components/common/Logo';
import styles from './index.module.scss';

const LinkIntercept: FunctionComponent = () => {
  const searchParams = getSearchParams();

  const intl = useIntl();

  const onLinkClick = () => {
    location.href = searchParams?.targetUrl as string;
  };

  if (!searchParams?.targetUrl) return <NotFound />;
  return (
    <Overlay
      v2
      visible
      hasMask
      canCloseByEsc={false}
      autoFocus={false}
      target={document.body}
      disableScroll
      container="app"
      align="tc tc"
      wrapperClassName={styles.linkInterceptWrapper}
    >
      <div className={styles.linkIntercept}>
        <a href="https://www.aliyun.com/" target="_blank" className={styles.logo} rel="noreferrer"><Logo /></a>
        <div className={styles.messageContext}>
          <section className={styles.innerContext}>
            <h4 className={styles.title}>
              {intl.formatMessage({ id: 'help.linkIntercept.awaySite' })}
            </h4>
            <p className={styles.desc}>{intl.formatMessage({ id: 'help.linkIntercept.warningTip' })}</p>
            <p className={styles.url}>{searchParams?.targetUrl}</p>
          </section>
          <section className={styles.footerAction}>
            <span className={styles.primaryBtn} onClick={onLinkClick}>
              {intl.formatMessage({ id: 'help.linkIntercept.continueVisit' })}
            </span>
          </section>
        </div>
      </div>
    </Overlay>
  );
};

export default LinkIntercept;
