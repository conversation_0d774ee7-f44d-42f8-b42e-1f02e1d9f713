.linkInterceptWrapper {

  .linkIntercept {
    margin-top: 160px;
    width: 600px;

    .logo {
      margin-bottom: 12px;
      display: inline-block;
    }

    .messageContext {
      background-color: #ffffff;
      border: 1px solid #D4D6DB;
      padding: 24px;

      .innerContext {
        padding-bottom: 16px;
        border-bottom: 1px solid #D4D6DB;

        .title {
          font-weight: 600;
          font-size: 16px;
          color: #1F2024;
          letter-spacing: 0;
          margin-bottom: 12px;
        }

        .desc {
          font-weight: 400;
          font-size: 12px;
          color: #474A52;
          letter-spacing: 0;
          line-height: 16px;
          margin-bottom: 12px;
        }

        .url {
          word-break: break-all;
          font-weight: 400;
          font-size: 12px;
          color: #474A52;
          letter-spacing: 0;
          line-height: 16px;
        }
      }

      .footerAction {
        display: flex;
        justify-content: flex-end;
        align-content: center;
        margin-top: 24px;

        .primaryBtn {
          height: 32px;
          display: inline-block;
          background: #1366EC;
          padding: 0 16px;
          text-align: center;
          line-height: 32px;
          color: #ffffff;

          &:hover {
            cursor: pointer;
          }
        }
      }
    }
  }

  :global {
    .next-overlay-backdrop {
      background-color: rgba(245, 246, 247, 1);
    }
  }
}