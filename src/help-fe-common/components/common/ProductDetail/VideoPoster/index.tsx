import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import React, { FunctionComponent, useRef } from 'react';
import styles from './index.module.scss';

interface IProps {
  nodeId: any;
  videoUrl: any;
  videoTitle: string;
}

const VideoPoster: FunctionComponent<IProps> = ({ nodeId, videoUrl, videoTitle }) => {
  const videoRef = useRef() as any;
  const posterRef = useRef() as any;
  const playVideo = () => {
    posterRef.current.style.display = 'none';
    videoRef.current.play();
  };
  const videoOnPlay = () => {
    sendSlsLog({ page: 'documentDetail', section: 'video', action: 'play', userParams1: nodeId, userParams2: videoUrl });
  };
  const videoOnEnd = () => {
    sendSlsLog({ page: 'documentDetail', section: 'video', action: 'fullPlay', userParams1: nodeId, userParams2: videoUrl });
  };
  return (
    <div className={styles.videoPosterContainer}>
      <div className={styles.imgContainer} onClick={playVideo} ref={posterRef}>
        <i className={styles.playButton} />
        <div className={styles.titleContainer}>
          <p className={styles.title} title={videoTitle}>{videoTitle}</p>
        </div>
      </div>
      <video
        src={videoUrl}
        controls
        controlsList="nodownload"
        ref={videoRef}
        onPlay={videoOnPlay}
        onEnded={videoOnEnd}
      />
    </div>
  );
};
export default VideoPoster;
