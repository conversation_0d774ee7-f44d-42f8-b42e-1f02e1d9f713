@import '../../../../styles/variables.scss';
$scale: 1;

.videoPosterContainer {
  position: relative;
  display: flex;
  align-items: center;
  max-height: 337.5px;
  max-width: 600px;
  aspect-ratio: 16 / 9;
  margin: 16px 2px 24px 2px;
  background-color: #000;
  box-shadow: 0 4px 15px 0 rgba(0, 0, 0, 0.1);
  cursor: pointer;

  video {
    display: block;
  }

  .imgContainer {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background-image: url(https://img.alicdn.com/imgextra/i3/O1CN01p3aUSz1l32w2icR3C_!!6000000004762-2-tps-1204-740.png);
    background-size: cover;
    background-repeat: no-repeat;
    background-color: #fff;

    .mainImg {
      width: 100%;
      height: 100%;
    }

    .titleContainer {
      position: absolute;
      top: 85%;
      left: 5%;
      max-width: 100%;
      display: flex;
      align-items: center;

      .title {
        font-weight: 500;
        font-size: 16px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .playButton {
      position: absolute;
      transform: translate(0, -50%);
      top: 78%;
      right: 20*$scale+px;
      width: 80*$scale+px;
      height: 80*$scale+px;
      background-image: url(https://img.alicdn.com/imgextra/i4/O1CN015USRW31GyywXHOIaZ_!!6000000000692-2-tps-160-160.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    &:hover {
      .playButton {
        background-image: url(https://img.alicdn.com/imgextra/i3/O1CN01zSUB5J1Oyzt0WwDjW_!!6000000001775-2-tps-80-80.png);
      }

      .title {
        color: #1366ec;
      }
    }
  }
}


// 移动端
@media only screen and (max-width: 1055px) {
  .videoPosterContainer {
    height: auto;
    max-width: 100%;

    .imgContainer {

      .playButton {
        width: 40*$scale+px;
        height: 40*$scale+px;
      }
    }
  }
}