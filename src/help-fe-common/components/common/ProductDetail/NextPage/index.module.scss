@import '../../../../styles/variables.scss';

.pagination,
div[class='aliyun-docs-pagination'] {
  height: 60px;

  .container {
    height: 60px;
    padding: 16px 0 24px 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    border-top: 1px solid #d7d8d9;

    .paginationLeft {
      i {
        font-size: 12px;
        margin-right: 8px;
      }
    }

    .paginationRight {
      i {
        font-size: 12px;
        margin-left: 8px;
      }
    }

    .paginationLeft,
    .paginationRight {
      i {
        color: #666;
        margin-right: 8px;
      }

      a {
        color: #181818;
      }

      &:hover {
        color: $text-link-color;

        i,
        a {
          color: $text-link-color;
        }
      }
    }
  }
}