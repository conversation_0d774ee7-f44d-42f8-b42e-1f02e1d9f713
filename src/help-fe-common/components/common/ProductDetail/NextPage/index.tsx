import React from 'react';
import { FormattedMessage } from 'react-intl';
import classNames from 'classnames';
import { useHistory } from 'ice';
import store from '@/store';
import { aLinkTrigger } from '@/help-fe-common/utils/global/url/aLinkTrigger';
import { generateUrl } from '@/help-fe-common/utils/global/url/generateUrl';
import { isServer } from '@/help-fe-common/utils/node/getContext';
import isMobile from '@/help-fe-common/utils/global/isMobile';
import styles from './index.module.scss';

export default () => {
  const history = useHistory();
  const [menuState]: any = store.useModel('menu');
  const paginationDocs = menuState?.paginationDocs;

  const renderIcon = (next: boolean) => {
    if (isMobile()) return null;
    if (!next) {
      return <i className="help-iconfont help-icon-prev-arrow" />;
    } else {
      return <i className="help-iconfont help-icon-next-arrow" />;
    }
  };

  return (
    isServer() ?
      <div className="aliyun-docs-pagination" /> :
      <div className={classNames('aliyun-docs-pagination', styles.pagination)}>
        {
          paginationDocs?.[0] || paginationDocs?.[1] ?
            <div className={styles.container}>
              <span className={styles.paginationLeft}>
                {
                  paginationDocs?.[0] ?
                    <a
                      href={generateUrl(paginationDocs?.[0])}
                      onClick={(e) => aLinkTrigger(e, history, { from: 'detail' })}
                      data-spm-click="gostr=/aliyun;locaid=preDoc"
                    >
                      {renderIcon(false)}
                      <FormattedMessage id="help.doc.pagination.prev" />
                      {paginationDocs?.[0]?.title}
                    </a> :
                    <>
                      {renderIcon(false)}
                      <FormattedMessage id="help.doc.pagination.prevNone" />
                    </>
                }
              </span>
              <span className={styles.paginationRight}>
                {
                  paginationDocs?.[1] ?
                    <a
                      href={generateUrl(paginationDocs?.[1])}
                      onClick={(e) => aLinkTrigger(e, history, { from: 'detail' })}
                      data-spm-click="gostr=/aliyun;locaid=nextDoc"
                    >
                      <FormattedMessage id="help.doc.pagination.next" />
                      {paginationDocs?.[1]?.title}
                      {renderIcon(true)}
                    </a> :
                    <>
                      <FormattedMessage id="help.doc.pagination.nextNone" />
                      {renderIcon(true)}
                    </>
                }
              </span>
            </div> :
            null
        }
      </div>
  );
};
