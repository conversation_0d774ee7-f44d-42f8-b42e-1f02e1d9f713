import React from 'react';
import { FormattedMessage } from 'react-intl';
import styles from './index.module.scss';

export default ({ tipId, tipType }) => {
  return (
    <div className={styles.editTip}>
      <span className={styles.icon}>
        <i className={styles.importantIcon} />
        <strong>
          <FormattedMessage id={tipType} />
        </strong>
      </span>
      <p className={styles.tip}>
        <FormattedMessage id={tipId} />
      </p>
    </div>
  );
};
