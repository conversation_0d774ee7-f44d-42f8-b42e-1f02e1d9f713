
import React, { useEffect, useState, FunctionComponent } from 'react';
import { useHistory } from 'ice';
import classnames from 'classnames';
import { FormattedMessage, useIntl } from 'react-intl';
import { generateUrl } from '@/help-fe-common/utils/global/url/generateUrl';
import { aLinkTrigger } from '@/help-fe-common/utils/global/url/aLinkTrigger';
import { setLocalStorageItem, getLocalStorageItem } from '@/help-fe-common/utils/global/localStorage';
import styles from './index.module.scss';
import isMobile from '@/help-fe-common/utils/global/isMobile';

interface IProps {
  productList: any[];
  dataSpm: string;
}

const ProductSelect: FunctionComponent<IProps> = ({ productList, dataSpm }) => {
  const history = useHistory();
  const intl = useIntl();
  const [selectedSub, setSelectedSub] = useState(intl.formatMessage({ id: 'help.doc.newUserTitle' }));
  const [showOptions, setShowOptions] = useState(false);
  const [showBubble, setShowBubble] = useState(true);

  const aClick = (e) => {
    aLinkTrigger(e, history);
    setShowOptions(false);
    setShowBubble(false);
  };

  const expandMenu = () => {
    setShowOptions(!showOptions);
  };

  const closeBubble = () => {
    setShowBubble(false);
  };

  const onCancelClick = (e, className) => {
    const target: any = e?.target;
    const searchDOM = document.getElementsByClassName(className)[0];
    if (!(target === searchDOM) && !searchDOM?.contains(target)) {
      setShowOptions(false);
      setShowBubble(false);
    }
  };

  const setLocalTip = () => {
    const storageItem = getLocalStorageItem('newUserTip');
    if (!storageItem) {
      setLocalStorageItem('newUserTip', true);
      setShowBubble(true);
    } else {
      setShowBubble(false);
    }
  };

  useEffect(() => {
    const selectedProduct = productList?.find((item) => !item?.main && item?.selected);
    if (selectedProduct) {
      setSelectedSub(selectedProduct?.title);
    } else {
      setSelectedSub(intl.formatMessage({ id: 'help.doc.newUserTitle' }));
    }
  }, [productList]);

  useEffect(() => {
    setLocalTip();
    window.addEventListener('click', (e) => { onCancelClick(e, 'subContainer'); });
    return () => {
      window.removeEventListener('click', (e) => { onCancelClick(e, 'subContainer'); });
    };
  }, []);

  const generateOptions = () => {
    return productList.map((product, index) => {
      return (
        <div
          key={product?.id}
          className={styles.menuItem}
          style={{ backgroundColor: product.selected ? '#ECECEF' : '' }}
        >
          <a
            href={generateUrl(product)}
            data-spm={`d_${index}`}
            data-tracker-scm={product?.scm || ''}
            onClick={aClick}
          >
            <span className={styles.itemName} style={{ fontWeight: product.main ? '600' : '400' }}>
              {product.title}
            </span>
          </a>
        </div>
      );
    });
  };

  return (
    <div className={classnames(styles.selectContainer, 'subContainer')} data-spm={dataSpm}>
      <div className={styles.selectedTitle} onClick={expandMenu}>
        <span>{selectedSub}</span>
        <i className={classnames('help-iconfont', 'help-icon-yijishouqi', 'smallFont', showOptions ? styles.iconExpand : styles.iconFold)} />
      </div>
      <div className={styles.optionContainer} style={{ display: showOptions ? 'block' : 'none' }}>
        {generateOptions()}
      </div>
      {
        !isMobile() && showBubble &&
        <div className={styles.bubble}>
          <div className={styles.title}>
            <span><FormattedMessage id="help.doc.newUserTitle" /></span>
            <i className="help-iconfont help-icon-quxiao" onClick={closeBubble} />
          </div>
          <img src={intl.formatMessage({ id: 'help.doc.newUserImg' })} />
          <p><FormattedMessage id="help.doc.newUserTip" /></p>
        </div>
      }
    </div>
  );
};

export default ProductSelect;
