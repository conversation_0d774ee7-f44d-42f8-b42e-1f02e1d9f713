@keyframes expand-in-bottom {
  0% {
    -webkit-clip-path: polygon(-20% 0, 120% 0, 120% 0, -20% 0);
    clip-path: polygon(-20% 0, 120% 0, 120% 0, -20% 0);
    opacity: 0;
  }

  to {
    -webkit-clip-path: polygon(-20% 0, 120% 0, 120% 120%, -20% 120%);
    clip-path: polygon(-20% 0, 120% 0, 120% 0, -20%);
    opacity: 1;
  }
}

.selectContainer {
  position: relative;
  z-index: 998;
  margin: 0 24px;
  margin-bottom: 16px;
  user-select: none;

  i {
    font-style: normal;
  }

  .selectedTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    font-size: 13px;
    color: #474a52;
    letter-spacing: 0.43px;
    border: 1px solid #d4d6db;
    box-sizing: border-box;
    background-color: #fff;

    span {
      margin: 6px 16px;
      text-align: left;
      white-space: nowrap;
      line-height: 20px;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    i {
      margin-right: 10px;
    }

    .iconExpand {
      transform-origin: center;
      transform: rotate(180deg);
    }

    .iconFold {
      transform: rotate(0deg);
    }

    &:hover {
      border: 1px solid #1366ec;
      cursor: pointer;
    }
  }

  .optionContainer {
    display: none;
    position: absolute;
    z-index: 100;
    width: calc(100% - 2px);
    left: 1px;
    top: 34px;
    max-height: 240px;
    padding-top: 10px;
    overflow: auto;
    background-color: #fff;
    box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.10);
    animation: expand-in-bottom 0.2s ease-in-out;

    .menuItem {
      a {
        height: auto;
        text-decoration: none;
        color: #474a52;

        .itemName {
          display: block;
          padding: 8px 16px;
          font-size: 12px;
          line-height: 20px;
          word-break: break-word;
        }

        .mainIcon {
          display: inline-block;
          padding: 0;
          margin-left: 8px;
          width: 20px;
          height: 20px;
          background-color: #e7f0fe;
          border-radius: 4px;
          font-size: 11px;
          color: #1366ec;
          line-height: 20px;
          text-align: center;
        }

      }

      &:hover {
        background-color: #f1f2f3;
      }
    }

    &::-webkit-scrollbar {
      width: 5px;
      height: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #dfdfdf;
      border-radius: 5px;
    }
  }

  .bubble {
    position: absolute;
    left: calc(100% + 16px);
    top: 0;
    z-index: 1003;
    width: 300px;
    height: auto;
    padding: 12px;
    color: #666;
    font-size: 12px;
    background-color: #fff;
    box-shadow: 0 3px 12px 0 rgba(47, 49, 51, 0.12);


    .title {
      display: flex;
      justify-content: space-between;
      font-weight: 500;
      color: #333;

      i {
        font-size: 12px;
        color: #555;
        cursor: pointer;
      }
    }

    img {
      width: 100%;
      height: 126px;
      margin: 10px 0;
      border: 1px solid #ededed;
    }

    p {
      font-size: 12px;
      color: #553;
      line-height: 20px;
    }

    &::after {
      content: '';
      width: 0;
      height: 0;
      transform: rotate(90deg);
      border: 8px solid transparent;
      border-top: 8px solid #fff;
      color: #000;
      overflow: hidden;
      pointer-events: none;
      position: absolute;
      left: -15px;
      top: 12px;
    }
  }
}