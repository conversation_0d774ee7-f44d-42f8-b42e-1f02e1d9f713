.container {
  padding: 0 45px;
  margin-bottom: 20px;

  .content {
    margin-top: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(248px, 1fr));
    grid-gap: 20px;

    >a {
      text-decoration: none;
    }

    .subItem {
      min-width: 248px;
      max-height: 126px;
      background-color: #ffffff;
      border: 1px solid #ebecec;
      padding: 24px;
      cursor: pointer;
      background-position: right bottom;
      background-size: cover;
      background-repeat: no-repeat;

      &:hover {
        animation: backgroundIMG 0.2s ease-in 0s;
        animation-fill-mode: forwards;

        .subTitle {
          color: #1366ec;
        }
      }

      @keyframes backgroundIMG {
        100% {
          box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.1);
          background-image: url('https://img.alicdn.com/imgextra/i3/O1CN01ldbihd1J6syekLpUG_!!6000000000980-2-tps-1456-246.png');
        }
      }

      .subTitle {
        height: 24px;
        line-height: 24px;
        margin: 0;
        margin-bottom: 8px;
        font-size: 16px;
        color: #181818;
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-decoration: none;
      }

      .subDesc {
        max-height: 40px;
        line-height: 20px;
        font-size: 12px;
        color: #666;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
      }
    }
  }
}