import React from 'react';
import { useHistory } from 'ice';
import ModuleHead from '@/help-fe-common/components/common/ProductIndex/ModuleHead';
import { aLinkTrigger } from '@/help-fe-common/utils/global/url/aLinkTrigger';
import isMobile from '@/help-fe-common/utils/global/isMobile';
import pcStyles from './index.module.scss';
import mobileStyles from './mobile.module.scss';

const styles = isMobile() ? mobileStyles : pcStyles;

const SubProduct = ({ data }) => {
  const history = useHistory();

  return (
    <div className={styles.container}>
      <ModuleHead title={data?.title} desc={data?.desc} />
      <div className={styles.content}>
        {
          data?.children?.map((item) => (
            <a
              onClick={(e) => aLinkTrigger(e, history, { from: 'product' })}
              href={item?.url}
              className={styles.subItem}
              key={item?.nodeId}
            >
              <div>
                <h3 className={styles.subTitle} title={item?.title}>{item?.title}</h3>
                <p className={styles.subDesc} title={item?.desc}>{item?.desc}</p>
              </div>
            </a>
          ))
        }
      </div>
    </div>
  );
};
export default SubProduct;

