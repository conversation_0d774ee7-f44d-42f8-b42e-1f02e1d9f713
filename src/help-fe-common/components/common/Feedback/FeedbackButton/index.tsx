import React, { useEffect, useState, useCallback } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import classnames from 'classnames';
import { useRequest } from 'ice';
import service from '@/help-fe-common/services/feedback';
import FeedbackDialog from '@/help-fe-common/components/common/Feedback/FeedbackDialog';
import Message from '@/help-fe-common/components/common/Feedback/Message';
import { setLocalStorageItem, getLocalStorageItem } from '@/help-fe-common/utils/global/localStorage';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import styles from './index.module.scss';

export default ({ docInfo }) => {
  const intl = useIntl();
  const url = window.location.href;
  const [localScoreData, setLocalScoreData] = useState<any>([]);

  // 点赞点踩相关 1点赞、-1点踩，0默认值
  const [score, setScore] = useState(0);
  // 点赞点踩默认值，用于取消时数据回源
  const [defaultScore, setDefaultScore] = useState(0);
  // 弹窗相关
  const [feedBackId, setFeedBackId] = useState<number>(NaN);
  const [showDialog, setShowDialog] = useState(false);
  const [showMessage, setShowMessage] = useState(false);
  const [showTip, setShowTip] = useState(false);
  const [messageType, setMessageType] = useState(true);
  const { request } = useRequest(service.sendFeedback);

  /**
   * 保存到本地localStorage
   * @param scoreValue 反馈评分
   */
  const saveToLocal = useCallback((scoreValue: number) => {
    const newLocalData = [...localScoreData];
    if (scoreValue === 1 || scoreValue === -1) {
      const dataIndex = localScoreData?.findIndex((item) => item?.id === docInfo?.nodeId);
      if (dataIndex > -1) {
        newLocalData.splice(dataIndex, 1);
      }
      setLocalStorageItem('feedback', [...newLocalData, { id: docInfo?.nodeId, score: scoreValue }]);
    }
  }, [docInfo?.nodeId, localScoreData]);

  /**
   * 反馈请求提交
   */
  const feedbackRequest = async (scoreValue: number, formData?) => {
    const params = {
      nodeId: docInfo?.nodeId,
      title: docInfo?.title,
      url,
      score: scoreValue,
      id: !isNaN(feedBackId) ? feedBackId : null,
      ...formData,
    };
    sendSlsLog({
      page: 'documentDetail',
      section: 'feedback',
      action: 'submit',
      userParams1: JSON.stringify(params),
      userParams2: docInfo?.nodeId,
    });
    saveToLocal(scoreValue);
    return request(params);
  };

  /**
   * 点赞&点踩，点踩必须弹窗填写内容
   */
  const onSubmitScore = (scoreValue: number) => {
    setScore(scoreValue);
    if (scoreValue === -1) {
      setShowDialog(true);
    } else {
      setShowTip(true);
    }
    setDefaultScore(scoreValue);
    feedbackRequest(scoreValue).then((res) => {
      if (res?.success) {
        setFeedBackId(res?.data || NaN);
      }
    });
  };

  /**
   * 反馈表单提交
   * @param formData form表单数据
   */
  const onSubmitForm = (formData) => {
    feedbackRequest(score, formData).then((res) => {
      setShowDialog(false);
      setShowMessage(true);
      if (res?.success) {
        setDefaultScore(score);
        setMessageType(true);
      } else {
        setScore(0);
        setMessageType(false);
      }
    });
  };

  useEffect(() => {
    if (!docInfo) return;
    // 切换文档时，需要重置反馈状态
    const storageItem = getLocalStorageItem('feedback') || [];
    setLocalScoreData(storageItem);
    const localScore = storageItem?.find((item) => item?.id === docInfo?.nodeId)?.score || 0;
    setScore(localScore);
    setDefaultScore(localScore);
    setShowDialog(false);
    setFeedBackId(NaN);
  }, [docInfo]);

  useEffect(() => {
    showMessage && setTimeout(() => {
      setShowMessage(false);
    }, 3000);
  }, [showMessage]);

  useEffect(() => {
    showTip && setTimeout(() => {
      setShowTip(false);
    }, 5000);
  }, [showTip]);

  return (
    <div className={classnames(styles.feedbackContainer, 'aliyun-docs-feedback')} id="help-doc-feedback">
      <div className={styles.feedbackHelpTip}>
        <FormattedMessage id="help.feedback.tip" />
      </div>
      <div className={styles.feedbackButtonContainer}>
        {
          showTip &&
          <div className={styles.feedbackTip}>
            <span><FormattedMessage id="help.feedback.thankInfo2" /></span>
            <button onClick={() => { setShowDialog(true); }}><FormattedMessage id="help.feedback.detail" /></button>
          </div>
        }
        <div className={styles.feedbackButtonBox}>
          <button
            className={classnames(styles.iconButton, score === 1 ? styles.fillButton : '')}
            onClick={() => { score !== 1 && onSubmitScore(1); }}
          >
            <i className={classnames('help-iconfont', score === 1 ? 'help-icon-tag-fill' : 'help-icon-tag-empty')} />
          </button>
          <button
            className={classnames(styles.iconButton, score === -1 ? styles.fillButton : '')}
            onClick={() => { score !== -1 && onSubmitScore(-1); }}
          >
            <i
              className={classnames('help-iconfont', score === -1 ? 'help-icon-tag-fill' : 'help-icon-tag-empty')}
              style={{ display: 'inline-block', transform: 'rotateX(-180deg)' }}
            />
          </button>
          <button
            className={styles.clickButton}
            onClick={() => { setShowDialog(true); }}
          >
            <i className="help-iconfont help-icon-bianji" />
            <FormattedMessage id="help.feedback.textButton" />
          </button>
        </div>
      </div>

      {
        showMessage && <Message
          title={intl.formatMessage({ id: messageType ? 'help.feedback.thankInfo' : 'help.feedback.errorInfo' })}
          isSuccess={messageType}
          onClose={() => { setShowMessage(false); }}
        />
      }
      {
        showDialog &&
        <FeedbackDialog
          nodeId={docInfo?.nodeId}
          onSubmit={onSubmitForm}
          onClose={() => { setShowDialog(false); setScore(defaultScore); }}
        />
      }

    </div>
  );
};
