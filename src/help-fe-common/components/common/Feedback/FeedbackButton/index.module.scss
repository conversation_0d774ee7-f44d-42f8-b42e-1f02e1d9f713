.feedbackContainer {
  width: 100%;
  height: 90px;
  padding: 14px 24px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .feedbackHelpTip {
    width: 100%;
    max-width: 600px;
    color: #717171;
    font-size: 14px;
    font-weight: 500;
    line-height: 24px;
    margin-bottom: 14px;
    display: flex;
    align-items: center;

    &::before,
    &::after {
      content: "";
      flex: 1;
      margin: 0 10px;
      border-bottom: 1px solid #EAEAEA;
    }
  }

  .feedbackButtonContainer {
    position: relative;

    .feedbackTip {
      width: 180px;
      box-sizing: border-box;
      padding: 10px 13px;
      margin-bottom: 10px;
      line-height: 24px;
      color: #666;
      font-size: 12px;
      background-color: #FFFFFF;
      box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.10);
      position: absolute;
      top: -80px;
      left: -20px;

      &::after {
        content: '';
        width: 0;
        height: 0;
        border: 8px solid transparent;
        border-top: 8px solid #fff;
        color: #000;
        overflow: hidden;
        pointer-events: none;
        position: absolute;
        left: 20px;
        bottom: -16px;
      }

      button {
        user-select: none;
        border: none;
        background-color: #fff;
        color: #1366EC;
      }
    }

    .feedbackButtonBox {
      display: flex;
      align-items: center;

      button {
        user-select: none;
        border: none;
      }

      .iconButton,
      .clickButton {
        background: #fff;
        text-align: center;
        margin-right: 14px;
        cursor: pointer;
      }

      .iconButton {
        i {
          font-size: 16px;
        }

        &:hover {
          color: #1366EC;
        }
      }

      .fillButton {
        color: #1366EC;
      }

      .clickButton {
        height: 24px;
        line-height: 22px;
        width: 63px;
        border: 1px solid #1366EC;
        margin-right: 14px;
        color: #1366EC;

        font-size: 12px;
        cursor: pointer;
        text-align: center;

        i {
          font-size: 12px;
          margin-right: 6px;
        }
      }
    }
  }
}