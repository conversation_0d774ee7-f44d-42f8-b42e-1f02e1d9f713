import React, { useState, useEffect } from 'react';
import { useRequest } from 'ice';
import { useIntl } from 'react-intl';
import FeedbackDialog from '@/help-fe-common/components/common/Feedback/FeedbackDialog';
import Message from '@/help-fe-common/components/common/Feedback/Message';
import service from '@/help-fe-common/services/feedback';
import { isServer } from '@/help-fe-common/utils/node/getContext';

export default ({ identifier, showDialog, setShowDialog }) => {
  const intl = useIntl();
  // 弹窗相关
  const [showMessage, setShowMessage] = useState(false);
  const [messageType, setMessageType] = useState(true);
  const { request } = useRequest(service.sendFeedback);

  /**
   * 反馈请求提交
   */
  const feedbackRequest = async (formData?) => {
    const params = {
      nodeId: identifier?.nodeId,
      alias: identifier?.alias,
      title: '',
      url: window.location.href,
      score: 0,
      ...formData,
    };
    return request(params);
  };

  /**
 * 反馈表单提交
 * @param formData form表单数据
 */
  const onSubmitForm = (formData) => {
    feedbackRequest(formData).then((res) => {
      setShowDialog(false);
      setShowMessage(true);
      if (res?.success) {
        setMessageType(true);
      } else {
        setMessageType(false);
      }
    });
  };

  // 提示消息3s自动关闭
  useEffect(() => {
    showMessage && setTimeout(() => {
      setShowMessage(false);
    }, 3000);
  }, [showMessage]);

  if (isServer()) {
    return (
      null
    );
  }

  return (
    <div>
      {
      showDialog &&
      <FeedbackDialog
        nodeId={identifier?.nodeId}
        alias={identifier?.alias}
        onSubmit={onSubmitForm}
        onClose={() => { setShowDialog(false); }}
      />
    }
      {
      showMessage && <Message
        title={intl.formatMessage({ id: messageType ? 'help.feedback.thankInfo' : 'help.feedback.errorInfo' })}
        isSuccess={messageType}
        onClose={() => { setShowMessage(false); }}
      />
    }
    </div>
  );
};
