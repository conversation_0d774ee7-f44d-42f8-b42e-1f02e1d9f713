import React from 'react';
import { FormattedMessage } from 'react-intl';
import classnames from 'classnames';

import styles from './index.module.scss';

export default ({ ...param }) => {
  const { title, message, isSuccess, isShowButton, onClose } = param;

  const close = () => {
    if (onClose) {
      onClose();
    }
  };

  return (
    <div className={styles.messageContainer}>
      <dialog className={styles.container} open >
        <div className={styles.top}>
          <i className="help-iconfont help-icon-quxiao" onClick={close} />
        </div>
        <div className={styles.content}>
          <div className={styles.dialogTitle}>
            <i className={classnames('help-iconfont', isSuccess ? 'help-icon-success' : 'help-icon-zhuyi')} style={{ color: isSuccess ? '#63BA4D' : '#F15533' }} />
            {title}
          </div>
          {
            message ?
              <div className={styles.dialogMessage}>
                {message}
              </div>
              :
              null
          }
        </div>
        {
          !isShowButton ?
            <span className={styles.footerTip}><FormattedMessage id="help.feedback.timeTip" /></span>
            :
            <div className={styles.buttonGroup}>
              <button className={styles.close} onClick={close}><FormattedMessage id="help.feedback.cancel" /></button>
              <button className={styles.submit} onClick={close}><FormattedMessage id="help.feedback.submit" /></button>
            </div>
        }
      </dialog>
    </div>
  );
};
