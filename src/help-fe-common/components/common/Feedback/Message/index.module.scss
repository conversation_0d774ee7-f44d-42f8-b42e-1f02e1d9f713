.messageContainer {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  padding: 0;
  margin: 0;
  background-color: rgba($color: #fff, $alpha: 0.4);
  z-index: 100;

  .container {
    max-width: 440px;
    width: 80%;
    padding: 20px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: #FFFFFF;
    box-shadow: 0 0 25px 0 rgba(0, 0, 0, 0.20);
    border: initial;

    .top {
      position: absolute;
      right: 20px;
      top: 20px;

      i {
        font-size: 16px;
        color: #999;
        cursor: pointer;
      }
    }

    .content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 146px;

      .dialogTitle {
        font-size: 18px;
        font-weight: 500;
        color: #181818;

        i {
          font-size: 20px;
          font-weight: 500;
          margin-right: 8px;
        }
      }

      .dialogMessage {
        margin-top: 10px;
        font-size: 14px;
        color: #181818;
      }
    }

    .footerTip {
      position: absolute;
      right: 20px;
      bottom: 20px;
      font-size: 14px;
      color: #999999;
    }

    .buttonGroup {
      text-align: right;

      .close {
        padding: 5px 20px;
        margin-right: 10px;
        border: 1px solid #d8d8d8;
        font-size: 14px;
        background-color: #fff;
        color: rgb(153, 153, 153);

        &:hover {
          background-color: #f4f4f4;
        }
      }

      .submit {
        padding: 5px 20px;
        color: #fff;
        background-color: #1366ec;
        font-size: 14px;
        border: none;

        &:hover {
          background: #1154c0;
        }
      }
    }
  }
}