.imgContent {
  height: 80px;
  width: 100%;
  position: relative;
  overflow: hidden;
  cursor: pointer;

  img {
    width: 100%;
    object-fit: cover;
  }

  .mask {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
    color: #fff;
    text-align: right;

    span {
      display: inline-block;
      background-color: #1366ec;
      padding: 3px 15px;
      font-size: 12px;
    }
  }
}

.overlay {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1202;

  .overlayInner {
    width: 80%;
    min-height: 80%;
    z-index: 1001;
    padding: 40px;

    .connectFeedbackCanvas {
      position: relative;
      height: 80vh;
      width: 100%;
      overflow-y: auto;
      background-color: #fff;
    }
  }

  .tools {
    position: absolute;
    left: 50%;
    bottom: 0;
    transform: translateX(-50%);
    background-color: #fff;
    vertical-align: middle;
    line-height: 36px;
    height: 36px;
    font-size: 0;

    .close {
      color: #F15533;
    }

    .save {
      color: #63BA4D
    }

    button {
      display: inline-block;
      width: 36px;
      height: 36px;
      cursor: pointer;
      text-align: center;
      user-select: none;
      border: none;
      background-color: #fff;

      i {
        font-size: 14px;
      }

      &:hover {
        background-color: #d8d8d8;
      }
    }
  }
}

// canvas样式处理
:global {

  .tui-image-editor-canvas-container,
  .lower-canvas,
  .upper-canvas {
    height: auto !important;
    max-width: none !important;
    max-height: none !important;
  }
}