import React, { useEffect, useState, useRef, useCallback } from 'react';
import { Overlay } from '@alifd/next';
import classnames from 'classnames';
import { FormattedMessage } from 'react-intl';
import ImageEditor from 'tui-image-editor';
import Loading from '@/help-fe-common/components/common/Loading';
import styles from './index.module.scss';

interface IProps {
  url: string;
  loading: boolean;
  onSave: (dataURL: string) => void;
}

function Editor(props: IProps) {
  const { url, loading, onSave } = props;
  const [showOverlay, setShowOverlay] = useState(false);
  const [previewImage, setPreviewImage] = useState(url);
  const editorRef = useRef<ImageEditor>(null);

  const drawRect = useCallback(() => {
    editorRef.current?.stopDrawingMode();
    editorRef.current?.startDrawingMode('SHAPE');
  }, []);

  const drawLine = useCallback(() => {
    editorRef.current?.stopDrawingMode();
    editorRef.current?.startDrawingMode('LINE_DRAWING', {
      width: 8,
      color: '#d93026',
      // @ts-ignore
      arrowType: {
        tail: 'chevron',
      },
    });
  }, []);

  const drawText = useCallback(() => {
    editorRef.current?.stopDrawingMode();
    editorRef.current?.startDrawingMode('TEXT');
  }, []);

  const close = useCallback(() => {
    editorRef.current?.stopDrawingMode();
    editorRef.current?.clearUndoStack();
    setShowOverlay(false);
  }, []);

  const save = useCallback(() => {
    editorRef.current?.stopDrawingMode();
    const dataURL = editorRef.current?.toDataURL();
    setPreviewImage(dataURL || '');
    if (onSave) onSave(dataURL || '');
    setShowOverlay(false);
  }, [onSave]);

  const reset = useCallback(() => {
    editorRef.current?.clearObjects();
  }, []);

  const handleShowOverlay = useCallback(() => {
    if (loading) return;
    setShowOverlay(true);
  }, [loading]);

  // 初始化图片编辑工具
  useEffect(() => {
    const anchor = '.help-feedback-canvas';
    if (!document.querySelector(anchor) || editorRef.current) return;

    // @ts-ignore
    editorRef.current = new ImageEditor(document.querySelector(anchor), {
      usageStatistics: false,
      cssMaxWidth: 1280,
      cssMaxHeight: 600,
      selectionStyle: {
        cornerSize: 10,
        rotatingPointOffset: 70,
        cornerColor: '#FF6A00',
        borderColor: '#FF6A00',
      },
    });

    const instance = editorRef.current;

    instance.on(
      'addText',
      (pos: { originPosition: { x: number; y: number } }) => {
        instance
          .addText(' ', {
            position: pos.originPosition,
            styles: {
              fill: '#d93026',
              fontWeight: '600',
            },
          })
          .catch(() => { });
      },
    );

    instance.setDrawingShape('rect', {
      fill: 'rgba(255,244,236,0.36)',
      stroke: '#FFDE03',
      strokeWidth: 2,
    });

    // eslint-disable-next-line no-void
    void instance.loadImageFromURL(url, 'help').then((v) => {
      instance.clearUndoStack();
    });
  }, [showOverlay, url]);

  useEffect(() => {
    if (!url) return;
    setPreviewImage(url);
    // eslint-disable-next-line no-void
    void editorRef.current?.loadImageFromURL(url, 'help').then(() => {
      // @ts-ignore
      editorRef.current.clearUndoStack();
    });
  }, [url]);

  return (
    <div>
      {
        loading ?
          <Loading loading={loading} style={{}} /> :
          <div className={styles.imgContent} onClick={handleShowOverlay}>
            <img src={previewImage} alt="" />
            <div className={styles.mask}>
              <span><FormattedMessage id="help.feedback.preview" /></span>
            </div>
          </div>
      }
      <Overlay
        cache
        hasMask
        disableScroll
        align="cc cc"
        visible={showOverlay}
        wrapperClassName={styles.overlay}
      >
        <div className={styles.overlayInner} style={{ display: !showOverlay ? 'none' : '' }}>
          <div className={classnames(styles.connectFeedbackCanvas, 'help-feedback-canvas')} />
          <div className={styles.tools}>
            <button onClick={drawRect}>
              <i className="help-iconfont help-icon-tx-zhengfangxing" />
            </button>
            <button onClick={drawLine}>
              <i className="help-iconfont help-icon-jiantou_youxia" />
            </button>
            <button onClick={drawText}>
              <i className="help-iconfont help-icon-ziti" />
            </button>
            <button onClick={reset}>
              <i className="help-iconfont help-icon-shuaxin" />
            </button>
            <button className={styles.close} onClick={close} >
              <i className="help-iconfont help-icon-quxiao" />
            </button>
            <button className={styles.save} onClick={save}>
              <i className="help-iconfont help-icon-duigou1" />
            </button>
          </div>
        </div>
      </Overlay >
    </div >
  );
}

export default React.memo(Editor);
