.overlay {
  width: 100%;
  height: 100%;
  position: relative;
}

.dialogContainer {
  width: 80vw;
  height: 100vh;

  .container {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    max-width: 580px;
    background-color: #FFFFFF;
    box-shadow: 0 0 25px 0 rgba(0, 0, 0, 0.20);
    padding: 25px;
    border: initial;
    line-height: normal;

    .title {
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      div {
        font-size: 16px;
        font-weight: 500;
      }

      i {
        font-size: 16px;
        color: #999;
        cursor: pointer;
      }
    }

    .feedbackTip {
      margin-bottom: 20px;
      font-size: 14px;
      color: #999;

      a {
        margin: 0 4px;
        color: #1366ec;
      }
    }

    .formItem {
      margin-bottom: 20px;

      .formItemLabel {
        margin-bottom: 15px;
        font-size: 14px;
        color: #181818;
        display: flex;
        align-items: center;

        input {
          margin-right: 3px;
        }
      }

      .requiredStar::before {
        line-height: 21px;
        color: #FF3838;
        font-size: 22px;
        margin-right: 4px;
        position: relative;
        top: 4px;
        content: "*";
      }

      .feedbackContent {
        width: calc(100% - 20px);
        height: 100px;
        padding: 10px;
        background-color: #fff;
        border: 1px solid #dedede;
        resize: none;
        outline: none;
      }

      .feedbackContact {
        width: calc(100% - 20px);
        line-height: 20px;
        padding: 10px;
        background-color: #fff;
        border: 1px solid #dedede;
        resize: none;
        outline: none;
      }

      .imgContainer {
        height: 80px;
        background-color: #F5F5F5;
        border: 1px solid #D8D8D8;
        position: relative;

        .imgTip {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: #999;
          font-size: 14px;
        }

        .imgContent {
          height: 80px;
          width: 100%;
          position: relative;
          overflow: hidden;
          cursor: pointer;

          img {
            width: 100%;
            object-fit: cover;
          }

          .mask {
            position: absolute;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.1);
            color: #fff;
            text-align: right;

            span {
              display: inline-block;
              background-color: #1366ec;
              padding: 3px 15px;
              font-size: 12px;
            }
          }
        }
      }

      .radioContainer {
        display: flex;
        flex-wrap: wrap;

        .radioItem {
          margin: 0 20px 15px 0;
          display: flex;
          align-items: center;
          font-size: 14px;
          color: #999;

          label {
            margin: 0;
            max-width: initial;
            font-weight: 400;
            font-size: 14px;
            color: #999;
            cursor: pointer;
          }

          input[type="radio"] {
            width: 15px;
            height: 15px;
            background-color: #fff;
            border-radius: 50%;
            margin: 0 8px 0 0;
            cursor: pointer;
            position: relative;
            border: 1px solid #D8D8D8;
          }

          input:checked::before {
            position: absolute;
            content: "";
            width: 15px;
            height: 15px;
            top: 0;
            left: 0;
            border-radius: 50%;
            background-color: #fff;
            border: 1px solid #1366ec;
          }

          input:checked::after {
            position: absolute;
            content: "";
            width: 8px;
            height: 8px;
            top: 3.5px;
            left: 3.5px;
            border-radius: 50%;
            background-color: #1366ec;
            border: 1px solid #1366ec;
          }
        }
      }

      .inputErrorTip {
        font-size: 12px;
        color: #d93026;
        display: inline-block;
      }
    }

    .buttonGroup {
      text-align: right;

      .close {
        padding: 5px 20px;
        margin-right: 10px;
        border: 1px solid #d8d8d8;
        font-size: 14px;
        background-color: #fff;
        color: rgb(153, 153, 153);

        &:hover {
          background-color: #f4f4f4;
        }
      }

      .submit {
        padding: 5px 20px;
        color: #fff;
        background-color: #1366ec;
        font-size: 14px;
        border: none;

        &:hover {
          background: #1154c0;
        }
      }
    }
  }
}

@media only screen and (max-width: 1055px) {
  .dialogContainer {
    width: 100vw;

    .container {
      width: 90%;
    }
  }
}