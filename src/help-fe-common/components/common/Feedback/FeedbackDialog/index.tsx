import React, { useCallback, useEffect, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { useRequest } from 'ice';
import { Overlay } from '@alifd/next';
import classnames from 'classnames';
import html2canvas from 'html2canvas';
import ImgEditor from './ImgEditor';
import service from '@/help-fe-common/services/feedback';
import { getLoginState, generateLoginUrl } from '@/help-fe-common/utils/global/user';
import { dataURL2Blob2File } from '@/help-fe-common/utils/global/fileUpload';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import styles from './index.module.scss';

interface IProps {
  nodeId: string;
  alias?: any;
  onSubmit: Function;
  onClose: Function;
}

export default ({ nodeId, alias, onSubmit, onClose }: IProps) => {
  const intl = useIntl();
  const [inputValue, setInputValue] = useState('');
  const [radioValue, setRadioValue] = useState('');
  const [showInputTip, setShowInputTip] = useState(false);
  const [allowSubmit, setAllowSubmit] = useState(false);
  const [expire, setExpire] = useState(0); // 设置oss凭证有效时间
  const EXPIRE_TIME = 1800000; // 有效期半小时
  /*  截图相关 */
  const [showImg, setShowImg] = useState(false);
  const [imgSrc, setImgSrc] = useState('');
  const [screenLoading, setScreenLoading] = useState(false);
  const { data: uploadInfo, request: getUploadInfo } = useRequest(service.getFeedBackUploadInfo);
  const { request: uploadSnapshot } = useRequest(service.uploadSnapshot);

  const selectItems = [
    { value: 'CONTENT_ACCURACY', name: intl.formatMessage({ id: 'help.feedback.problem1' }) },
    { value: 'PRODUCT_USE', name: intl.formatMessage({ id: 'help.feedback.problem2' }) },
    { value: 'SEARCH_ACCURACY', name: intl.formatMessage({ id: 'help.feedback.problem3' }) },
    { value: 'CONTENT_ILLEGAL', name: intl.formatMessage({ id: 'help.feedback.problem4' }) },
    { value: 'OTHERS', name: intl.formatMessage({ id: 'help.feedback.problem5' }) },
  ];

  const onInputChange = (e) => {
    const value = e?.target?.value?.trim();
    if (value?.length < 10) {
      setShowInputTip(true);
      setInputValue('');
    } else {
      setShowInputTip(false);
      setInputValue(value);
    }
  };

  const onRadioChange = (e) => {
    setRadioValue(e?.target?.id);
  };

  const replaceSvgImg = () => {
    return new Promise((resolve, reject) => {
      let count = 0;
      const convertImgToDataURL = (img: HTMLImageElement) => {
        fetch(img.src)
          .then((response) => response.blob())
          .then((blob) => {
            const reader = new FileReader();
            reader.readAsDataURL(blob);
            reader.onloadend = () => {
              img.src = reader.result as string;
            };
          })
          .catch(() => {
            console.error('svg 图片转 DataUrl 失败', img.src);
            reject();
          })
          .finally(() => {
            count--;
            if (count === 0) {
              resolve(true);
            }
          });
      };

      document.querySelectorAll('.unionContainer .markdown-body img').forEach((img: HTMLImageElement) => {
        const src = img.getAttribute('src') || '';
        if (src.toLowerCase().endsWith('.svg')) {
          count++;
          convertImgToDataURL(img); // 处理每个 SVG img 元素
        }
      });

      if (count === 0) {
        resolve(true);
      }
    });
  };

  /**
   * 获取页面截图
   */
  const getScreenshot = async () => {
    await replaceSvgImg();
    const appDOM = document.getElementById('app') as HTMLElement;
    // canvas截图忽略的dom元素
    const feedbackDOM = appDOM.querySelector('#help-doc-feedback');
    const feedbackDialog = appDOM.querySelector('#help-feedback-dialog');
    feedbackDOM && feedbackDOM.setAttribute('data-html2canvas-ignore', 'true');
    feedbackDialog && feedbackDialog.setAttribute('data-html2canvas-ignore', 'true');
    const height = innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
    const scrollY = document.documentElement.scrollTop || document.body.scrollTop;
    html2canvas(appDOM, {
      useCORS: true,
      y: scrollY,
      scrollY,
      height,
      scale: window.devicePixelRatio, // 避免截图模糊
    }).then((canvas) => {
      const dataImg = canvas.toDataURL('image/jpeg', 0.1);
      setScreenLoading(false);
      setImgSrc(dataImg);
    }).catch((e) => {
      console.error(e);
      setScreenLoading(false);
    });
  };

  /**
   * 上传页面截图
   */
  const uploadScreenShot = useCallback(() => {
    const fileImg = dataURL2Blob2File(imgSrc, `snapshot_${nodeId || alias}`);
    uploadSnapshot(uploadInfo, fileImg, imgSrc);
  }, [imgSrc, nodeId, alias, uploadSnapshot, uploadInfo]);

  /**
   * 获取上传oss凭证，点击截图&超出有效时间触发
   */
  const initUploadInfo = useCallback(() => {
    setExpire(Date.now());
    return getUploadInfo({ nodeId, alias });
  }, [getUploadInfo, nodeId, alias]);

  const handleSubmit = useCallback((accessUrl: any) => {
    const feedbackDom = document.getElementById('help-feedback-dialog');
    const textareaDOM = feedbackDom?.querySelectorAll('textarea');
    const content = textareaDOM?.[0].value;
    const contact = textareaDOM?.[1].value;
    const type = document.querySelector('input[name=type]:checked')?.id;
    const val = {
      content,
      contact,
      type,
      snapshot: showImg ? accessUrl : '',
    };
    if (onSubmit) {
      onSubmit(val);
      // 反馈记录提交sls日志
      sendSlsLog({
        page: 'documentDetail',
        section: 'feedback',
        action: 'uploadSuccess',
        userParams1: JSON.stringify({ val, imgSrc }),
      });
    }
  }, [imgSrc, onSubmit, showImg]);

  const submit = useCallback(() => {
    setAllowSubmit(false); // 防止重复点击
    if (Date.now() - expire >= EXPIRE_TIME && showImg) { // 超时且开启截图
      initUploadInfo().then((rst) => {
        handleSubmit(rst?.accessUrl);
      });
    } else {
      handleSubmit(uploadInfo?.accessUrl);
    }
  }, [expire, handleSubmit, initUploadInfo, showImg, uploadInfo?.accessUrl]);

  const close = () => {
    if (onClose) {
      onClose();
    }
  };

  useEffect(() => {
    if (showImg && !imgSrc) {
      setScreenLoading(true);
      setTimeout(() => { // 将截图设为宏任务，避免阻塞loading
        getScreenshot();
      }, 10);
    }
    if (showImg && imgSrc) {
      uploadScreenShot();
    }
  }, [imgSrc, showImg, uploadScreenShot]);

  useEffect(() => {
    if (inputValue && radioValue && ((showImg && imgSrc) || !showImg)) {
      setAllowSubmit(true);
    } else {
      setAllowSubmit(false);
    }
  }, [imgSrc, inputValue, radioValue, showImg]);

  useEffect(() => {
    if (Date.now() - expire >= EXPIRE_TIME && showImg) {
      initUploadInfo();
    }
  }, [showImg, initUploadInfo, expire]);

  return (
    <Overlay
      cache
      hasMask
      disableScroll
      align="cc cc"
      visible
      wrapperClassName={styles.overlay}
    >
      <div className={styles.dialogContainer} id="help-feedback-dialog">
        <dialog className={styles.container} open>
          <div className={styles.title}>
            <div><FormattedMessage id="help.feedback.title" /></div>
            <i className="help-iconfont help-icon-quxiao" onClick={close} />
          </div>
          {
            !getLoginState() &&
            <div className={styles.feedbackTip}>
              <FormattedMessage id="help.feedback.loginTip1" />
              <a href={generateLoginUrl(encodeURIComponent(window.location.href))} ><FormattedMessage id="help.feedback.login" /></a>
              <FormattedMessage id="help.feedback.loginTip2" />
            </div>
          }
          <div className={styles.formItem} style={{ marginBottom: 0 }}>
            <div className={classnames(styles.formItemLabel, styles.requiredStar)}>
              <span><FormattedMessage id="help.feedback.problemType" /></span>
            </div>
            <div className={styles.radioContainer}>
              {
                selectItems?.map((item) => (
                  <div key={item?.value} className={styles.radioItem}>
                    <input type="radio" name="type" id={item?.value} onChange={(e) => onRadioChange(e)} />
                    <label htmlFor={item?.value}>{item?.name}</label>
                  </div>
                ))
              }
            </div>
          </div>
          <div className={styles.formItem}>
            <div className={classnames(styles.formItemLabel, styles.requiredStar)}>
              <span><FormattedMessage id="help.feedback.description" /></span>
            </div>
            <div>
              <textarea
                maxLength={2047}
                cols={6}
                className={styles.feedbackContent}
                style={showInputTip ? { border: '1px solid #d93026' } : {}}
                placeholder={intl.formatMessage({ id: 'help.feedback.descriptionTip' })}
                onChange={(e) => onInputChange(e)}
              />
              {
                showInputTip &&
                <span className={styles.inputErrorTip}><FormattedMessage id="help.feedback.inputErrorTip" /></span>
              }
            </div>
          </div>
          <div className={styles.formItem}>
            <div className={styles.formItemLabel}><FormattedMessage id="help.feedback.contact" /></div>
            <div>
              <textarea
                maxLength={100}
                cols={2}
                className={styles.feedbackContact}
                placeholder={intl.formatMessage({ id: 'help.feedback.contactTip' })}
              />
            </div>
          </div>
          <div className={styles.formItem}>
            <div className={styles.formItemLabel}>
              <input type="checkbox" checked={showImg} className={styles.singleRadio} onChange={() => { setShowImg(!showImg); }} />
              <FormattedMessage id="help.feedback.screenshot" />
            </div>
            <div className={styles.imgContainer}>
              {
                !showImg ?
                  <span className={styles.imgTip}><FormattedMessage id="help.feedback.noImg" /></span>
                  :
                  <ImgEditor url={imgSrc} loading={screenLoading} onSave={(img) => { setImgSrc(img); }} />
              }
            </div>
          </div>
          <div className={styles.buttonGroup}>
            <button className={styles.close} onClick={close}><FormattedMessage id="help.feedback.cancel" /></button>
            <button
              className={styles.submit}
              style={{ cursor: allowSubmit ? 'pointer' : 'not-allowed' }}
              onClick={() => { allowSubmit && submit(); }}
            >
              <FormattedMessage id="help.feedback.submit" />
            </button>
          </div>
        </dialog>
      </div>
    </Overlay>
  );
};
