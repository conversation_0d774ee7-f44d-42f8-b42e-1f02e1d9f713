@media only screen and (max-width: 1055px) {
  :root {
    --col-wrapper-margin: 16px
  }
}

@media only screen and (min-width: 1056px) and (max-width:1968px) {
  :root {
    --col-wrapper-margin: 24px
  }
}

@media only screen and (min-width: 1969px) {
  :root {
    --col-wrapper-margin: calc((100vw - 1920px) / 2)
  }
}

.help-body-wrapper-column {
  margin: 0 auto;
  width: calc(100% - var(--col-wrapper-margin)* 2);
}

.help-body-wrapper-column .col-extend-left {
  margin-left: calc(var(--col-wrapper-margin) * -1);
  padding-left: var(--col-wrapper-margin)
}

.help-body-wrapper-column .col-extend-right {
  margin-right: calc(var(--col-wrapper-margin) * -1);
  padding-right: var(--col-wrapper-margin)
}

.help-body-wrapper-column .col-extend-left.col-extend-right {
  width: calc(100% + var(--col-wrapper-margin) * 2)
}