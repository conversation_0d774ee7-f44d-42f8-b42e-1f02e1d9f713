import React, { useEffect, useRef, useState } from 'react';
import { computeImgStyle } from '@/help-fe-common/utils/global/previewImg';
import styles from './index.module.scss';

interface IProps {
  data: { width: number; height: number; src: string };
  onClose: () => void;
}

export default ({ data, onClose }: IProps) => {
  const [store, setStore] = useState({ scale: 1, moveable: false, originScale: 1, pageX: 0, pageY: 0, pageX2: 0, pageY2: 0 });
  const [mouseDownPos, setMouseDownPos] = useState({ x: 0, y: 0 });
  const [mouseDownFlag, setMouseDownFlag] = useState(false);
  const [zoomIn, setZoomIn] = useState(true);
  const [imgDrag, setImgDrag] = useState(false);
  const imgZoom: any = useRef(null);

  const getDistance = function (start, stop) {
    // 用三角函数计算两指之间距离
    return Math.hypot(stop.x - start.x,
      stop.y - start.y);
  };

  const handleTouchDown = (e) => {
    e.stopPropagation();
    e.preventDefault();
    if (e.touches.length === 1) {
      // 单指操作
      store.moveable = true;
      const { clientX, clientY } = e;
      setMouseDownFlag(true);
      setMouseDownPos({
        x: clientX,
        y: clientY,
      });
    } else {
      setImgDrag(true);
      const [item1, item2] = e.touches;
      const { pageX, pageY } = item1;
      const { pageX: pageX2, pageY: pageY2 } = item2;
      setStore((prev) => {
        return (
          {
            ...prev,
            pageX,
            pageY,
            pageX2,
            pageY2,
            moveable: true,
            originScale: prev.scale || 1,
          }
        );
      });
    }
  };

  const handleTouchMove = (e) => {
    e.stopPropagation();
    e.preventDefault();

    const { touches } = e;
    const events = touches[0];
    const events2 = touches[1];

    if (!store.moveable || !imgDrag) {
      return;
    }

    if (touches.length === 1) {
      handleSingleTouchMove(e);
    } else if (events2) {
      handleDoubleTouchMove(events, events2);
    }
  };

  const handleSingleTouchMove = (e) => {
    const { clientX, clientY } = e.touches[0];
    const diffX = clientX - mouseDownPos.x;
    const diffY = clientY - mouseDownPos.y;

    if ((!mouseDownFlag) || (diffX === 0 && diffY === 0)) {
      return;
    }

    const { offsetLeft, offsetTop } = imgZoom.current as HTMLImageElement;
    const offsetX = parseInt(`${diffX + offsetLeft}`, 10);
    const offsetY = parseInt(`${diffY + offsetTop}`, 10);

    setMouseDownPos({
      x: clientX,
      y: clientY,
    });

    imgZoom.current.style.left = `${offsetX}px`;
    imgZoom.current.style.top = `${offsetY}px`;
  };

  const handleDoubleTouchMove = (events, events2) => {
    // 第2个指头坐标在touchmove时候获取
    if (!store.pageX2) {
      store.pageX2 = events2.pageX;
    }
    if (!store.pageY2) {
      store.pageY2 = events2.pageY;
    }

    // 双指缩放比例计算
    const zoomm = getDistance({
      x: events.pageX,
      y: events.pageY,
    }, {
      x: events2.pageX,
      y: events2.pageY,
    }) / getDistance({
      x: store.pageX,
      y: store.pageY,
    }, {
      x: store.pageX2,
      y: store.pageY2,
    });

    // 应用在元素上的缩放比例
    let newScale = store.originScale * zoomm;

    // 最大缩放比例限制
    if (newScale > 10) {
      newScale = 10;
    } else if (newScale < 1) {
      newScale = 1;
    }

    // 记住使用的缩放值
    store.scale = newScale;

    // 图像应用缩放效果
    imgZoom.current.style.transform = `scale(${newScale})`;

    if (newScale === 1) {
      imgZoom.current.style.left = '';
      imgZoom.current.style.top = '';
      setImgDrag(false);
    }
  };

  // 针对解决safari的overflow：hidden失效的问题
  const body = useRef(document.getElementsByTagName('body'));
  useEffect(() => {
    if (/Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent)) {
      body.current[0].style.overflowY = 'hidden';
      body.current[0].style.overscrollBehavior = 'contain';
    } else {
      body.current[0].style.overflowY = 'hidden';
    }
  }, [body]);

  const handleTouchEnd = (e) => {
    store.moveable = false;
  };

  const handleTouchCancel = (e) => {
    store.moveable = false;
  };

  return (
    <div
      className="img-back"
      id="img-container"
      onClick={onClose}
    >
      <img
        src={data.src}
        ref={imgZoom}
        className="img-float"
        draggable="false"
        style={computeImgStyle(data, zoomIn)}
        onTouchStart={handleTouchDown}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onTouchCancel={handleTouchCancel}
      />
      <button className={styles.button} onClick={onClose}>
        <i className="help-iconfont help-icon-quxiao" />
      </button>
    </div>
  );
};
