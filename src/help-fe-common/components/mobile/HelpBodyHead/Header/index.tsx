import React, { useEffect, useState } from 'react';
import classnames from 'classnames';
import { FormattedMessage } from 'react-intl';
import ProductList from '@/help-fe-common/components/common/ProductList';
import Menu from '@/help-fe-common/components/pc/Menu';
import Outline from '@/help-fe-common/components/mobile/Outline';
import { extractSynopsisData, clickToHash } from '@/help-fe-common/utils/helpDoc/docOutline';
import styles from './index.module.scss';

interface childProps {
  showSynopsisButton?: boolean;
  current?: any;
  menuData: any;
}

enum WrapperType {
  MENU,
  ALL_PRODUCT_LIST,
  OUTLINE,
  DEFAULT
}

export default ({ showSynopsisButton = false, current, menuData }: childProps) => {
  const { newProductInfo } = menuData;
  const [showWrapperType, setShowWrapperType] = useState<WrapperType>(WrapperType.DEFAULT);
  const [synopsisData, setSynopsisData] = useState<any>({});

  const getSynopsisData = () => {
    const docContent = document.querySelector('.unionContainer .markdown-body')?.innerHTML || '';
    const data = extractSynopsisData(docContent);
    setSynopsisData(data);
  };

  const closeWrapper = () => {
    setShowWrapperType(WrapperType.DEFAULT);
  };

  const openWrapper = (type: WrapperType) => {
    const showType = showWrapperType === type ? WrapperType.DEFAULT : type;
    setShowWrapperType(showType);
  };

  const onOutlineClick = (id) => {
    closeWrapper();
    document.body.classList.remove(styles.bodyHide);
    clickToHash(id);
  };

  const renderWrapper = () => {
    switch (showWrapperType) {
      case WrapperType.MENU:
        return (
          <div className={styles.directoryTree}>
            <Menu onClose={closeWrapper} menuData={menuData} />
          </div>
        );
      case WrapperType.ALL_PRODUCT_LIST:
        return (
          <div className={styles.productDialog}>
            <ProductList onMouseLeave={closeWrapper} />
          </div>
        );
      case WrapperType.OUTLINE:
        return (
          <div className={styles.sysnopsisDialog}>
            <Outline
              data={synopsisData}
              current={current}
              onOutlineClick={onOutlineClick}
            />
          </div>
        );
      case WrapperType.DEFAULT:
        return null;
    }
  };

  useEffect(() => {
    if (showWrapperType !== WrapperType.DEFAULT) {
      document.body.classList.add(styles.bodyHide);
      document.body.style.top = '0px';
    } else {
      document.body.classList.remove(styles.bodyHide);
    }
  }, [showWrapperType]);

  return (
    <div className={styles.headerContainer}>
      <div className={styles.HeaderTop}>
        <i
          onClick={() => {
            openWrapper(WrapperType.MENU);
          }}
          className={
            classnames(
              'help-iconfont',
              showWrapperType === WrapperType.MENU
                ? 'help-icon-mulushu-zhankaiicon'
                : 'help-icon-mulushu-shouqiicon',
            )}
        />
        <span
          className={styles.title}
          onClick={() => {
            openWrapper(WrapperType.ALL_PRODUCT_LIST);
          }}
        >
          <span
            className={classnames(styles.titleText, showWrapperType === WrapperType.ALL_PRODUCT_LIST ? styles.action : '')}
          >
            {newProductInfo?.title}
          </span>
          <i
            className={
              classnames(
                'help-iconfont',
                showWrapperType === WrapperType.ALL_PRODUCT_LIST
                  ? `help-icon-yijizhankai ${styles.action}`
                  : 'help-icon-yijishouqi',
              )}
          />
        </span>
        {
          showSynopsisButton ?
            <span
              className={showWrapperType === WrapperType.OUTLINE ? styles.directoryOpen : styles.directoryTitle}
              onClick={() => {
                getSynopsisData();
                openWrapper(WrapperType.OUTLINE);
              }}
            >
              <FormattedMessage id="help.doc.synopsis.mobile.title" />
            </span> :
            null
        }
      </div>
      {renderWrapper()}
    </div>
  );
};
