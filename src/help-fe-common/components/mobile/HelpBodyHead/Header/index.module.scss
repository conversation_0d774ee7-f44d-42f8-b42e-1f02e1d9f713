.headerContainer {
  width: 100%;
  height: 0.54rem;

  background: #f5f5f5;

  .HeaderTop {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0rem 0.2rem;
    height: 100%;
    border-bottom: 1px solid #ccc;

    .title {
      color: #1366ec;
      font-size: 0.18rem;
      flex: 1;
      text-align: center;
      padding: 0 0.2rem;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;

      .titleText {
        color: #181818;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .titleText.action {
        color: #1366ec;
      }

      >i {
        color: #181818;
        font-size: 0.1rem;
        margin-left: 0.07rem;
      }

      >i.action {
        color: #1366ec;
      }
    }

    >i {
      font-size: 0.24rem;
      color: #797979;
    }

    :global {
      .icon-mulushu-zhankaiicon {
        color: #1366ec;
      }
    }

    .directoryTitle {
      font-size: 0.16rem;
      color: #797979;
    }

    .directoryOpen {
      font-size: 0.16rem;
      color: #1366ec;

      .selected {
        color: #1366ec;
      }
    }
  }

  .productDialog,
  .directoryTree,
  .sysnopsisDialog {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 200;
    background-color: #fff;
  }
}

.bodyHide {
  position: fixed !important;
  width: 100%;
  height: 100%;
}