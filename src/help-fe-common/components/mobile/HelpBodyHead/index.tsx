import React, { useMemo, useEffect, useState } from 'react';
import { useLocation } from 'ice';
import Header from '@/help-fe-common/components/mobile/HelpBodyHead/Header';
import { isServer } from '@/help-fe-common/utils/node/getContext';
import { extractUrlParam, isProductNode } from '@/help-fe-common/utils/global/url/urlExtract';
import styles from './index.module.scss';

const HelpBodyHead = ({ isMobileFlag, menuData }) => {
  const location = useLocation();
  const [showOutlineButton, setShowOutlineButton] = useState(false);
  const identifier = useMemo(() => extractUrlParam(location?.pathname), [location?.pathname]);

  useEffect(() => {
    if (identifier && (typeof (isMobileFlag) !== 'undefined' && isMobileFlag)) {
      const { alias } = identifier;
      // 产品首页不展示导读
      if (!isProductNode(location?.pathname, alias)) {
        setShowOutlineButton(true);
      } else {
        setShowOutlineButton(false);
      }
    }
  }, [identifier, isMobileFlag]);

  if (isServer()) {
    return (
      <div className={styles.mobileHeadBlank} />
    );
  }

  return (
    isMobileFlag &&
      <Header
        showSynopsisButton={showOutlineButton}
        menuData={menuData}
      />
  );
};


export default HelpBodyHead;
