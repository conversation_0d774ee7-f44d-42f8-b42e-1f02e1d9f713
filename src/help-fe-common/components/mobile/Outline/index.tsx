import React from 'react';
import { isEmpty } from 'lodash';
import classnames from 'classnames';
import styles from './index.module.scss';

interface IProps {
  data: any[];
  current: any;
  onOutlineClick: Function;
}

export default ({
  data,
  current,
  onOutlineClick,
}: IProps) => {
  const clickA = (id) => {
    if (onOutlineClick) {
      onOutlineClick(id);
    }
  };
  return (
    <div>
      <div className={styles.back}>
        <ul className={styles.sysnopsisDialog}>
          {
            !isEmpty(data) && data.map((item, index) => (
              <li
                key={index}
                className={classnames(styles.content, current?.id == item?.id ? styles.active : '')}
                onClick={
                  () => {
                    clickA(item?.id);
                  }
                }
              >
                {item.text}
              </li>
            ))
          }
        </ul>
      </div>
    </div>
  );
};
