.back {
  width: 100%;
  height: calc(100vh - 40px - 0.54rem);
  z-index: 100;
  background: rgba(0, 0, 0, 0.5);

  .sysnopsisDialog {
    width: 100%;
    height: auto;
    max-height: 100%;
    overflow-y: auto;
    z-index: 1000;
    background-color: #fff;
    box-shadow: 0 5px 6px 0 rgba(0, 0, 0, 0.11);

    .content {
      border-bottom: 0.01rem solid rgba(220, 220, 220, 0.39);
      height: auto;
      max-width: 100%;
      padding: 0 0.35rem;
      text-align: left;
      line-height: 0.46rem;
      font-size: 0.14rem;
      letter-spacing: 0.005rem;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      word-break: break-all;
      word-wrap: break-word;
      -webkit-box-orient: vertical;

      a {
        color: #181818;
      }
    }

    .active {
      background: #fff4ec;

      a {
        color: #1366ec;
      }
    }
  }
}