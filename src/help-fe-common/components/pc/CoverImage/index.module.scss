$scale: 1;

.imgContainer {
  position: relative;
  width: 252*$scale+px;
  height: 165*$scale+px;
  border: 1px solid rgba(237, 237, 237, 1);

  .mainImg {
    width: 100%;
    height: 100%;
  }

  .titleContainer {
    position: absolute;
    left: 0;
    bottom: 0;
    height: 25%;
    width: 100%;
    line-height: 25%;
    display: flex;
    align-items: center;

    .title {
      padding-left: 20*$scale+px;
      font-size: 14*$scale+px;
      color: #181818;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .playButton {
    position: absolute;
    transform: translate(0, -50%);
    top: 75%;
    right: 20*$scale+px;
    width: 50*$scale+px;
    height: 50*$scale+px;
    background-image: url(https://img.alicdn.com/imgextra/i4/O1CN015USRW31GyywXHOIaZ_!!6000000000692-2-tps-160-160.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    transition: all .3s ease-in-out;
  }

  &:hover {
    box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.1);

    .playButton {
      background-image: url(https://img.alicdn.com/imgextra/i3/O1CN01zSUB5J1Oyzt0WwDjW_!!6000000001775-2-tps-80-80.png);
      transition: all .3s ease-out;
    }

    .title {
      color: #1366ec;
    }
  }
}

// mobile端
@media only screen and (max-width: 1055px) {
  .imgContainer {
    width: 100%;
    max-width: 500px;
    height: auto;
  }
}