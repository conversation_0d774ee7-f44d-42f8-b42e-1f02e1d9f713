import React, { useState } from 'react';
import classNames from 'classnames';
import { onShowMenuSide } from '@/help-fe-common/utils/helpDoc/docDetail';
import styles from './index.module.scss';

export const MenuHideButton = () => {
  const [showMenu, setShowMenu] = useState(false);

  return (
    <div>
      <span
        className={classNames(styles.iconContainer, showMenu ? styles.fold : '')}
        onClick={() => { onShowMenuSide(showMenu ? 0 : 1); setShowMenu(!showMenu); }}
      >
        <i className="help-iconfont help-icon-right-arrow" />
      </span>
    </div>
  );
};
