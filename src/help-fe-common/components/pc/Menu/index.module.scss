$mobile: 1055px;

.helpMenuBox {
  height: 100%;
  display: inline-block;
  vertical-align: top;
  position: relative;
  z-index: 10;

  .helpMenuInnerBox {
    width: 300px;
    height: 100%;
    background-color: #fff;
    border-right: 1px solid #e9e9e9;

    .helpMenuDrag {
      width: 4px;
      height: 100%;
      right: -4px;
      position: absolute;
      background-color: transparent;
      border-left: 1px solid #e3e3e3;
      cursor: ew-resize;
      z-index: 1;

      &:hover {
        border-color: #d8d8d8;
      }
    }

    .helpMenu {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;

      .helpMenuTop {
        min-height: 64px;
        display: flex;
        box-sizing: border-box;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        text-decoration: none;
        padding: 0 0 0 24px;
        color: #979797;

        .menuTitle,
        div[class='help-menu-title'] {
          min-height: 64px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: #222;
          padding: 20px 0;
          font-size: 18px;
          letter-spacing: 0.4px;

          .menuTitleText,
          a[class='menu-title-text'] {
            height: 100%;
            font-size: 18px;
            color: #181818;
            text-decoration: none;

            &:hover {
              color: #1366ec;
            }
          }
        }

        .iconBox {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 24px;
          cursor: pointer;

          i {
            font-size: 22px;
          }

          .iconExpand {
            color: #1366ec;
            transform: rotate(90deg);
            transition: transform 0.3s linear;
          }

          .iconFold {
            transform: rotate(0deg);
            transition: transform 0.3s linear;
          }
        }
      }

      >div[class='help-menu-subproduct'] {
        >div[class='help-subproduct-container'] {
          height: 50px;
        }
      }

      .menuContent {
        padding: 0 27px 40px 36px;
        position: relative;
        left: 0;
        top: 0;
        right: -3px;
        bottom: 0;
        overflow-y: auto;

        li {
          width: 100%;
        }

        &::-webkit-scrollbar {
          width: 3px;
          height: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #d8d8d8;
          border-radius: 3px;
        }

        .level1,
        .level2,
        .level3,
        .level4,
        .level5,
        .level6,
        .level7 {
          height: auto;

          & ul {
            display: none;
            margin-bottom: 4px;
          }

          &.open ul {
            display: block;
          }
        }

        .level1>a {
          padding-left: 0;
        }

        .level2>a {
          padding-left: 18px;
        }

        .level3>a {
          padding-left: 30px;
        }

        .level4>a {
          padding-left: 45px;
        }

        .level5>a {
          padding-left: 60px;
        }

        .level6>a {
          padding-left: 75px;
        }

        .level7>a {
          padding-left: 90px;
        }

        .level8>a {
          padding-left: 105px;
        }

        li {
          a {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            height: auto;
            text-decoration: none;

            &:hover {

              .menuItemText,
              i {
                color: #1366ec !important;
              }
            }

            i {
              flex: 0 0 12px;
              margin-left: -12px;
              font-size: 12px;
              color: #c7c8ca;
            }
          }
        }

        // ssr渲染时，服务端会拼接目录树，对li节点进行样式适配
        ul[id='common-menu-container']>li {
          >a>span {
            flex: 1;
            height: auto;
            line-height: 24px;
            padding: 8px 10px;
            letter-spacing: 0.4px;
            font-weight: 400;
            font-size: 14px;
            color: #3e464c;
            font-weight: 500;
            vertical-align: middle;
            overflow: visible;
            word-break: break-word;
          }
        }

        .menuItemText {
          flex: 1;
          height: auto;
          line-height: 24px;
          padding: 5px 10px;
          color: #82888e;
          font-size: 13px;
          letter-spacing: 0.4px;
          font-weight: 400;
          vertical-align: middle;
          overflow: visible;
          word-break: break-word;
        }

        .level1 {
          >a>.menuItemText {
            padding: 8px 10px;
            font-size: 14px;
            color: #3e464c;
            font-weight: 500;
          }

          .level2 {
            &>a>.menuItemText {
              color: #3e464c;
            }
          }
        }

        .highlight {
          >a {
            color: #1366ec;

            .menuItemText,
            i {
              color: #1366ec !important;
            }
          }
        }
      }
    }
  }

  .productList {
    position: fixed;
    background: rgba(0, 0, 0, 0.2);
    z-index: 999;
  }
}

// 移动端
@media screen and (max-width: $mobile) {
  .helpMenuBox {
    width: 100% !important;

    .helpMenuInnerBox {
      width: 100% !important;

      .helpMenuDrag {
        display: none;
      }

      .helpMenu {
        padding-top: 20px;

        .helpMenuTop {
          display: none;
        }

        .menuContent {
          max-height: calc(100% - 73px);
        }
      }
    }
  }
}