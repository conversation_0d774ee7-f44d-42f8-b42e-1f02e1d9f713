import React, { useEffect, useRef, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { useHistory } from 'ice';
import classnames from 'classnames';
import { getFilterMenuList } from '@/help-fe-common/utils/helpDoc/filterList';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import { aLinkTrigger } from '@/help-fe-common/utils/global/url/aLinkTrigger';
import { setLocalSearchValue } from '@/help-fe-common/utils/helpDoc/searchStorage';
import isMobile from '@/help-fe-common/utils/global/isMobile';
import styles from './index.module.scss';

const DEFAULT_INDEX = NaN;

const MenuSearch = ({ newTreeList, dataSpm, onClose }) => {
  const intl = useIntl();
  const history = useHistory();

  const [searchHighlight, setSearchHighlight] = useState(false);
  const [searchShow, setSearchShow] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [filteredResult, setFilteredResult] = useState<any[]>([]);
  const [enterFlag, setEnterFlag] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(DEFAULT_INDEX);
  const currentRef = useRef(currentIndex);
  const resultRef: any = useRef(null);
  const inputRef: any = useRef(null);
  // 目录搜索
  const onInputChange = (e) => {
    const value = e?.target?.value;
    setSearchValue(value);
    const cleanValue = value?.trim();
    if (!cleanValue) {
      setSearchShow(false);
      setFilteredResult([]);
      return;
    }
    setSearchShow(true);
    const filtered = getFilterMenuList(value, newTreeList);
    setFilteredResult(filtered);
    sendSlsLog({ page: '', section: 'menuTree', action: 'menuSearch', userParams1: 'keywords', userParams2: value });
  };

  const onUrlClick = (e, url) => {
    e.preventDefault();
    aLinkTrigger(e, history);
    sendSlsLog({ page: '', section: 'menuTree', action: 'menuSearch', userParams1: 'onClick', userParams2: url });
    // 移动端点击行为后需要隐藏目录组件
    isMobile() && onClose && onClose(false);
  };

  const triggerSuggestClick = (e) => {
    e.preventDefault();
    setLocalSearchValue(searchValue, 'menu');
    const clickDOM = document.querySelector('.help-search-btn') as HTMLElement;
    clickDOM && clickDOM?.click();
  };

  /**
   * 键盘监听事件
   * @param event
   */
  const listenerKeydown = (event) => {
    if (event.key === 'ArrowDown') {
      setCurrentIndex((preCurrent) => {
        let current = preCurrent;
        if (isNaN(preCurrent) || preCurrent === filteredResult?.length - 1) {
          current = 0;
        } else {
          current = preCurrent + 1;
        }
        scrollIntoView(current);
        return current;
      });
    } else if (event.key === 'ArrowUp') {
      setCurrentIndex((preCurrent) => {
        let current = preCurrent;
        if (isNaN(preCurrent) || preCurrent === 0) {
          current = filteredResult?.length - 1;
        } else {
          current = preCurrent - 1;
        }
        scrollIntoView(current);
        return current;
      });
    } else if (event.key === 'Enter') {
      setEnterFlag(true);
    }
  };

  /**
   * 滚动至当前
   * @param viewIndex
   * @returns
   */
  const scrollIntoView = (viewIndex) => {
    const currentItemNode = resultRef?.current?.childNodes?.[viewIndex];
    if (!currentItemNode) return;
    resultRef?.current?.scrollTo({
      top: currentItemNode?.offsetTop - 30,
    });
  };

  useEffect(() => {
    currentRef.current = currentIndex;
  }, [currentIndex]);

  useEffect(() => {
    if (enterFlag) {
      const currentItemNode = resultRef?.current?.childNodes?.[currentRef.current];
      if (currentItemNode) {
        currentItemNode?.click && currentItemNode?.click();
        inputRef?.current && inputRef?.current?.blur && inputRef?.current?.blur();
      }
      setEnterFlag(false);
    }
  }, [enterFlag]);

  useEffect(() => {
    if (!(filteredResult?.length > 0)) return;
    window.addEventListener('keydown', listenerKeydown);
    return () => {
      setCurrentIndex(DEFAULT_INDEX);
      window.removeEventListener('keydown', listenerKeydown);
    };
  }, [filteredResult?.length]);

  return (
    <div className={styles.collapseActionBar}>
      <div className={styles.searchContainer}>
        <div className={styles.searchInputWrapper} style={searchHighlight ? { borderBottom: '1px solid #1366ec' } : {}}>
          <i className="help-iconfont help-icon-sousuoicon" style={searchHighlight ? { color: '#1366ec' } : {}} />
          <input
            onChange={(e) => onInputChange(e)}
            placeholder={intl.formatMessage({ id: 'help.search.menuTree' })}
            className={styles.searchInput}
            onFocus={() => {
              sendSlsLog({ page: '', section: 'menuTree', action: 'menuSearch', userParams1: 'onFocus' });
              setSearchHighlight(true);
            }}
            onBlur={(e) => {
              e.target.value = '';
              setSearchHighlight(false);
              setTimeout(() => {
                setSearchShow(false);
                setFilteredResult([]);
              }, 300);
            }}
            ref={inputRef}
          />
        </div>
        {
          searchShow ?
            <div className={styles.resultContainer} ref={resultRef} data-spm={dataSpm}>
              {
                filteredResult?.length ?
                  filteredResult?.map((item, index) => {
                    return (
                      <div
                        key={index}
                        data-spm={`d_${index}`}
                        data-tracker-scm={item?.scm || ''}
                        className={classnames(styles.resultItem, index === currentIndex ? styles.highlightResult : '')}
                        data-href={item?.url || `/document_detail/${item?.id}.html`}
                        onClick={(e) => { onUrlClick(e, item?.url || `/document_detail/${item?.id}.html`); }}
                      >
                        <span dangerouslySetInnerHTML={{ __html: item?.title }} />
                      </div>
                    );
                  }) :
                  <div className={styles.resultNoData}>
                    <FormattedMessage id="help.search.nodata" />
                    <a href="" onClick={(e) => { triggerSuggestClick(e); }}><FormattedMessage id="help.search.menuTip" /></a>
                  </div>
              }
            </div> :
            null
        }
      </div>
    </div>
  );
};

export default MenuSearch;
