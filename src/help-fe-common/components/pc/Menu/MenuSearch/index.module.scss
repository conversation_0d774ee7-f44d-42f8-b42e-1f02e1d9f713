$mobile: 1055px;

.collapseActionBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 5px 0 15px 0;

  .searchContainer {
    flex: 1;
    margin: 0 24px;
    outline: none;
    position: relative;

    .searchInputWrapper {
      width: 100%;
      height: 28px;
      line-height: 28px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 5px;
      padding-bottom: 6px;
      border-bottom: 1px solid rgba(213, 213, 213, 0.54);

      i {
        font-size: 12px;
        color: #979797;
        vertical-align: sub;
        float: right;
      }

      .searchInput {
        padding-left: 10px;
        height: 24px;
        width: 92%;
        color: #3d3d3d;
        outline: none;
        border: none;
        line-height: 24px;
        background: transparent;
        font-size: 12px;
        letter-spacing: 0;

        &::-webkit-input-placeholder {
          color: #979797;
        }
      }
    }

    .resultContainer {
      position: absolute;
      width: 194px;
      min-height: 40px;
      max-height: 300px;
      padding: 5px 0;
      overflow: auto;
      background-color: #fff;
      box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.10);
      border-radius: 0 0 2px 2px;
      z-index: 100;

      &::-webkit-scrollbar {
        width: 3px;
        height: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #d8d8d8;
        border-radius: 3px;
      }

      .resultItem {
        width: 100%;
        height: auto;
        line-height: 20px;
        padding: 6px 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        word-break: break-all;
        word-wrap: break-word;

        span {
          font-size: 12px;
        }

        em {
          color: #1366ec;
          font-style: normal;
        }

        &:hover {
          cursor: pointer;
          color: #1366ec;
          background-color: #f5f5f6;
        }
      }

      .highlightResult {
        background-color: #f5f5f6;
      }

      .resultNoData {
        padding: 20px;
        font-size: 12px;
        color: #979797;
        letter-spacing: 0;
        line-height: 24px;

        a {
          color: #1366ec;
        }
      }
    }

    &:hover {
      .searchInputWrapper {
        border-bottom: 1px solid #1366ec;

        i {
          color: #1366ec;
        }
      }
    }
  }
}

// 移动端
@media screen and (max-width: $mobile) {
  .collapseActionBar {
    .searchContainer {
      .resultContainer {
        width: calc(100% - 60px);
      }
    }
  }
}