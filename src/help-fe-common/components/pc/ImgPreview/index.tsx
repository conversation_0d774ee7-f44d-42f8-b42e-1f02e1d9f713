import React, { useEffect, useRef, useState } from 'react';
import { computeImgStyle } from '@/help-fe-common/utils/global/previewImg';
import styles from './index.module.scss';

interface IProps {
  data: { width: number; height: number; src: string };
  onClose: () => void;
}

export default ({ data, onClose }: IProps) => {
  const [mouseDownInitPos, setMouseDownInitPos] = useState({ initX: 0, initY: 0 });
  const [mouseDownPos, setMouseDownPos] = useState({ x: 0, y: 0 });
  const [mouseDownFlag, setMouseDownFlag] = useState(false);
  const [zoomIn, setZoomIn] = useState(true);
  const [imgDrag, setImgDrag] = useState(false);
  const imgZoom: any = useRef(null);
  const scaleRatio = 2;

  const zoom = (e) => {
    e.stopPropagation();
    const imgCur = imgZoom.current;
    if (zoomIn && !imgDrag) {
      imgCur.style.transform = `scale(${scaleRatio})`;
      setZoomIn(false);
    } else if (!imgDrag) {
      imgCur.style.transform = 'scale(1)';
      // 缩小后保持居中
      imgCur.style.left = '';
      imgCur.style.top = '';
      setZoomIn(true);
    }
  };

  const handleMouseDown = (e) => {
    e.stopPropagation();
    e.preventDefault();
    const { clientX, clientY } = e;
    setMouseDownFlag(true);
    setMouseDownPos({
      x: clientX,
      y: clientY,
    });
    setMouseDownInitPos({
      initX: clientX,
      initY: clientY,
    });
  };

  const handleMouseMove = (e) => {
    e.stopPropagation();
    e.preventDefault();
    if (zoomIn) return;
    const { clientX, clientY } = e;
    const diffX = clientX - mouseDownPos.x;
    const diffY = clientY - mouseDownPos.y;
    if ((!mouseDownFlag) || (diffX === 0 && diffY === 0)) {
      return;
    }
    const { offsetLeft, offsetTop } = imgZoom.current as HTMLImageElement;
    const offsetX = parseInt(`${diffX + offsetLeft}`, 10);
    const offsetY = parseInt(`${diffY + offsetTop}`, 10);
    setMouseDownPos({
      x: clientX,
      y: clientY,
    });
    imgZoom.current.style.left = `${offsetX}px`;
    imgZoom.current.style.top = `${offsetY}px`;
  };

  const handleMouseUp = (e) => {
    e.stopPropagation();
    e.preventDefault();
    const { clientX, clientY } = e;
    setMouseDownFlag(false);
    if (clientX !== mouseDownInitPos.initX && clientY !== mouseDownInitPos.initY) {
      setImgDrag(true);
    } else {
      setImgDrag(false);
    }
  };

  useEffect(() => {
    document.onmouseover = () => {
      if (mouseDownFlag) {
        setMouseDownFlag(false);
      }
    };
    return () => {
      document.onmouseover = null;
    };
  }, [mouseDownFlag]);

  // 监听esc键退出图片预览
  useEffect(() => {
    const keyDownHandler = (e) => {
      if (e.key === 'Escape') {
        e.preventDefault();
        onClose();
      }
    };
    document.addEventListener('keydown', keyDownHandler);
    return () => {
      document.removeEventListener('keydown', keyDownHandler);
    };
  }, []);

  // 针对解决safari的overflow：hidden失效的问题
  const body = useRef(document.getElementsByTagName('body'));
  useEffect(() => {
    if (/Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent)) {
      body.current[0].style.overflowY = 'hidden';
      body.current[0].style.overscrollBehavior = 'contain';
    } else {
      body.current[0].style.overflowY = 'hidden';
    }

    return () => {
      body.current[0].style.overflowY = '';
      body.current[0].style.overscrollBehavior = '';
    };
  }, [body]);

  return (
    <div
      className="img-back"
      id="img-container"
      onClick={onClose}
    >
      <img
        src={data.src}
        ref={imgZoom}
        className="img-float"
        draggable="false"
        style={computeImgStyle(data, zoomIn)}
        onClick={zoom}
        onPointerDown={handleMouseDown}
        onPointerMove={handleMouseMove}
        onPointerUp={handleMouseUp}
      />
      <button className={styles.button} onClick={onClose}>
        <i className="help-iconfont help-icon-quxiao" />
      </button>
    </div>
  );
};
