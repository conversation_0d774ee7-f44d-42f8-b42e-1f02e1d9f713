.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.6);
  z-index: 200;

  .videoContainer {
    position: relative;
    width: 50vw;
    max-width: 800px;
    min-width: 600px;

    .close {
      position: absolute;
      right: 0;
      top: 0;
      z-index: 1;
      display: inline-block;
      padding: 4px;
      cursor: pointer;

      i {
        color: #999;
        background: rgba(255, 255, 255, 0.3);
      }
    }

    video {
      width: 100%;
    }
  }
}

// mobile端
@media only screen and (max-width: 1055px) {
  .overlay {
    padding: 12px;

    .videoContainer {
      width: 100%;
      min-width: auto;
    }
  }
}