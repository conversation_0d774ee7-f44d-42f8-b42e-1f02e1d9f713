import React from 'react';
import styles from './index.module.scss';

export default ({ url, onCloseClick: propsOnCloseClick }) => {
  const onCloseClick = () => {
    if (propsOnCloseClick) {
      propsOnCloseClick();
    }
  };

  return (
    <div className={styles.overlay}>
      <div className={styles.videoContainer}>
        <span className={styles.close} onClick={onCloseClick}>
          <i className="help-iconfont help-icon-delete" style={{ cursor: 'pointer' }} />
        </span>
        <video
          src={url}
          controls
          controlsList="nodownload"
          autoPlay
        />
      </div>
    </div>
  );
};
