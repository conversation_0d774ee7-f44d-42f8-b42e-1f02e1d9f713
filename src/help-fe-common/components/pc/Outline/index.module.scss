@import '../../../styles/variables.scss';

.synopsisBox {
  color: #999;
  font-size: 14px;
  padding: 20px 24px;
  max-height: calc(100vh - var(--default-head-height));
  height: calc(100vh - var(--default-head-height));
  overflow-x: hidden;
  overflow-y: auto;
  overscroll-behavior: none;

  &::-webkit-scrollbar {
    width: 3px;
    height: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #d8d8d8;
    border-radius: 3px;
  }

  .synopsisTitle {
    height: 26px;
    font-weight: 500;
    line-height: 26px;
    font-size: 18px;
    color: #181818;
    margin-bottom: 10px;
  }

  .docTip {
    font-size: 12px;
    color: #fff;
  }

  .synopsisItem {
    height: auto;
    max-width: 100%;
    cursor: pointer;
    padding: 4px;
    overflow: hidden;
    line-height: 22px;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    word-break: break-all;
    word-wrap: break-word;
    -webkit-box-orient: vertical;

    a {
      text-decoration: none;
      color: #999;

      &:target {
        padding-top: 40px;
      }
    }
  }

  .itemContainer {
    height: auto;
    max-width: 100%;

    .synopsisItem1 {
      display: flex;
      align-items: center;

      .synopsisH2Mark {
        height: 4px;
        width: 4px;
        display: inline-block;
        margin-right: 6px;
        border-radius: 100%;
        background: rgba($color: #979797, $alpha: 0.8);
      }

      .synopsisH2 {
        flex: 1;
        cursor: pointer;
        padding: 3px;
        margin: 3px;
        overflow: hidden;
        line-height: 22px;
        text-overflow: ellipsis;
        display: inline-block;
        -webkit-line-clamp: 3;
        word-wrap: break-word;
        word-break: break-word;
        -webkit-box-orient: vertical;

        span {
          text-decoration: none;
          color: #666;

          &:target {
            padding-top: 40px;
          }
        }

        &:hover {
          span {
            color: $text-link-color;
          }
        }
      }

      .active {

        span {
          color: $text-link-color;
        }
      }

      .markActive {
        background-color: $text-link-color;
      }
    }

    .synopsisItem2 {
      display: table;
      width: 100%;
      line-height: 0;

      .synopsisH3Mark {
        width: 1px;
        display: table-cell;
        background-color: #eaeaea;
        text-align: center;
      }

      .synopsisH3 {
        display: inline-block;
        width: calc(100% - 16px);
        height: auto;
        line-height: 16px;
        margin: 4px 4px 4px 12px;
        padding: 4px 4px 4px 12px;
        cursor: pointer;
        font-size: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 3;
        word-wrap: break-word;
        word-break: break-word;

        span {
          color: #999;

          &:target {
            padding-top: 40px;
          }
        }

        &:hover {
          span {
            color: $text-link-color;
          }
        }
      }

      .active {

        span {
          color: $text-link-color;
        }
      }

      .markActive {
        background-color: $text-link-color;
      }
    }
  }
}