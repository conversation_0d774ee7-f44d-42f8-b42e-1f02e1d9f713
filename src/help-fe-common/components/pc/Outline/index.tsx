import React from 'react';
import classnames from 'classnames';
import { FormattedMessage } from 'react-intl';
import { clickToHash } from '@/help-fe-common/utils/helpDoc/docOutline';
import styles from './index.module.scss';

interface IProps {
  data: any;
  currentId: number;
  docSourceInfo?: any; // 文档承接信息及翻译来源
  onClick: Function;
}

export default ({ data, currentId, docSourceInfo, onClick }: IProps) => {
  const onTitleClick = (id) => {
    onClick(id);
    clickToHash(id);
  };

  return (
    <ul className={classnames(styles.synopsisBox)}>
      {
        data?.length > 0 &&
        <>
          <li className={styles.synopsisTitle}>
            <FormattedMessage id="help.doc.synopsis.title" />
            {
              docSourceInfo &&
              <span className={styles.docTip}>{` （${docSourceInfo}）` || ''}</span>
            }
          </li>
          <div className={styles.itemContainer}>
            {
              data?.map((item, index) => (
                <div key={index}>
                  <div className={styles.synopsisItem1}>
                    <li className={classnames(styles.synopsisH2Mark, currentId === item?.id ? styles.markActive : '')} />
                    <li
                      className={classnames(styles.synopsisH2, currentId === item?.id ? [styles.active, 'current-synopsis'] : '')}
                      onClick={() => {
                        onTitleClick(item?.id);
                      }}
                    >
                      <span>{item?.text}</span>
                    </li>
                  </div>
                  {
                    item?.children?.length ?
                      (
                        item?.children?.map((child, childIndex) => (
                          <div className={styles.synopsisItem2} key={childIndex}>
                            <li className={classnames(styles.synopsisH3Mark, currentId === child?.id ? styles.markActive : '')} />
                            <li
                              className={classnames(styles.synopsisH3, currentId === child?.id ? [styles.active, 'current-synopsis'] : '')}
                              onClick={() => {
                                onTitleClick(child?.id);
                              }}
                            >
                              <span>{child?.text}</span>
                            </li>
                          </div>
                        ))
                      )
                      :
                      null
                  }
                </div>
              ))
            }
          </div>
        </>
      }
    </ul>
  );
};
