/* eslint-disable no-extend-native */
import React from 'react';
import { Helmet } from 'react-helmet';
import { runApp, IAppConfig } from 'ice';
import LocaleProvider from '@/help-fe-common/components/common/LocalProvider';
import { getLocale } from '@/help-fe-common/utils/global/locale';
import '@/help-fe-common/utils/global/flexible'; // 设置根节点 font-size
import { isServer } from '@/help-fe-common/utils/node/getContext';
import { sendAemLog } from '@/help-fe-common/utils/global/track/aem'; // 引入aem
import { setDocumentScrollHeight } from '@/help-fe-common/utils/helpDoc/docDetail';
import { setMenuTreeInfo } from '@/help-fe-common/utils/helpDoc/menu';
import { loadWaterMark } from '@/utils/loadWaterMark';

if (isServer()) {
  Helmet.canUseDOM = false;
  // 解决Node console 错误
  React.useLayoutEffect = React.useEffect;
} else {
  // 浏览器环境设置
  setDocumentScrollHeight();
  setMenuTreeInfo();
  loadWaterMark();
}

sendAemLog();
const locale = getLocale();
Promise.prototype.finally = Promise.prototype.finally || {
  finally(fn) {
    const onFinally = (callback) => Promise.resolve(fn()).then(callback);
    // @ts-ignore
    return this.then(
      (result) => onFinally(() => result),
      (reason) => onFinally(() => Promise.reject(reason)),
    );
  },
}.finally;

const appConfig: IAppConfig = {
  app: {
    rootId: 'app',
    addProvider: (({ children }) => (
      <LocaleProvider locale={locale}>
        {children}
      </LocaleProvider>
    )),
  },
  router: {
    type: 'browser',
    fallback: <div />,
  },
  request: {
    baseURL: '/',
    interceptors: {
      request: {
        onConfig: (config) => {
          return config;
        },
        onError: (error) => {
          return Promise.reject(error);
        },
      },
      response: {
        onConfig: (response) => {
          return response;
        },
      },
    },
  },
};

runApp(appConfig);
