import { tutorialClassList } from '@/constants/tutorial';

export interface ITutorial {
  manual: {
    totalTime: number;
    url?: string;
    category: string;
    steps: IStep[];
  };
  onestop?: {
    totalTime: number;
    category: string;
    steps: IStep[];
  };
  pipCode: string;
}

export interface IStep {
  label: string; // 步骤标志
  title: string; // 标题
  id: string; // section id
  className: string; // section类名
  stepTime?: number; // 每一步骤耗时，允许为空
  index: number; // 数组序号
}

export class TutorialProcessor {
  /**
   * 文档html内容，从中解析教程相关数据
   */
  content?: string;

  constructor(content?: string) {
    this.content = content;
  }

  /**
   * 获取一键配置、手动配置内容数据
   * @returns {ITutorial}
   */
  extractTutorialData(): ITutorial {
    const result: ITutorial = {
      manual: {
        totalTime: 0,
        url: '',
        category: '',
        steps: [],
      },
      pipCode: '',
    };
    const parser = new DOMParser();
    const dom = parser?.parseFromString(this.content || '', 'text/html');
    tutorialClassList.forEach((item) => {
      const data = this.getTutorialData(dom, item.className, item?.category);
      data && (result[item?.label] = data);
    });
    result.pipCode = this.getPipCode(dom);
    return result;
  }

  /**
   * 移除时间显示相关节点
   * @param {HTMLElement} node html节点
   * @param className 时间p标签类名
   */
  removeTimeNode(node: HTMLElement, className: string): void {
    const timeNodeList = node?.querySelectorAll(className) || [];
    timeNodeList?.forEach((timeNode) => {
      const parentNode = timeNode?.parentNode as HTMLElement;
      if (parentNode?.tagName === 'P') {
        parentNode?.remove();
      } else {
        timeNode && timeNode.remove();
      }
    });
  }

  /**
   * 获取单个教程数据
   * @param dom html文档dom
   * @param className 教程类名
   * @param category 教程分类
   * @returns
   */
  private getTutorialData(dom: HTMLElement|Document, className: string, category: string) {
    const targetDOM = dom.querySelector(className);
    if (!targetDOM) return null;
    const totalTime = this.getTime(targetDOM as HTMLElement, 'p.time>.ph, .ph.time');
    const url = this.getUrl(targetDOM as HTMLElement, '.ph.url');
    // 筛选条件为第一层且有outputclass属性
    const stepNodeList = targetDOM?.querySelectorAll(':scope>section.section[outputclass]');
    const steps = Array.prototype.map.call(stepNodeList, (stepNode, index) => {
      return this.getStepData(stepNode, index);
    });
    return { totalTime, url, steps, category };
  }

  /**
   * 获取教程pipCode
   */
  private getPipCode(dom: HTMLElement|Document) {
    const pipCodeNode = dom.querySelector('p.pipcode') as HTMLElement;
    if (!pipCodeNode) return '';
    const pipCode = pipCodeNode?.innerText || '';
    return pipCode;
  }

  /**
   * 获取教程每个步骤相关数据
   * @param {HTMLElement} node section节点
   * @param {number} index section序号
   * @returns {IStep}
   */
  private getStepData(node: HTMLElement, index: number): IStep {
    if (!node) return [] as any;
    const label = node.getAttribute('outputclass') || '';
    const title = node.querySelector('h3')?.innerText || '';
    const id = node.getAttribute('id') || '';
    const className = node.getAttribute('class') || '';
    const stepTime = this.getTime(node, 'p.steptime>.ph, .ph.steptime');
    const stepResult: IStep = { label, title, id, className, stepTime, index };
    return stepResult;
  }

  /**
   * 获取步骤耗时
   * @param {HTMLElement} node section单节点
   * @param {string} className 时间p标签类名
   * @returns {number} time
   */
  private getTime(node: HTMLElement, className: string): number {
    const timeNode = node.querySelector(className) as HTMLElement;
    const time = Number(timeNode?.innerText) || 0;
    return time;
  }

  /**
   * 获取解决方案链接
   * @param {HTMLElement} node section单节点
   * @param {string} className 时间p标签类名
   * @returns {string} url
   */
  private getUrl(node: HTMLElement, className: string): string {
    const targetNode = node.querySelector(className) as HTMLElement;
    return targetNode?.innerText || '';
  }
}
