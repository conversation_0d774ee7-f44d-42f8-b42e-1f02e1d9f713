import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';

interface IExamConfig {
  manual: {
    examNum: number;
    examFinishedArr: string[];
  };
  onestop?: {
    examNum: number;
    examFinishedArr: string[];
  };
}

/* eslint-disable no-param-reassign */
const tutorialClass = '.help-tutorial-container .markdown-body';

const examConfig: IExamConfig = {
  manual: {
    examNum: 0,
    examFinishedArr: [],
  },
  onestop: {
    examNum: 0,
    examFinishedArr: [],
  },
};

/**
 * 生成radio节点
 * @param value 值
 * @param answer 正确答案
 * @param node 需要处理的节点
 * @param keyId 唯一值区分不同问题组别
 * @param content 答案解析
 * @param index 考题序号
 * @param mode 教程模式：手动配置版、一键配置版
 * @param docId 文档id
 */
const generateRadio = (value, answer, node, keyId, content, index, mode, docId) => {
  const itemClick = (e) => {
    e.stopPropagation();
    const _this = e.currentTarget;
    _this.querySelector('input')?.click();
  };
  // radio标签设置
  const inputNode = document.createElement('input');
  inputNode?.setAttribute('type', 'radio');
  inputNode?.setAttribute('name', keyId);
  inputNode?.setAttribute('id', value);
  inputNode?.addEventListener('click', (e) => {
    e?.stopPropagation();
    const radioNode = e.target as any;
    const ulNode = radioNode?.parentNode?.parentNode;
    const ulId = ulNode?.getAttribute('id');
    const pNode = ulNode?.nextElementSibling;
    const examNum = examConfig?.[mode]?.examNum;
    if (radioNode?.id === answer) {
      pNode.innerText = `回答正确：${content}`;
      pNode.setAttribute('class', 'rightExplain');
      sendSlsLog({ page: 'tutorialDetail', section: 'exam', action: 'rightAnswer', userParams1: mode, userParams2: examNum, docId });
    } else {
      pNode.innerText = `回答错误：${content}`;
      pNode.setAttribute('class', 'wrongExplain');
      sendSlsLog({ page: 'tutorialDetail', section: 'exam', action: 'wrongAnswer', userParams1: mode, userParams2: examNum, docId });
    }
    // 每题仅允许点击一次
    ulNode?.classList?.add('help-tutorial-test-forbidden');
    if (mode === 'manual') {
      const finishedArr = examConfig.manual.examFinishedArr;
      if (!ulId || finishedArr?.includes(ulId)) return;
      examConfig.manual.examFinishedArr?.push(ulId);
    } else {
      if (!ulId || !examConfig?.onestop || examConfig?.onestop.examFinishedArr?.includes(ulId)) return;
      examConfig?.onestop.examFinishedArr?.push(ulId);
    }
  });
  // label标签设置
  const labelNode = document.createElement('label');
  labelNode?.setAttribute('htmlFor', value);
  labelNode.textContent = value;
  node.innerHTML = '';
  node?.appendChild(inputNode);
  node?.appendChild(labelNode);
  node?.addEventListener('click', itemClick);
};

/**
 * 计算已完成的考题
 */
const computeExam = (sectionNode: HTMLElement, mode) => {
  const examNum = sectionNode?.querySelectorAll('ul')?.length;
  if (mode === 'manual') {
    examConfig.manual.examNum = examNum;
  } else {
    examConfig.onestop && (examConfig.onestop.examNum = examNum);
  }
};

const getExamOptions = (sectionNode: HTMLElement, mode: string, docId: string) => {
  computeExam(sectionNode, mode);
  const itemList = sectionNode?.querySelectorAll('li');
  Array.from(itemList)?.forEach((node, index) => {
    const text = node?.querySelector('p')?.innerText || '';
    let answer = '';
    if (node?.classList?.contains('right')) {
      answer = text;
    }
    const ulNode = node?.parentNode as HTMLElement;
    const keyId = ulNode?.getAttribute('id') || `li-${index}`;
    const content = (ulNode?.nextElementSibling as HTMLElement)?.innerText;
    generateRadio(text, answer, node, keyId, content, index, mode, docId);
  });
};

const process = ({ data }) => {
  const tutorialDOM = document.querySelector(tutorialClass);
  if (!tutorialDOM) return;
  const manualSectionNode = tutorialDOM?.querySelector('.manual .sum>.test') as HTMLElement;
  const onestopSectionNode = tutorialDOM?.querySelector('.onestop .sum>.test') as HTMLElement;
  manualSectionNode && getExamOptions(manualSectionNode, 'manual', data?.nodeId);
  onestopSectionNode && getExamOptions(onestopSectionNode, 'onestop', data?.nodeId);
};

export default [process];
