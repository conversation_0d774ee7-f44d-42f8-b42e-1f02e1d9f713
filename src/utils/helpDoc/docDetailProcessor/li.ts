const tutorialClass = '.help-tutorial-container .markdown-body';

const isFlex = (node) => {
  const children = node?.children;
  // 判断前两个子节点是否为section
  return children?.length > 0 && Array.from(children)?.filter((item: HTMLElement, index) => {
    return (index === 0 || index === 1) && item?.tagName === 'SECTION';
  })?.length === 2;
};

/**
 * 判断是否需要flex布局，并设置类名
 * @param node 节点DOM
 */
const setFlex = (node) => {
  const children = node?.children;
  const isFlexNode = isFlex(node);
  // 对flex布局的子元素用新的div标签包裹，避免display:flex影响li标签中序号展示
  if (!isFlexNode) return;
  const newDiv = document.createElement('div');
  newDiv?.classList?.add('help-tutorial-flex');
  Array.from(children)?.forEach((item: any) => {
    // 取section节点放入newDiv对象进行flex布局处理
    if (!(item && item?.tagName === 'SECTION')) return;
    newDiv?.appendChild(item);
  });
  node.insertBefore(newDiv, node.firstChild);
};

const process = () => {
  const tutorialDOM = document.querySelector(tutorialClass);
  if (!tutorialDOM) return;
  const liNodeList = tutorialDOM?.querySelectorAll('li');
  liNodeList?.length && liNodeList?.forEach((liNode: HTMLElement) => {
    const { children } = liNode;
    // li标签下子节点数量大于1时，判断前两个子节点是否为section，数量为1时取二级子节点判断
    if (children?.length >= 2) {
      setFlex(liNode);
    } else if (children?.length === 1) {
      setFlex(children?.[0]);
    }
  });
};

export default [process];
