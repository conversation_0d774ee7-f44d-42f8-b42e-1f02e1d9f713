import aPlugin from '@/help-fe-common/plugins/docDetailProcessor/a';
import apiPlugin from '@/help-fe-common/plugins/docDetailProcessor/api';
import highlightCodePlugin from '@/help-fe-common/plugins/docDetailProcessor/highlightCode';
import collapsePlugin from '@/help-fe-common/plugins/docDetailProcessor/collapse';
import copyPlugin from '@/help-fe-common/plugins/docDetailProcessor/copy';
import prePlugin from '@/help-fe-common/plugins/docDetailProcessor/pre';
import selectionPlugin from '@/help-fe-common/plugins/docDetailProcessor/selection';
import tablePlugin from '@/help-fe-common/plugins/docDetailProcessor/table';
import codeTabPlugin from '@/help-fe-common/plugins/docDetailProcessor/codeTab';
import videoPlugin from '@/help-fe-common/plugins/docDetailProcessor/video';
import imgPlugin from '@/help-fe-common/plugins/docDetailProcessor/img';
import contentTabPlugin from '@/help-fe-common/plugins/docDetailProcessor/contentTab';
import notePlugin from '@/help-fe-common/plugins/docDetailProcessor/note';
import audioPlugin from '@/help-fe-common/plugins/docDetailProcessor/audio';
import fixedTh from '@/help-fe-common/plugins/docDetailProcessor/fixedTh';
import virtualListPlugin from '@/help-fe-common/plugins/docDetailProcessor/virtualList';
import tableNoBorder from '@/help-fe-common/plugins/docDetailProcessor/tableNoBorder';
// 纯净文档插件
import pureAClick from '@/help-fe-common/plugins/docDetailProcessor/pureAClick';
// 教程相关插件
import liPlugin from './li'; // 对教程文档中li标签处理后采用flex布局
import examPlugin from './exam'; // 教程文档中考试模块
// 一键部署插件
import helplabPlugin from './helplab';
import deployPlugin from './deploy';
// 主题页插件
import topicAPlugin from '@/help-fe-common/plugins/topicDetailProcessor/a';

/**
 * 公共插件组
 */
const commonPlugins = [
  collapsePlugin, // 尽可能提前执行
  highlightCodePlugin,
  prePlugin,
  copyPlugin,
  tablePlugin,
  codeTabPlugin,
  contentTabPlugin,
  imgPlugin,
  notePlugin,
  videoPlugin,
  tableNoBorder,
  fixedTh,
  audioPlugin,
];

/*
 * 普通文档pc端插件组
 */
const pcPlugins = [
  ...commonPlugins,
  aPlugin,
  apiPlugin,
  helplabPlugin,
  deployPlugin,
];

/*
 * 普通文档移动端端插件组
 */
const mobilePlugins = [
  ...commonPlugins,
  aPlugin,
  virtualListPlugin, // 大文档优化
];

/*
 * 纯净文档插件组
 */
const pureDocPlugins = [
  ...commonPlugins,
  pureAClick,
];

/*
 * 云主题文档插件组
 */
const topicPlugins = [
  ...commonPlugins,
  topicAPlugin,
];

/*
 * 教程文档插件组
 */
const tutorialPlugins = [
  ...commonPlugins,
  aPlugin,
  liPlugin,
  examPlugin,
];

export { pcPlugins, mobilePlugins, topicPlugins, tutorialPlugins, pureDocPlugins };
