import { onDeployClick } from '@/components/QuickDeploy/utils/renderDeploy';

/**
* 绑定一键部署按钮点击事件
* @param buttonItem 需要绑定的按钮节点
* @param docInfo 文档内容
*/
export const bindDeployButtonClick = (buttonItem: HTMLElement, docInfo) => {
  buttonItem?.addEventListener('click', () => {
    const { deploys, title, nodeId } = docInfo;
    const deployId = buttonItem?.getAttribute('deploy-id') || buttonItem?.getAttribute('deployId') || '';
    const deployItem = deploys?.find((item) => item?.id === deployId);
    if (!deployItem) return;
    onDeployClick(deployItem, title, nodeId);
  });
};

/**
 * 插件运行逻辑
 * @param params
 */
const process = (params) => {
};

const bindEvent = (params) => {
  // 代码快速部署
  document.querySelectorAll('.unionContainer .markdown-body .help-deploy-button').forEach(
    (buttonItem: HTMLElement) => {
      if (!buttonItem) return;
      bindDeployButtonClick(buttonItem, params?.data);
    },
  );
};

export default [process, bindEvent];
