import { extractUrl } from './extractUrl';
import { showOverlay } from './overlay';
import { popupDialog } from '@/help-fe-common/utils/global/prompt';
import { setLocalStorageItem } from '@/help-fe-common/utils/global/localStorage';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';

/**
 * 向cloudshell发送消息运行脚本
 * @param ossUrl 文档转换后的oss链接
 * @param docName 文档名称，用于重命名oss链接
 */
export function openShell(ossUrl, docName) {
  // iframe 为嵌入 Cloud Shell 的 iframe
  const iframe = document.getElementById('cloudshell-iframe');
  // @ts-ignore iframe提示忽略
  iframe?.contentWindow.postMessage(JSON.stringify({
    action: 'paste',
    command: `aliyun-help-lab "${docName}" ${ossUrl}\r`, // 需要粘贴的命令
  }), 'https://shell.aliyun.com?openPureMode=true');
}

/**
 * 打开jupyter链接
 * @param text cloudshell消息中返回文本内容
 * @param nodeId 文档id
 * @param newOpen 是否新开窗口，true新开窗口，false当前页打开
 */
export function openJupyterNoteBook(text, nodeId, newOpen) {
  const { port, token } = extractUrl(text);
  const url = `https://shell.aliyun.com/preview?port=${port}&token=${token}`;
  if (port && token) {
    const expire = 30 * 60 * 1000;
    setLocalStorageItem('jupyterUrl', JSON.stringify({ nodeId, text }), expire);
    if (newOpen) {
      window.open(url);
      showOverlay(false, 0);
    } else {
      window.location.href = url;
    }
    sendSlsLog({ page: 'documentDetail', section: 'helpLab', action: 'runSuccess', userParams1: nodeId, userParams2: text });
  } else {
    popupDialog({ title: '运行失败，请刷新页面后重试', isShowButton: false, isSuccess: false });
    sendSlsLog({ page: 'documentDetail', section: 'helpLab', action: 'runFailed', userParams1: nodeId, userParams2: text });
  }
}

/**
 * 处理cloudshell message消息
 * @param e event对象
 * @param ossUrl oss链接
 * @param nodeId 文档id
 * @param docName 文档名称
 * @returns {boolean} 是否隐藏当前cloudshell窗口
 */
export function handleMessage(e, ossUrl, nodeId, docName) {
  let message;
  let close = false;
  try {
    message = JSON.parse(e.data);
    // Cloud Shell 连接建立完成，已经可用
    if (message.action === 'CLOUDSHELL:CONNECTED') {
      close = true;
      openShell(ossUrl, docName);
    }

    if (message.action === 'CLOUDSHELL:NOTIFICATION') {
      // data 为 notify 命令执行时传入的参数
      const { data } = message;
      const { text } = data;
      openJupyterNoteBook(text, nodeId, false);
    }
    return close;
  } catch (error) {
    return false;
  }
}
