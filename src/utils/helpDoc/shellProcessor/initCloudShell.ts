import CloudShell from '@ali/cloudshell-js-sdk';
import { handleMessage, openJupyterNoteBook, openShell } from '@/utils/helpDoc/shellProcessor/handleMessage';
import { showOverlay } from './overlay';
import { popupDialog } from '@/help-fe-common/utils/global/prompt';
import services from '@/services/helplab/helplab';
import previewService from '@/help-fe-common/services/preview';
import { getLocalStorageItem } from '@/help-fe-common/utils/global/localStorage';

/**
 * 根据文档id获取jupyter文件链接
 * @param id 文档id
 * @param docHtml 文档内容html
 * @returns ipynb格式的oss链接
 */
const getJupyterUrl = async (id, docHtml) => {
  let ossData;
  if (previewService.isPreviewMode()) {
    ossData = await services.getJupyterUrl({ docHtml });
  } else {
    ossData = await services.getJupyterUrl({ nodeId: id });
  }
  return ossData;
};

/**
 * 监听cloudshell message并发送消息
 * @param ossUrl 向cloudshell发送的oss链接
 * @param nodeId 文档id
 */
const listenCloudShell = (ossUrl, nodeId, docName) => {
  window.addEventListener('message', (e) => {
    const close = handleMessage(e, ossUrl, nodeId, docName);
    if (close) {
      hideCloudShell(close);
      showOverlay(true, 0);
    }
  }, false);
};

const launchShell = (nodeId, docName, docHtml) => {
  getJupyterUrl(nodeId, docHtml).then((ossData) => {
    if (ossData?.ossUrl) {
      initCloudShell();
      listenCloudShell(ossData?.ossUrl, nodeId, docName);
    } else {
      popupDialog({ title: '启动异常，请提交反馈或稍后再试！', isShowButton: false, isSuccess: false });
    }
  });
};

/**
 * cloudshell初始化
 */
export const initCloudShell = () => {
  CloudShell.launch({
    openPureMode: true,
  });
  // 500ms延迟异步执行，避免cloudshell未初始化
  setTimeout(() => {
    showOverlay(true, 260);
  }, 500);
};

/**
 * cloudshell隐藏
 * @param {boolean} hide true隐藏/false展示
 */
export const hideCloudShell = (hide) => {
  const shellDOM = document.getElementById('cloudshell-service');
  shellDOM?.setAttribute('style', hide ? 'display:none' : 'display:block');
};

/**
 * cloudshell唤起
 * @param nodeId 文档id
 */
export const triggerShell = (nodeId, docName, docHtml) => {
  const shellDOM = document.getElementById('cloudshell-service');
  // 如果shell已启动
  if (shellDOM) {
    try {
      const jupyterUrl = JSON.parse(getLocalStorageItem('jupyterUrl') || {});
      const text = jupyterUrl?.text;
      const id = jupyterUrl?.nodeId;
      // 如果localStorage中有缓存，且为当前nodeId
      if (text && id === nodeId) {
        openJupyterNoteBook(text, nodeId, true);
      } else {
        getJupyterUrl(nodeId, docHtml).then((ossData) => {
          if (ossData?.ossUrl) {
            openShell(ossData?.ossUrl, docName);
          } else {
            popupDialog({ title: '启动异常，请提交反馈或稍后再试！', isShowButton: false, isSuccess: false });
          }
        });
      }
    } catch (error) {
      popupDialog({ title: '运行失败，请刷新页面后重试', isShowButton: false, isSuccess: false });
    }
  } else {
    launchShell(nodeId, docName, docHtml);
  }
};
