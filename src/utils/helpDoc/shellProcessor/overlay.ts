
/**
 * helplab运行后过渡页面
 * @param isShow true展示/false隐藏
 * @param blankHeight 底部空白预留高度
 */
const showOverlay = (isShow, blankHeight) => {
  const dialogDOM = document.querySelector('.helplab-overlay') as HTMLElement;
  if (dialogDOM) {
    dialogDOM.style.display = isShow ? 'block' : 'none';
    dialogDOM.style.height = `calc(100vh - ${blankHeight}px)`;
  } else {
    const overlayDialog = document.createElement('div');
    overlayDialog.classList.add('helplab-overlay');
    overlayDialog.style.display = isShow ? 'block' : 'none';
    overlayDialog.style.height = `calc(100vh - ${blankHeight}px)`;
    overlayDialog.innerHTML = `
    <div class="container">
    <div class="run-tip">请稍等，环境正在准备中...</div>
      <div class="spinner">
        <div class="rect1"></div>
        <div class="rect2"></div>
        <div class="rect3"></div>
        <div class="rect4"></div>
        <div class="rect5"></div>
      </div>  
    </div>`;
    document.body.appendChild(overlayDialog);
  }
};

/**
 * iframe容器，展示Jupyter lab运行页
 * @param isShow true展示/false隐藏
 * @param url iframe链接
 */
const showIframe = (isShow, url) => {
  const iframeDOM = document.querySelector('.helplab-iframe') as HTMLElement;
  if (iframeDOM) {
    iframeDOM.style.display = isShow ? 'block' : 'none';
  } else {
    const iframeNode = document.createElement('iframe');
    iframeNode.classList.add('helplab-iframe');
    iframeNode.setAttribute('src', url);
    document.body.appendChild(iframeNode);
  }
};

export { showOverlay, showIframe };
