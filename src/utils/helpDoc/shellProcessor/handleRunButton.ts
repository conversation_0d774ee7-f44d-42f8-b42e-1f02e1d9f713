import { getLoginState, getApiLoginState, loginDetector } from '@/help-fe-common/utils/global/user';
import { debounce } from 'lodash';
import { triggerShell } from '@/utils/helpDoc/shellProcessor/initCloudShell';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';

/**
 * 运行按钮绑定事件
 * @param event 事件event
 * @param {object} docData 文档数据
 */
const runCode = debounce((e, docData) => {
  const { nodeId, title: docName, content: docHtml } = docData;
  // 需要判断子账号，子账号不支持使用
  loginDetector(() => { triggerShell(nodeId, docName, docHtml); }, true);
  sendSlsLog({ page: 'documentDetail', section: 'helpLab', action: 'click', userParams1: nodeId, userParams2: e.target.className });
}, 1000, {
  leading: true,
  trailing: false,
});

/**
 * 添加文档可运行按钮
 * @param {string} className
 */
const addHeaderRunButton = (className) => {
  const hNode = document.querySelector(`.${className} h1`);
  const runButton = '<div class="help-lab-button" style="margin:0 0 0 10px"><i class="help-iconfont help-icon-bofang"></i>点击前往HelpLab运行</div>';
  hNode?.insertAdjacentHTML('beforeend', runButton);
};

/**
 * helpLab运行按钮逻辑处理
 * @param  {boolean} loginState  登录状态
 */
const handleRunButton = (loginState) => {
  // 主账号登录时，添加标题部分及代码部分的运行按钮
  if (loginState) {
    document.querySelectorAll('.markdown-body .help-code-block').forEach((codeNode) => {
      const iconNode = codeNode.querySelector('.left-tools') as HTMLElement;
      const iconHTML = '<i class="run-btn help-iconfont help-icon-bofang" title="在 Help Lab 中运行"></i>';
      iconNode.innerHTML = iconHTML;
    });
    // 试用入口按钮上线后不再添加顶部运行按钮
    addHeaderRunButton('aliyun-docs-view-header');
  }
  // 根据登录状态决定p.help-lab运行按钮及文案
  document.querySelectorAll('.markdown-body p.help-lab').forEach((pNode) => {
    if (!pNode) return;
    // 未登录不展示可运行文案，登录添加icon
    if (!loginState) {
      pNode.remove();
    } else {
      pNode?.insertAdjacentHTML('afterbegin', '<i class="help-iconfont help-icon-bofang"></i>');
    }
  });
};

/**
 * 根据登录状态初始化
 */
export const initCodeRun = (docData) => {
  // 优先读取cookie中登录状态，为true调用接口判断是否为子账号
  const localLoginState = getLoginState();
  if (!localLoginState) {
    handleRunButton(false);
    return;
  }
  // 接口调用方法自执行
  (async () => {
    const apiLoginState = await getApiLoginState();
    // 不支持子账号使用，子账号识别为未登录场景
    if (apiLoginState?.login && !apiLoginState?.ram) {
      handleRunButton(true);
      document.querySelectorAll('.help-lab, .help-lab-button, .run-btn').forEach((runNode) => {
        runNode?.addEventListener('click', (e) => runCode(e, docData));
      });
    } else {
      handleRunButton(false);
    }
  })();
};

