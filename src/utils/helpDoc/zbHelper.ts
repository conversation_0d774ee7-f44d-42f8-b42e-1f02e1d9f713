import { getEnv } from '@/help-fe-common/utils/global/env';

// 生成众包提交链接
export function generateZbSubmitUrl(docId) {
  const zbSubmitBugUrl =
    `https://${getEnv() === 'pre' ? 'pre-xing' : 'xing'}.aliyun.com/submit?documentId=${docId}&website=cn&language=zh`;
  return zbSubmitBugUrl;
}

// 生成众包跳转
export function generateZb(url) {
  const zbUrl =
    `https://${getEnv() === 'pre' ? 'pre-xing' : 'xing'}.aliyun.com/${url}`;
  return zbUrl;
}

export const onSubmitBug = (url) => {
  window.open(url);
};
