export enum moduleEnum {
  DOC_MODULE = 0,
}

const moduleTypeTest = {
  [moduleEnum.DOC_MODULE]: /(\/|product|document_detail|video_detail|knowledge_detail|video_list|knowledge_list|my_favorites)/,
};
export function getModuleType(pathname) {
  let flag = -1;
  Object.keys(moduleTypeTest)?.forEach((key) => {
    if (moduleTypeTest[key]?.test(pathname)) {
      flag = Number(key);
    }
  });
  return flag;
}
