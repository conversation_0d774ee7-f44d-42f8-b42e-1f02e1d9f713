import CookieHelper from '@/help-fe-common/utils/global/cookie';

const loadWaterMark = async () => {
  try {
    if (window?.$ACE?.loadScript) {
      await window.$ACE.loadScript("https://g.alicdn.com/data-security-manager/web-watermark/1.0.30/index.js");
      const now = new Date();
      let month = now.getMonth() + 1 as any;
      let day = now.getDate() as any;
      month = month < 10 ? ('0' + month) : month;
      day = day < 10 ? ('0' + day) : day;
      const date = String(month) + String(day);
      const cna = CookieHelper.get('cna') || '';
      const pk = CookieHelper.get('login_aliyunid_pk') || '';

      const staffId = pk ? pk.substr(pk.length - 5, 5) : cna ? cna.substr(cna.length - 5, 5) : '';
      //@ts-ignore
      window?.watermarkInit && window?.watermarkInit({
        staffId: date + staffId,//不超过九位数的整数  最大不超过2^32
        domain: "help.aliyun.com",//上面注册的域名
        //挂载暗水印的父节点容器。
        invisibleContainers: [document.getElementById('app')],
      });
    }
  } catch (error) {
    console.error(error);
  }
}

export { loadWaterMark };
