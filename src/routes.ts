import { IRouterConfig } from 'ice';
// 解决方案路由配置
import SolutionRoutes from '@/solution/routes';
import BasicLayout from '@/help-fe-common/layouts/BasicLayout';
import MainLayout from '@/help-fe-common/layouts/MainLayout';
import PcLayout from '@/help-fe-common/layouts/PcLayout';
import Index from '@/help-fe-common/pages/Index';
import ProductIndex from '@/pages/ProductIndex';
import ProductDetail from '@/pages/ProductDetail';
import MyFavorites from '@/pages/MyFavorites';
import PVWrapper from '@/help-fe-common/RouterWrapper/PVWrapper';
import PureDocWrapper from '@/help-fe-common/RouterWrapper/PureDocWrapper';
// 云主题
import TopicDetail from '@/pages/Topic/TopicDetail';
import LandingZoneDetail from '@/pages/Topic/LandingZoneDetail';
// 预览页
import Preview from '@/help-fe-common/pages/Preview';
// 404页面
import NotFound from '@/help-fe-common/components/common/NotFound';
// 校验页面，用于fc创建角色后发送事件消息
import Authorization from '@/pages/Authorization';
import LinkIntercept from '@/help-fe-common/components/common/LinkIntercept';


const routerConfig: IRouterConfig[] = [
  {
    path: '/redirect',
    component: LinkIntercept,
    wrappers: [PVWrapper],
  },
  {
    path: '/',
    component: BasicLayout,
    children: [
      ...SolutionRoutes,
      {
        path: '/authorization',
        exact: true,
        component: Authorization,
        wrappers: [PVWrapper],
      },
      {
        path: '/(zh|getting-started)/what-is/:alias',
        component: TopicDetail,
        wrappers: [PVWrapper],
      },
      {
        path: '/zh/landing-zone/:alias',
        component: LandingZoneDetail,
        wrappers: [PVWrapper],
      },
      {
        path: '/zh/preview',
        exact: true,
        component: Preview,
      },
      {
        path: '/',
        component: MainLayout,
        children: [
          {
            path: '/(zh)?',
            exact: true,
            component: Index,
            wrappers: [PVWrapper],
          },
          {
            path: '/new-index.html',
            exact: true,
            component: NotFound,
            wrappers: [PVWrapper],
          },
          {
            path: '/my_favorites*',
            component: MyFavorites,
            wrappers: [PVWrapper],
          },
          {
            path: '/',
            component: PcLayout,
            children: [
              {
                path: '/product/:id',
                exact: true,
                component: ProductIndex,
                wrappers: [PVWrapper],
              },
              {
                path: '/(document_detail|knowledge_detail|video_detail|video_list|knowledge_list)/:id',
                exact: true,
                component: ProductDetail,
                wrappers: [PVWrapper],
              },
              {
                path: '/zh/:productAlias',
                exact: true,
                component: ProductIndex,
                wrappers: [PVWrapper],
              },
              {
                path: '/zh/:productAlias/:topicAlias/:subProductAlias?/:docAlias?',
                exact: true,
                component: ProductDetail,
                wrappers: [PVWrapper],
              },
              {
                path: '/',
                component: NotFound,
                wrappers: [PVWrapper],
              },
            ],
          },
        ],
        wrappers: [PureDocWrapper],
      },
    ],
  },
  {
    path: '/',
    component: NotFound,
    wrappers: [PVWrapper],
  },
];
export default routerConfig;
