import get from 'lodash/get';
import { rosLinkConfig } from '@/components/QuickDeploy/constant/rosLinkConfig';
import { rosConsoleHost } from '@/components/QuickDeploy/utils/consoleEnv';
// 随机生成4位36进制数
export const generateRandom = () => Math.random().toString(36).substring(2, 6);

const generateRandomString = (length) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * chars.length);
    result += chars[randomIndex];
  }
  return result;
};

// 创建角色和授权URL
export const generateAuthorizeUrl = (config: Record<string, any>) => {
  const { service, roleName, requiredPolicy, description, actionType } = config;
  const params = {
    ReturnUrl: `${window.location.origin}/authorization?&mode=pure&actionType=${actionType}&response={response}`, // 回调地址
    // TODO 参数未定, 应该取值 customValues?.service
    Service: service?.toUpperCase() || 'FC',
    RoleName: roleName,
    SystemPolicyArray: requiredPolicy, // 要绑定的系统策略列表
    Type: 'custom',
    RoleDescription: description,
  };
  const url = `https://ram.console.aliyun.com/role/commonAuthorize?request=${encodeURIComponent(
    JSON.stringify(params),
  )}`;
  window.open(url);
};

export const getInitStackName = () => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份是从0开始的, 需要加1
  const day = String(date.getDate()).padStart(2, '0');
  const time = `${year}_${month}_${day}`;
  return `stack_${time}_${generateRandomString(9)}`;
};

export const transRosParams = (formValues: Record<string, any>) => {
  const obj = {};
  Object.keys(formValues)?.forEach((key, index) => {
    obj[`Parameters.${index + 1}.ParameterKey`] = key;
    obj[`Parameters.${index + 1}.ParameterValue`] = formValues[key];
  });
  return obj;
};

interface IStackOutput {
  Description: any;
  OutputKey: string;
  OutputValue: string;
}

/**
 * 生成ros资源栈访问链接
 * @param regionId 地域id
 * @param stackId 资源栈id
 * @returns
 */
export const getRosConsoleUrl = (regionId, stackId) => {
  return `${rosConsoleHost}/${regionId}/stacks/${stackId}`;
};

export const getRosCreateUrl = (rosTemplateUrl) => {
  return `${rosConsoleHost}/region/stacks/create?templateUrl=${encodeURIComponent(rosTemplateUrl)}
  &notificationURLs=[%22help%22]&immutableNotificationURLs=[%22help%22]`;
};

export const getStackOutputUrl = (outputs: IStackOutput[]) => {
  const outputItem = outputs?.find(
    (item) => item?.OutputKey === 'Console.Url' || item?.OutputKey === 'Console@Url',
  );
  return outputItem?.OutputValue;
};
const getChannelLink = (id, opt = null) => {
  const config = rosLinkConfig;
  let link: string = get(config, id);
  if (opt) {
    link = link.replace(/{@?([^}]+)}/g, (match, key) => {
      return opt[key];
    });
  }
  return link;
};
const getResourceLink = (resourceType, info = {} as any) => {
  const link = getChannelLink(resourceType);
  if (resourceType === 'ALIYUN::ROS::Stack') {
    const { PhysicalId, RegionId } = info;
    if (PhysicalId) {
      return `${rosConsoleHost}/${RegionId}/stacks/${PhysicalId}`;
    }
  } else if (link) {
    /* eslint-disable */
    const resourceLink = link.replace(/\@{([A-Za-z0-9_]*)}/g, (key) => {
      return info[key.replace(/[\@\{\}]/g, '')]
    })
    return resourceLink;
  }
  return '';
}

export const getStackResourceUrl = (resourceItem) => {
  if (!resourceItem) return;
  const { ResourceType, PhysicalResourceId, regionId, LogicalResourceId } = resourceItem;
  const linkRes = getResourceLink(ResourceType, {
    ...resourceItem,
    PhysicalId: PhysicalResourceId,
    RegionId: regionId,
    ResourceName: LogicalResourceId,
  });
  // 避免资源链接中有些参数值是undefined
  if (!linkRes?.includes?.('undefined')) {
    return linkRes;
  }
  return ''
};
