import React, { FunctionComponentElement } from 'react';
import ReactDOM from 'react-dom';
import { debounce } from 'lodash';
import QuickDeployService from '@/services/helplab/quickDeploy';
import RosQuickDeploy from '@/components/QuickDeploy/RosQuickDeploy';
import FcQuickDeploy from '@/components/QuickDeploy/FcQuickDeploy';
import QuickDialog from '@/components/QuickDeploy/FcQuickDeploy/QuickDialog';
import DeployApplication from '@/components/QuickDeploy/FcQuickDeploy/DeployApplication';
import ListApplication from '@/components/QuickDeploy/ListApplication';
import { fastLogin } from '@/help-fe-common/utils/global/user';
import { onShowDocSide } from '@/help-fe-common/utils/helpDoc/docDetail';
import { EQuickDeployType } from '@/components/QuickDeploy/constant';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';

export interface IDeployItem {
  id: string;
  desc: string;
  output: string;
  type: EQuickDeployType;
}

export interface IDeployDialogProps {
  deployId: string;
  deployType: EQuickDeployType;
  templateId: string;
  deployedNodeId: string;
  deployedNodeTitle: string;
  testStepAnchor?: string;
  regionId?: string;
}

const onMounted = (childNode: FunctionComponentElement<any>) => {
  const containerNode = document.getElementById('aliyun-docs-side-blank');

  if (!containerNode) return;
  ReactDOM.unmountComponentAtNode(containerNode);
  onShowDocSide(true);
  ReactDOM.render(
    childNode,
    containerNode,
  );
};

export const onUnmounted = () => {
  onShowDocSide(false);

  setTimeout(() => {
    const containerNode = document.getElementById('aliyun-docs-side-blank');
    containerNode && ReactDOM.unmountComponentAtNode(containerNode);
  }, 100);
};

const checkDeployDialog = async (props: IDeployDialogProps) => {
  const {
    deployId,
    deployedNodeTitle,
  } = props;
  if (!deployId) return;
  const res = await QuickDeployService.getUserDeployStatus({ deployId });
  if (res?.success === false && res?.code === 401) {
    fastLogin(window.location.href);
  } else if (res?.data) {
    QuickDialog({
      type: 'notice',
      title: '您已经在本模板下部署过资源栈或应用',
      content: '您可以查看部署过的资源栈或应用，也可以部署全新的资源栈或应用。',
      okProps: {
        children: '部署资源栈或应用',
      },
      cancelProps: {
        children: '查看已部署',
      },
      onOk: () => {
        renderQuickDeployForm(props);
      },
      onCancel: () => {
        renderUserApplication({ ...props, searchValue: deployedNodeTitle });
      },
    });
  } else {
    renderQuickDeployForm(props);
  }
};

export const renderQuickDeployForm = (props: IDeployDialogProps) => {
  const {
    deployId,
    templateId,
    deployType,
    deployedNodeId,
    deployedNodeTitle,
    testStepAnchor,
  } = props;
  if (deployType === EQuickDeployType.FC) {
    const fcFormNode = React.createElement(FcQuickDeploy, {
      onClose: () => onUnmounted(),
      visible: true,
      deployId,
      deployType,
      templateId,
      testStepAnchor,
      deployedNodeId,
      deployedNodeTitle,
      // codeSelectionList,
    });
    onMounted(fcFormNode);
  } else {
    const rosFormNode = React.createElement(RosQuickDeploy, {
      onClose: () => onUnmounted(),
      visible: true,
      ...props,
    });
    onMounted(rosFormNode);
  }
};

export const renderUserApplication = (props: Record<string, any> = {}) => {
  const fcApplication = React.createElement(DeployApplication, {
    onClose: onUnmounted,
    visible: true,
    deployType: props.deployType,
    ...props,
  }, React.createElement(ListApplication, {
    searchValue: props.searchValue,
    onClose: onUnmounted,
    deployType: props.deployType,
  }));
  onMounted(fcApplication);
};

export const onDeployClick = debounce((deployItem: IDeployItem, title, nodeId) => {
  const { id, type, output } = deployItem;
  checkDeployDialog({
    deployId: id,
    deployType: type,
    templateId: output,
    deployedNodeId: nodeId,
    deployedNodeTitle: title,
  });
  sendSlsLog({ page: 'documentDetail', section: 'quickDeploy', action: 'pv', userParams1: type, userParams2: id });
}, 3000, { leading: true });

export const renderRosDeployStatus = ({ title, templateId, stackId, regionId }) => {
  const rosFormNode = React.createElement(RosQuickDeploy, {
    onClose: () => onUnmounted(),
    visible: true,
    deployType: EQuickDeployType.ROS,
    templateId,
    deployedNodeTitle: title,
    successViewProps: {
      stackId,
      regionId,
    },
  } as any);
  onMounted(rosFormNode);
};
