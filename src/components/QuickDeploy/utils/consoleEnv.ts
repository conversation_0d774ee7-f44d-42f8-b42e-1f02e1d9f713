import { ENV, getEnv } from '@/help-fe-common/utils/global/env';

const env = getEnv();

const getFcConsoleHostByEnv = () => {
  if (env === ENV.PROD) {
    return 'https://fcnext.console.aliyun.com';
  }
  return 'https://pre-fcnext.console.aliyun.com';
};

export const fcConsoleHost = getFcConsoleHostByEnv();


const getRosConsoleHostByEnv = () => {
  if (env === ENV.PROD) {
    return 'https://ros.console.aliyun.com';
  }
  return 'https://pre-ros.console.aliyun.com';
}


export const rosConsoleHost = getRosConsoleHostByEnv();
