/* eslint-disable max-len */
export const rosLinkConfig = {
  activity_Elastic_Computing_Cloud_Certification: 'https://edu.aliyun.com/certification/cldc22?utm_content=g_1000382053',
  activity_ROS_Automated_Deployment_Learning_Training_Camp: 'https://developer.aliyun.com/trainingcamp/38e4c86accdd44d9acb6bd90fe2625f6',
  activity_ROS_Practice_Series_Courses: 'https://developer.aliyun.com/adc/scenarioSeries/8b4e8fa2a3084ce6af58957e95cfa89e',
  alicdn: 'https://g.alicdn.com',
  alicloud_actiontrail: 'https://actiontrail.console.aliyun.com/@{RegionId}/trail-list/@{PhysicalId}',
  alicloud_actiontrail_trail: 'https://actiontrail.console.aliyun.com/@{RegionId}/trail-list/@{PhysicalId}',
  alicloud_adb_cluster: 'https://ads.console.aliyun.com/adb/@{RegionId}/instances/v3/@{PhysicalId}/basic',
  alicloud_adb_db_cluster: 'https://ads.console.aliyun.com/adb/@{RegionId}/instances/v3/@{PhysicalId}/basic',
  alicloud_alikafka_topic: 'https://kafka.console.aliyun.com/#/TopicManagement?regionId=@{RegionId}',
  alicloud_amqp_instance: 'https://amqp.console.aliyun.com/region/@{RegionId}/instance/@{PhysicalId}/instance-detail',
  alicloud_api_gateway_api: 'https://apigateway.console.aliyun.com/#/@{RegionId}/apis/list',
  alicloud_api_gateway_app: 'https://apigateway.console.aliyun.com/#/@{RegionId}/apps/detail/@{PhysicalId}',
  alicloud_api_gateway_group: 'https://apigateway.console.aliyun.com/#/@{RegionId}/groups/detail/@{PhysicalId}',
  alicloud_arms_alert_contact: 'https://arms.console.aliyun.com/#/alarm/contacts/contact',
  alicloud_arms_alert_contact_group: 'https://arms.console.aliyun.com/#/alarm/contacts/contact',
  alicloud_auto_provisioning_group: 'https://ecs.console.aliyun.com/#/fleet/region/@{RegionId}',
  alicloud_cas_certificate: 'https://yundun.console.aliyun.com/?p=cas#/overview/@{RegionId}',
  alicloud_cdn_domain: 'https://cdn.console.aliyun.com/domain/list',
  alicloud_cen_bandwidth_limit: 'https://cen.console.aliyun.com/cen/detail/@{PhysicalId}/bandwidthPackage',
  alicloud_cen_bandwidth_package: 'https://cen.console.aliyun.com/cen/detail/@{PhysicalId}/bandwidthPackage',
  alicloud_cen_bandwidth_package_attachment: 'https://cen.console.aliyun.com/cen/detail/@{PhysicalId}/crossInteregional',
  alicloud_cen_instance: 'https://cen.console.aliyun.com/cen/detail/@{PhysicalId}/attachInstance',
  alicloud_cen_instance_attachment: 'https://cen.console.aliyun.com/cen/detail/@{PhysicalId}/attachInstance',
  alicloud_cen_route_entry: 'https://cen.console.aliyun.com/cen/detail/@{PhysicalId}/route',
  alicloud_cen_transit_router: 'https://cen.console.aliyun.com/cen/attachment/@{RegionId}/@{CenId}/@{PhysicalId}/attachInstance',
  alicloud_cen_transit_router_route_entry: 'https://cen.console.aliyun.com/cen/detail/@{PhysicalId}/route',
  alicloud_cen_transit_router_vpc_attachment: 'https://cen.console.aliyun.com/cen/attachment/@{RegionId}/@{CenId}/@{TransitRouterId}/crossInteregional',
  alicloud_cloud_connect_network: 'https://smartag.console.aliyun.com/ccn/@{RegionId}/ccns/@{PhysicalId}',
  alicloud_cloud_firewall_address_book: 'https://yundun.console.aliyun.com',
  alicloud_cms_alarm_contact: 'https://cloudmonitor.console.aliyun.com/#/contactListApp',
  alicloud_cms_alarm_contact_group: 'https://cloudmonitor.console.aliyun.com/#/contactListApp',
  alicloud_cms_event_rule: 'https://cloudmonitor.console.aliyun.com/#/alarmservice/product=&searchValue=&searchType=&searchProduct=',
  alicloud_cms_group_metric_rule: 'https://cloudmonitor.console.aliyun.com/#/alarmservice/product=&searchValue=&searchType=&searchProduct=',
  alicloud_cms_metric_rule_template: 'https://cloudmonitor.console.aliyun.com/alarmTemplate/',
  alicloud_cms_monitor_group: 'https://cloudmonitor.console.aliyun.com/#/groups/category=&region=&instanceIds=&groupType=&keyword=',
  alicloud_cms_site_monitor: 'https://cloudmonitornext.console.aliyun.com/newSite/details/@{PhysicalId}',
  alicloud_common_bandwidth_package: 'https://vpcnext.console.aliyun.com/cbwp/@{RegionId}/cbwps/@{PhysicalId}',
  alicloud_copy_image: 'https://ecs.console.aliyun.com/#/image/region/@{RegionId}/imageList',
  alicloud_cr_chart_namespace: 'https://cr.console.aliyun.com/@{RegionId}/instances/namespaces',
  alicloud_cr_chart_repository: 'https://cr.console.aliyun.com/@{RegionId}/instances/repositories',
  alicloud_cr_ee_namespace: 'https://cr.console.aliyun.com/@{RegionId}/instances/namespaces',
  alicloud_cr_namespace: 'https://cr.console.aliyun.com/@{RegionId}/instances/namespaces',
  alicloud_cs_application: 'https://cs.console.aliyun.com/#/k8s/overview',
  alicloud_cs_edge_kubernetes: 'https://cs.console.aliyun.com/#/k8s/cluster/@{PhysicalId}/info/overview?clusterType=ManagedKubernetes&profile=Edge&state=running&region=@{RegionId}',
  alicloud_cs_kubernetes: 'https://cs.console.aliyun.com/?#/k8s/cluster/@{PhysicalId}/info?clusterType=Kubernetes&profile=&state=running&region=@{RegionId}',
  alicloud_cs_kubernetes_clusters: 'https://cs.console.aliyun.com/#/k8s/cluster/@{PhysicalId}/info',
  alicloud_cs_managed_kubernetes: 'https://cs.console.aliyun.com/?#/k8s/cluster/@{PhysicalId}/info?clusterType=ManagedKubernetes&profile=&state=running&region=@{RegionId}',
  alicloud_cs_serverless_kubernetes: 'https://cs.console.aliyun.com/#/k8s/cluster/@{PhysicalId}/info/overview?clusterType=Ask&profile=ask.v2&state=running&region=@{RegionId}',
  alicloud_datahub_project: 'https://dhsnext.console.aliyun.com/@{RegionId}/projects/@{PhysicalId}',
  alicloud_db_instance: 'https://rdsnext.console.aliyun.com/#/detail/@{PhysicalId}/basicInfo?region=@{RegionId}',
  alicloud_db_readonly_instance: 'https://rdsnext.console.aliyun.com/#/detail/@{PhysicalId}/basicInfo?region=@{RegionId}',
  alicloud_dms_enterprise_instance: 'https://dms.console.aliyun.com/#/dms/rsList',
  alicloud_dms_enterprise_user: 'https://dms.console.aliyun.com/#/dms/user/config',
  alicloud_dns_domain: 'https://dns.console.aliyun.com/#/dns/setting/@{PhysicalId}',
  alicloud_drds_instance: 'https://drdsnew.console.aliyun.com/#/@{PhysicalId}/instDetail/drdsInstanceInfo',
  alicloud_dts_migration_job: 'https://dts.console.aliyun.com/?#/progress/@{PhysicalId}/config',
  alicloud_ebs_disk_replica_group: 'https://ebs.console.aliyun.com/consistencyGroup/@{RegionId}/@{PhysicalId}',
  alicloud_eci_container_group: 'https://eci.console.aliyun.com/#/eci/@{RegionId}',
  alicloud_eci_image_cache: 'https://eci.console.aliyun.com/?#/eci/@{RegionId}/image/@{PhysicalId}',
  alicloud_eci_openapi_image_cache: 'https://eci.console.aliyun.com/?#/eci/@{RegionId}/image/@{PhysicalId}',
  alicloud_ecs_auto_snapshot_policy: 'https://ecs.console.aliyun.com/#/autoSnapshotPolicy/region/@{RegionId}',
  alicloud_ecs_command: 'https://ecs.console.aliyun.com/cloud-assistant/region/@{RegionId}/command?CommandId=@{PhysicalId}&operation=checkCommand&source=main&search=single',
  alicloud_ecs_dedicated_host: 'https://ecs.console.aliyun.com/#/ddh/region/@{RegionId}',
  alicloud_ecs_deployment_set: 'https://ecs.console.aliyun.com/#/deploymentSet/region/@{RegionId}',
  alicloud_ecs_disk: 'https://ecs.console.aliyun.com/#/diskdetail/@{PhysicalId}/detail?regionId=@{RegionId}',
  alicloud_ecs_disk_attachment: 'https://ecs.console.aliyun.com/#/diskdetail/@{PhysicalId}/detail?regionId=@{RegionId}',
  alicloud_ecs_hpc_cluster: 'https://ecs.console.aliyun.com/#/cluster/region/@{RegionId}',
  alicloud_ecs_instance_set: 'https://ecs.console.aliyun.com/#/server/region/@{RegionId}?instanceIds=@{PhysicalId}',
  alicloud_ecs_invocation: 'https://ecs.console.aliyun.com/#/cloud-assistant/region/@{RegionId}/result?operation=checkResult&InvokeId=@{PhysicalId}&source=main&search=single',
  alicloud_ecs_key_pair: 'https://ecs.console.aliyun.com/#/keyPair/region/@{RegionId}',
  alicloud_ecs_key_pair_attachment: 'https://ecs.console.aliyun.com/#/keyPair/region/@{RegionId}',
  alicloud_ecs_launch_template: 'https://ecs.console.aliyun.com/#/launchTemplate/region/@{RegionId}',
  alicloud_ecs_network_interface: 'https://ecs.console.aliyun.com/#/networkInterfaces/region/@{RegionId}',
  alicloud_ecs_network_interface_attachment: 'https://ecs.console.aliyun.com/#/server/@{PhysicalId}/eni?regionId=@{RegionId}',
  alicloud_ecs_network_interface_permission: 'https://ecs.console.aliyun.com/#/server/@{PhysicalId}/eni?regionId=@{RegionId}',
  alicloud_ecs_prefix_list: 'https://ecs.console.aliyun.com/#/prefixListDetail/@{RegionId}/@{PhysicalId}/items',
  alicloud_ecs_snapshot: 'https://ecs.console.aliyun.com/#/server/@{PhysicalId}/snapshot?regionId=@{RegionId}',
  alicloud_edas_cluster: 'https://edasnext.console.aliyun.com/#/edasResources/clusterInfo?regionNo=@{RegionId}&clusterId=@{PhysicalId}&networkMode=2&oversoldFactor=1&clusterType=2',
  alicloud_edas_k8s_cluster: 'https://edasnext.console.aliyun.com/#/edasResources/clusterInfo?regionNo=@{RegionId}&clusterId=@{PhysicalId}&networkMode=2&oversoldFactor=1&clusterType=2',
  alicloud_ehpc_cluster: 'https://ehpc.console.aliyun.com/#/cluster?regionId=@{RegionId}',
  alicloud_eip: 'https://vpc.console.aliyun.com/eip/@{RegionId}/eips?AllocationId=@{PhysicalId}',
  alicloud_elasticsearch_instance: 'https://elasticsearch-@{RegionId}.console.aliyun.com/#/instances/@{PhysicalId}/base',
  alicloud_emr_cluster: 'https://emr.console.aliyun.com/#/@{RegionId}/cluster/@{PhysicalId}/detail',
  alicloud_ens_key_pair: 'https://ens.console.aliyun.com/#/instance/list',
  alicloud_ess_alarm: 'https://essnew.console.aliyun.com/#/task/alarm/region/@{RegionId}',
  alicloud_ess_eci_scaling_configuration: 'https://ess.console.aliyun.com/#/ess/region/@{RegionId}',
  alicloud_ess_lifecycle_hook: 'https://essnew.console.aliyun.com/#/detail/@{RegionId}/@{PhysicalId}/lifecycleHook',
  alicloud_ess_scaling_configuration: 'https://ess.console.aliyun.com/#/ess/region/@{RegionId}',
  alicloud_ess_scaling_group: 'https://essnew.console.aliyun.com/#/detail/@{RegionId}/@{PhysicalId}/basicInfo',
  alicloud_ess_scheduled_task: 'https://essnew.console.aliyun.com/#/task/timer/region/@{RegionId}',
  alicloud_fc_custom_domain: 'https://fcnext.console.aliyun.com/@{RegionId}/domains/@{PhysicalId}/detail',
  alicloud_fc_function: 'https://fcnext.console.aliyun.com/@{RegionId}/services/@{ServiceName}/function-detail/@{FunctionName}/LATEST?tab=code',
  alicloud_fc_service: 'https://fcnext.console.aliyun.com/@{RegionId}/services/@{ServiceName}/functions',
  alicloud_fc_trigger: 'https://fcnext.console.aliyun.com/@{RegionId}/services/@{ServiceName}/function-detail/@{FunctionName}/LATEST?tab=trigger',
  alicloud_fnf_flow: 'https://fnf.console.aliyun.com/fnf/@{RegionId}/flows/item/{FlowName}',
  alicloud_forward_entry: 'https://vpc.console.aliyun.com/#/nat/@{RegionId}/list',
  alicloud_ga_accelerator: 'https://ga.console.aliyun.com/list/@{PhysicalId}',
  alicloud_ga_bandwidth_package: 'https://ga.console.aliyun.com/bandwidth',
  alicloud_havip: 'https://vpcnext.console.aliyun.com/vpc/@{RegionId}/havips/@{PhysicalId}',
  alicloud_hbr_restore_job: 'https://hbr.console.aliyun.com/#/cloud/sql?tab=restore&_k=nf0h8e',
  alicloud_image: 'https://ecs.console.aliyun.com/#/imageDetail/region/@{RegionId}/imageId/@{PhysicalId}',
  alicloud_images: 'https://ecs.console.aliyun.com/#/image/region/@{RegionId}/imageList',
  alicloud_image_copy: 'https://ecs.console.aliyun.com/#/server/@{PhysicalId}/snapshot?regionId=@{RegionId}',
  alicloud_instance: 'https://ecs.console.aliyun.com/#/server/@{PhysicalId}/detail?regionId=@{RegionId}',
  alicloud_kms_alias: 'https://kms.console.aliyun.com/@{RegionId}/key/detail/@{PhysicalId}',
  alicloud_kms_key: 'https://kms.console.aliyun.com/@{RegionId}/key/detail/@{PhysicalId}',
  alicloud_kvstore_instance: 'https://kvstore.console.aliyun.com/#/detail/@{PhysicalId}/Normal/CLASSIC/info?regionId=@{RegionId}',
  alicloud_log_machine_group: 'https://sls.console.aliyun.com/lognext/project/@{PhysicalId}/overview',
  alicloud_log_project: 'https://sls.console.aliyun.com/lognext/project/@{PhysicalId}/overview',
  alicloud_log_store: 'https://sls.console.aliyun.com/lognext/project/@{PhysicalId}/overview',
  alicloud_mns_queue: 'https://mns.console.aliyun.com/#/?regionId=@{RegionId}',
  alicloud_mns_topic: 'https://mns.console.aliyun.com/#/Mnstheme?regionId=@{RegionId}',
  alicloud_mongodb_instance: 'https://mongodb.console.aliyun.com/#/mongodb/detail/@{PhysicalId}/info',
  alicloud_mongodb_serverless_instance: 'https://mongodb.console.aliyun.com/serverless/@{RegionId}/instances/@{PhysicalId}/basicInfo',
  alicloud_mongodb_sharding_instance: 'https://mongodb.console.aliyun.com/sharding/@{RegionId}/instances/@{PhysicalId}/basicInfo',
  alicloud_mse_cluster: 'https://mse.console.aliyun.com/#/Home',
  alicloud_nas_access_rule: 'https://nasnext.console.aliyun.com/@{RegionId}/access-group',
  alicloud_nas_file_system: 'https://nasnext.console.aliyun.com/@{RegionId}/filesystem/@{PhysicalId}/info?sourceUrl=FileSystem',
  alicloud_nas_mount_target: 'https://nasnext.console.aliyun.com/@{RegionId}/filesystem/@{PhysicalId}/mount',
  alicloud_nat_gateway: 'https://vpc.console.aliyun.com/nat/@{RegionId}/nats/@{PhysicalId}',
  alicloud_nlb_server_group: 'https://slb.console.aliyun.com/nlb/@{RegionId}/server-groups/@{PhysicalId}',
  alicloud_ons_instance: 'https://ons.console.aliyun.com/#/InstanceManagement?instanceId=@{PhysicalId}&regionId=@{RegionId}',
  alicloud_oos_template: 'https://oos.console.aliyun.com/@{RegionId}/template/private/detail/@{PhysicalId}',
  alicloud_oss_bucket: 'https://oss.console.aliyun.com/bucket/oss-@{RegionId}/@{PhysicalId}/overview',
  alicloud_ots_instance: 'https://ots.console.aliyun.com/index#/list/@{RegionId}',
  alicloud_ots_table: 'https://ots.console.aliyun.com/index#/list/@{RegionId}',
  alicloud_polardb_account: 'https://polardb.console.aliyun.com/@{RegionId}/cluster/@{PhysicalId}/account',
  alicloud_polardb_cluster: 'https://polardb.console.aliyun.com/@{RegionId}/cluster/@{PhysicalId}/baseInfo',
  alicloud_polardb_database: 'https://polardb.console.aliyun.com/@{RegionId}/cluster/@{PhysicalId}/database',
  alicloud_polardb_endpoint: 'https://polardb.console.aliyun.com/@{RegionId}/cluster/@{PhysicalId}/baseInfo',
  alicloud_polardb_endpoint_address: 'https://polardb.console.aliyun.com/@{RegionId}/cluster/@{PhysicalId}/baseInfo',
  alicloud_privatelink_vpc_endpoint: 'https://vpc.console.aliyun.com/endpoint/@{RegionId}/endpoints/@{PhysicalId}',
  alicloud_privatelink_vpc_endpoint_service: 'https://vpc.console.aliyun.com/endpointservice/@{RegionId}/endpointservices/@{PhysicalId}',
  alicloud_pvtz_zone: 'https://dns.console.aliyun.com/#/privateZone/detail/@{PhysicalId}',
  alicloud_pvtz_zone_record: 'https://dns.console.aliyun.com/#/privateZone/setting/record/@{PhysicalId}',
  alicloud_ram_access_key: 'https://usercenter.console.aliyun.com/#/manage/ak',
  alicloud_ram_group: 'https://ram.console.aliyun.com/#/group/detail/@{PhysicalId}/info',
  alicloud_ram_policy: 'https://ram.console.aliyun.com/policies/@{PhysicalId}/Custom',
  alicloud_ram_role: 'https://ram.console.aliyun.com/roles/@{PhysicalId}',
  alicloud_ram_user: 'https://ram.console.aliyun.com/#/user/detail/@{PhysicalId}/info',
  alicloud_rds_clone_db_instance: 'https://rdsnext.console.aliyun.com/#/detail/@{PhysicalId}/basicInfo?region=@{RegionId}',
  alicloud_rds_parameter_group: 'https://rdsnext.console.aliyun.com/#/detail/@{PhysicalId}/basicInfo?region=@{RegionId}',
  alicloud_rds_upgrade_db_instance: 'https://rdsnext.console.aliyun.com/#/detail/@{PhysicalId}/basicInfo?region=@{RegionId}',
  alicloud_regions: 'https://ecs.console.aliyun.com/#/',
  alicloud_resource_manager_resource_group: 'https://resourcemanager.console.aliyun.com/resource-groups/@{PhysicalId}',
  alicloud_router_interface: 'https://vpc.console.aliyun.com/vpc/@{RegionId}/route-tables/@{PhysicalId}',
  alicloud_route_entry: 'https://vpc.console.aliyun.com/vpc/@{RegionId}/route-tables/@{PhysicalId}',
  alicloud_route_table: 'https://vpc.console.aliyun.com/vpc/@{RegionId}/route-tables//@{PhysicalId}',
  alicloud_sae_application: 'https://sae.console.aliyun.com/#/AppList/AppDetail?appId=@{PhysicalId}&regionId=@{RegionId}',
  alicloud_sae_namespace: 'https://sae.console.aliyun.com/#/NameSpace?regionId=@{RegionId}',
  alicloud_sag_acl: 'https://smartag.console.aliyun.com/acl/@{RegionId}/acls/@{PhysicalId}',
  alicloud_sag_acl_rule: 'https://smartag.console.aliyun.com/acl/@{RegionId}/acls/@{PhysicalId}',
  alicloud_security_group: 'https://ecs.console.aliyun.com/#/securityGroupDetail/region/@{RegionId}/groupId/@{PhysicalId}/instance',
  alicloud_slb_ca_certificate: 'https://slb.console.aliyun.com/slb/@{RegionId}/certs',
  alicloud_slb_listener: 'https://slb.console.aliyun.com/slb/@{RegionId}/slbs/@{load_balancer_id}/listeners',
  alicloud_slb_load_balancer: 'https://slb.console.aliyun.com/slb/@{RegionId}/slbs/@{PhysicalId}',
  alicloud_slb_master_slave_server_group: 'https://slb.console.aliyun.com/slb/@{RegionId}/slbs/@{PhysicalId}/ms-server-groups/',
  alicloud_slb_rule: 'https://slb.console.aliyun.com/slb/@{RegionId}/slbs/@{PhysicalId}/listeners',
  alicloud_slb_server_certificate: 'https://slb.console.aliyun.com/slb/@{RegionId}/certs',
  alicloud_snat_entry: 'https://vpc.console.aliyun.com/nat/@{RegionId}/nats/@{PhysicalId}/snats',
  alicloud_ssl_vpn_client_cert: 'https://vpc.console.aliyun.com/sslvpn/@{RegionId}/vpn-clients',
  alicloud_ssl_vpn_server: 'https://vpc.console.aliyun.com/sslvpn/@{RegionId}/vpn-servers',
  alicloud_vpc: 'https://vpc.console.aliyun.com/#/vpc/@{RegionId}/detail/@{PhysicalId}/info',
  alicloud_vpn_connection: 'https://vpc.console.aliyun.com/vpn/@{RegionId}/vpn-connections',
  alicloud_vpn_customer_gateway: 'https://vpcnext.console.aliyun.com/vpn/@{RegionId}/vpn-clients',
  alicloud_vpn_gateway: 'https://vpc.console.aliyun.com/vpn/@{RegionId}/vpns/@{PhysicalId}',
  alicloud_vswitch: 'https://vpc.console.aliyun.com/vpc/@{RegionId}/switches/@{PhysicalId}',
  alicloud_waf_instance: 'https://yundun.console.aliyun.com/?p=wafnext#/waf/@{RegionId}/manage/domain/list',
  'ALIYUN::ACM::Configuration': 'https://acmnext.console.aliyun.com/@{RegionId}/configlist',
  'ALIYUN::ACM::Namespace': 'https://acmnext.console.aliyun.com/@{RegionId}/namespace/',
  'ALIYUN::ACTIONTRAIL::Trail': 'https://actiontrail.console.aliyun.com/@{RegionId}/trail-list/@{PhysicalId}',
  'ALIYUN::ACTIONTRAIL::TrailLogging': 'https://actiontrail.console.aliyun.com/@{RegionId}/event-list',
  'ALIYUN::ADB::DBCluster': 'https://ads.console.aliyun.com/adb/@{RegionId}/instances/v3/@{PhysicalId}/basic',
  'ALIYUN::ALB::Acl': 'https://slb.console.aliyun.com/alb/@{RegionId}/acls/@{PhysicalId}/entries',
  'ALIYUN::ALB::AclAssociation': 'https://slb.console.aliyun.com/alb/@{RegionId}/albs/@{ListenerId}/listeners/@{PhysicalId}',
  'ALIYUN::ALB::BackendServerAttachment': 'https://slb.console.aliyun.com/alb/@{RegionId}/server-groups/@{ServerGroupId}',
  'ALIYUN::ALB::HealthCheckTemplate': 'https://slb.console.aliyun.com/alb/@{RegionId}/tls',
  'ALIYUN::ALB::Listener': 'https://slb.console.aliyun.com/alb/@{RegionId}/albs/@{LoadBalancerId}/listeners/@{PhysicalId}',
  'ALIYUN::ALB::LoadBalancer': 'https://slb.console.aliyun.com/alb/@{RegionId}/albs/@{PhysicalId}',
  'ALIYUN::ALB::SecurityPolicy': 'https://slb.console.aliyun.com/alb/@{RegionId}/tls',
  'ALIYUN::ALB::ServerGroup': 'https://slb.console.aliyun.com/alb/@{RegionId}/server-groups/@{PhysicalId}',
  'ALIYUN::AMQP::Instance': 'https://amqp.console.aliyun.com/region/@{RegionId}/instance/@{PhysicalId}/instance-detail',
  'ALIYUN::ApiGateway::Api': 'https://apigateway.console.aliyun.com/#/@{RegionId}/apis/list',
  'ALIYUN::ApiGateway::App': 'https://apigateway.console.aliyun.com/#/@{RegionId}/apps/detail/@{PhysicalId}',
  'ALIYUN::ApiGateway::Group': 'https://apigateway.console.aliyun.com/#/@{RegionId}/groups/detail/@{PhysicalId}',
  'ALIYUN::ApiGateway::Signature': 'https://apigateway.console.aliyun.com/#/@{RegionId}/keys/detail/@{PhysicalId}',
  'ALIYUN::ApiGateway::StageConfig': 'https://apigateway.console.aliyun.com/#/@{RegionId}/groups/stages/@{PhysicalId}',
  'ALIYUN::ApiGateway::TrafficControl': 'https://apigateway.console.aliyun.com/#/@{RegionId}/trafficControl/detail/@{PhysicalId}',
  'ALIYUN::ApiGateway::TrafficControlBinding': 'https://apigateway.console.aliyun.com/#/@{RegionId}/trafficControl/detail/@{PhysicalId}',
  'ALIYUN::ApiGateway::VpcAccessConfig': 'https://apigateway.console.aliyun.com/#/@{RegionId}/vpcAccess/list',
  'ALIYUN::ARMS::AlertContact': 'https://arms.console.aliyun.com/#/alarm/contacts/contact',
  'ALIYUN::ARMS::AlertContactGroup': 'https://arms.console.aliyun.com/#/alarm/contacts/contact',
  'ALIYUN::ARMS::RetcodeApp': 'https://arms.console.aliyun.com/#/retcode',
  'ALIYUN::CAS::Certificate': 'https://yundun.console.aliyun.com/?p=cas#/overview/@{RegionId}',
  'ALIYUN::CDN::Domain': 'https://cdn.console.aliyun.com/domain/list',
  'ALIYUN::CEN::CenBandwidthLimit': 'https://cen.console.aliyun.com/cen/detail/@{PhysicalId}/bandwidthPackage',
  'ALIYUN::CEN::CenBandwidthPackage': 'https://cen.console.aliyun.com/cen/detail/@{PhysicalId}/bandwidthPackage',
  'ALIYUN::CEN::CenBandwidthPackageAssociation': 'https://cen.console.aliyun.com/cen/detail/@{PhysicalId}/crossInteregional',
  'ALIYUN::CEN::CenInstance': 'https://cen.console.aliyun.com/cen/detail/@{PhysicalId}/attachInstance',
  'ALIYUN::CEN::CenInstanceAttachment': 'https://cen.console.aliyun.com/cen/detail/@{PhysicalId}/attachInstance',
  'ALIYUN::CEN::RouteEntry': 'https://cen.console.aliyun.com/cen/detail/@{PhysicalId}/route',
  'ALIYUN::CEN::TransitRouter': 'https://cen.console.aliyun.com/cen/attachment/@{RegionId}/@{CenId}/@{PhysicalId}/attachInstance',
  'ALIYUN::CEN::TransitRouterVpcAttachment': 'https://cen.console.aliyun.com/cen/attachment/@{RegionId}/@{CenId}/@{TransitRouterId}/crossInteregional',
  'ALIYUN::ClickHouse::DBCluster': 'https://clickhouse.console.aliyun.com/clickhouse/@{RegionId}/list/nav/@{PhysicalId}/basic',
  'ALIYUN::CLOUDFW::AddressBook': 'https://yundun.console.aliyun.com',
  'ALIYUN::CMS::Contact': 'https://cloudmonitor.console.aliyun.com/#/contactListApp',
  'ALIYUN::CMS::ContactGroup': 'https://cloudmonitor.console.aliyun.com/#/contactListApp',
  'ALIYUN::CMS::EventRule': 'https://cloudmonitor.console.aliyun.com/#/alarmservice/product=&searchValue=&searchType=&searchProduct=',
  'ALIYUN::CMS::EventRuleTargets': 'https://cloudmonitor.console.aliyun.com/#/contact/list/',
  'ALIYUN::CMS::GroupMetricRule': 'https://cloudmonitor.console.aliyun.com/#/alarmservice/product=&searchValue=&searchType=&searchProduct=',
  'ALIYUN::CMS::MetricRuleTemplate': 'https://cloudmonitor.console.aliyun.com/#/alarmTemplate/',
  'ALIYUN::CMS::MonitorGroup': 'https://cloudmonitor.console.aliyun.com/#/groups/category=&region=&instanceIds=&groupType=&keyword=',
  'ALIYUN::CMS::SiteMonitor': 'https://cloudmonitornext.console.aliyun.com/newSite/details/@{PhysicalId}',
  'ALIYUN::CR::Namespace': 'https://cr.console.aliyun.com/@{RegionId}/instances/namespaces',
  'ALIYUN::CR::Repository': 'https://cr.console.aliyun.com/@{RegionId}/instances/repositories',
  'ALIYUN::CS::App': 'https://cs.console.aliyun.com/#/k8s/overview',
  'ALIYUN::CS::Cluster': 'https://cs.console.aliyun.com/#/k8s/cluster/@{PhysicalId}/info',
  'ALIYUN::CS::KubernetesCluster': 'https://cs.console.aliyun.com/?#/k8s/cluster/@{PhysicalId}/info?clusterType=Kubernetes&profile=&state=running&region=@{RegionId}',
  'ALIYUN::CS::ManagedEdgeKubernetesCluster': 'https://cs.console.aliyun.com/#/k8s/cluster/@{PhysicalId}/info/overview?clusterType=ManagedKubernetes&profile=Edge&state=running&region=@{RegionId}',
  'ALIYUN::CS::ManagedKubernetesCluster': 'https://cs.console.aliyun.com/?#/k8s/cluster/@{PhysicalId}/info?clusterType=ManagedKubernetes&profile=&state=running&region=@{RegionId}',
  'ALIYUN::CS::ServerlessKubernetesCluster': 'https://cs.console.aliyun.com/#/k8s/cluster/@{PhysicalId}/info/overview?clusterType=Ask&profile=ask.v2&state=running&region=@{RegionId}',
  'ALIYUN::DATAHUB::Project': 'https://dhsnext.console.aliyun.com/@{RegionId}/projects/@{PhysicalId}',
  'ALIYUN::DMS::Instance': 'https://dms.console.aliyun.com/#/dms/rsList',
  'ALIYUN::DMS::User': 'https://dms.console.aliyun.com/#/dms/user/config',
  'ALIYUN::DNS::Domain': 'https://dns.console.aliyun.com/#/dns/setting/@{PhysicalId}',
  'ALIYUN::DRDS::DrdsInstance ': 'https://drdsnew.console.aliyun.com/#/@{PhysicalId}/instDetail/drdsInstanceInfo',
  'ALIYUN::DTS::MigrationJob': 'https://dts.console.aliyun.com/?#/progress/@{PhysicalId}/config',
  'ALIYUN::EBS::DiskReplicaGroup': 'https://ebs.console.aliyun.com/consistencyGroup/@{RegionId}/@{PhysicalId}',
  'ALIYUN::EBS::DiskReplicaPair': 'https://ebs.console.aliyun.com/dataProtection/@{RegionId}/@{PhysicalId}',
  'ALIYUN::ECI::ContainerGroup': 'https://eci.console.aliyun.com/#/eci/@{RegionId}',
  'ALIYUN::ECI::ImageCache': 'https://eci.console.aliyun.com/?#/eci/@{RegionId}/image/@{PhysicalId}',
  'ALIYUN::ECS::AutoProvisioningGroup': 'https://ecs.console.aliyun.com/#/fleet/region/@{RegionId}',
  'ALIYUN::ECS::AutoSnapshotPolicy': 'https://ecs.console.aliyun.com/#/autoSnapshotPolicy/region/@{RegionId}',
  'ALIYUN::ECS::Command': 'https://ecs.console.aliyun.com/cloud-assistant/region/@{RegionId}/command?CommandId=@{PhysicalId}&operation=checkCommand&source=main&search=single',
  'ALIYUN::ECS::CopyImage': 'https://ecs.console.aliyun.com/#/image/region/@{RegionId}/imageList',
  'ALIYUN::ECS::CustomImage': 'https://ecs.console.aliyun.com/#/image/region/@{RegionId}/imageList',
  'ALIYUN::ECS::DedicatedHost': 'https://ecs.console.aliyun.com/#/ddh/region/@{RegionId}',
  'ALIYUN::ECS::DeploymentSet': 'https://ecs.console.aliyun.com/#/deploymentSet/region/@{RegionId}',
  'ALIYUN::ECS::Disk': 'https://ecs.console.aliyun.com/#/diskdetail/@{PhysicalId}/detail?regionId=@{RegionId}',
  'ALIYUN::ECS::DiskAttachment': 'https://ecs.console.aliyun.com/#/diskdetail/@{PhysicalId}/detail?regionId=@{RegionId}',
  'ALIYUN::ECS::EIP': 'https://ip.console.aliyun.com/#/eip',
  'ALIYUN::ECS::ForwardEntry': 'https://vpc.console.aliyun.com/#/nat/@{RegionId}/list',
  'ALIYUN::ECS::HpcCluster': 'https://ecs.console.aliyun.com/#/cluster/region/@{RegionId}',
  'ALIYUN::ECS::ImageDetail': 'https://ecs.console.aliyun.com/#/imageDetail/region/@{RegionId}/imageId/@{PhysicalId}',
  'ALIYUN::ECS::Instance': 'https://ecs.console.aliyun.com/#/server/@{PhysicalId}/detail?regionId=@{RegionId}',
  'ALIYUN::ECS::InstanceClone': 'https://ecs.console.aliyun.com/#/server/@{PhysicalId}/detail?regionId=@{RegionId}',
  'ALIYUN::ECS::InstanceGroup': 'https://ecs.console.aliyun.com/#/server/region/@{RegionId}?instanceIds=@{PhysicalId}',
  'ALIYUN::ECS::InstanceGroupClone': 'https://ecs.console.aliyun.com/#/server/region/@{RegionId}?instanceIds=@{PhysicalId}',
  'ALIYUN::ECS::Invocation': 'https://ecs.console.aliyun.com/#/cloud-assistant/region/@{RegionId}/result?operation=checkResult&InvokeId=@{PhysicalId}&source=main&search=single',
  'ALIYUN::ECS::JoinSecurityGroup': 'https://ecs.console.aliyun.com/#/securityGroupDetail/region/@{RegionId}/groupId/@{PhysicalId}/instance',
  'ALIYUN::ECS::LaunchTemplate': 'https://ecs.console.aliyun.com/#/launchTemplate/region/@{RegionId}',
  'ALIYUN::ECS::NatGateway': 'https://vpc.console.aliyun.com/#/nat/@{RegionId}/detail/@{PhysicalId}/info',
  'ALIYUN::ECS::NetworkInterface': 'https://ecs.console.aliyun.com/#/networkInterfaces/region/@{RegionId}',
  'ALIYUN::ECS::NetworkInterfaceAttachment': 'https://ecs.console.aliyun.com/#/server/@{PhysicalId}/eni?regionId=@{RegionId}',
  'ALIYUN::ECS::NetworkInterfacePermission': 'https://ecs.console.aliyun.com/#/server/@{PhysicalId}/eni?regionId=@{RegionId}',
  'ALIYUN::ECS::PrefixList': 'https://ecs.console.aliyun.com/#/prefixListDetail/@{RegionId}/@{PhysicalId}/items',
  'ALIYUN::ECS::PrepayInstance': 'https://ecs.console.aliyun.com/#/server/@{PhysicalId}/detail?regionId=@{RegionId}',
  'ALIYUN::ECS::Region': 'https://ecs.console.aliyun.com/#/',
  'ALIYUN::ECS::Route': 'https://vpc.console.aliyun.com/vpc/@{RegionId}/route-tables/@{PhysicalId}',
  'ALIYUN::ECS::RunCommand': 'https://ecs.console.aliyun.com/#/cloud-assistant/region/@{RegionId}/result?operation=checkResult&InvokeId=@{PhysicalId}&source=main&search=single',
  'ALIYUN::ECS::SecurityGroup': 'https://ecs.console.aliyun.com/#/securityGroupDetail/region/@{RegionId}/groupId/@{PhysicalId}/instance',
  'ALIYUN::ECS::SecurityGroupClone': 'https://ecs.console.aliyun.com/#/securityGroupDetail/region/@{RegionId}/groupId/@{PhysicalId}/rule',
  'ALIYUN::ECS::SecurityGroupEgress': 'https://ecs.console.aliyun.com/#/securityGroupDetail/region/@{RegionId}/groupId/@{PhysicalId}/rule/internet',
  'ALIYUN::ECS::SecurityGroupIngress': 'https://ecs.console.aliyun.com/#/securityGroupDetail/region/@{RegionId}/groupId/@{PhysicalId}/detail/intranetIngress',
  'ALIYUN::ECS::Snapshot': 'https://ecs.console.aliyun.com/#/server/@{PhysicalId}/snapshot?regionId=@{RegionId}',
  'ALIYUN::ECS::SNatEntry': 'https://vpc.console.aliyun.com/#/nat/@{RegionId}/list',
  'ALIYUN::ECS::SSHKeyPair': 'https://ecs.console.aliyun.com/#/keyPair/region/@{RegionId}',
  'ALIYUN::ECS::SSHKeyPairAttachment': 'https://ecs.console.aliyun.com/#/keyPair/region/@{RegionId}',
  'ALIYUN::ECS::VPC': 'https://vpc.console.aliyun.com/#/vpc/@{RegionId}/detail/@{PhysicalId}/info',
  'ALIYUN::ECS::VSwitch': 'https://vpc.console.aliyun.com/vpc/@{RegionId}/switches/@{PhysicalId}',
  'ALIYUN::EDAS::Cluster': 'https://edasnext.console.aliyun.com/#/edasResources/clusterInfo?regionNo=@{RegionId}&clusterId=@{PhysicalId}&networkMode=2&oversoldFactor=1&clusterType=2',
  'ALIYUN::EDAS::UserDefineRegion': 'https://edasnext.console.aliyun.com/#/namespaces?regionNo=@{RegionId}',
  'ALIYUN::EHPC::Cluster': 'https://ehpc.console.aliyun.com/#/cluster?regionId=@{RegionId}',
  'ALIYUN::ElasticSearch::Instance': 'https://elasticsearch.console.aliyun.com/@{RegionId}/instances/@{PhysicalId}',
  'ALIYUN::EMR::Cluster': 'https://emr.console.aliyun.com/#/@{RegionId}/cluster/@{PhysicalId}/detail',
  'ALIYUN::EMR::Cluster2': 'https://emr-next.console.aliyun.com/#/region/@{RegionId}/resource/all/ecs/detail/@{PhysicalId}/overview',
  'ALIYUN::ENS::Instance': 'https://ens.console.aliyun.com/#/instance/list',
  'ALIYUN::ESS::AlarmTask': 'https://essnew.console.aliyun.com/#/task/v3/alarm/region/@{RegionId}',
  'ALIYUN::ESS::LifecycleHook': 'https://essnew.console.aliyun.com/#/v3/group/detail/@{RegionId}/@{PhysicalId}/lifecycleHook',
  'ALIYUN::ESS::ScalingConfiguration': 'https://ess.console.aliyun.com/#/ess/region/@{RegionId}',
  'ALIYUN::ESS::ScalingGroup': 'https://essnew.console.aliyun.com/#/v3/group/detail/@{RegionId}/@{PhysicalId}/basicInfo',
  'ALIYUN::ESS::ScalingGroupEnable': 'https://ess.console.aliyun.com/#/v3/group/detail/@{RegionId}/@{PhysicalId}',
  'ALIYUN::ESS::ScheduledTask': 'https://essnew.console.aliyun.com/#/task/timer/region/@{RegionId}',
  'ALIYUN::FC::CustomDomain': 'https://fcnext.console.aliyun.com/@{RegionId}/domains/@{PhysicalId}/detail',
  'ALIYUN::FC::Function': 'https://fcnext.console.aliyun.com/@{RegionId}/services/@{ServiceName}/function-detail/@{FunctionName}/LATEST?tab=code',
  'ALIYUN::FC::FunctionInvoker': 'https://fcnext.console.aliyun.com/fc/service/@{RegionId}',
  'ALIYUN::FC::Service': 'https://fcnext.console.aliyun.com/@{RegionId}/services/@{ServiceName}/functions',
  'ALIYUN::FC::Trigger': 'https://fcnext.console.aliyun.com/@{RegionId}/services/@{ServiceName}/function-detail/@{FunctionName}/LATEST?tab=trigger',
  'ALIYUN::Flink::Instance': 'https://realtime-compute.console.aliyun.com/#/region/@{RegionId}/dashboard/serverless/asi',
  'ALIYUN::FNF::Flow': 'https://fnf.console.aliyun.com/fnf/@{RegionId}/flows/item/{FlowName}',
  'ALIYUN::GA::Accelerator': 'https://ga.console.aliyun.com/list/@{PhysicalId}',
  'ALIYUN::GA::BandwidthPackage': 'https://ga.console.aliyun.com/bandwidth',
  'ALIYUN::GPDB::DBInstance': 'https://gpdbnext.console.aliyun.com/gpdb/@{RegionId}/list/nav/@{PhysicalId}/storageelastic/basic',
  'ALIYUN::GPDB::ElasticDBInstance': 'https://gpdbnext.console.aliyun.com/gpdb/@{RegionId}/list/nav/@{PhysicalId}/storageelastic/basic',
  'ALIYUN::GWS::Cluster': 'https://gws.console.aliyun.com/#/cluster/@{RegionId}',
  'ALIYUN::GWS::Instance': 'https://gws.console.aliyun.com/#/instance/@{RegionId}',
  'ALIYUN::HBR::RestoreJob': 'https://hbr.console.aliyun.com/#/cloud/sql?tab=restore&_k=nf0h8e',
  'ALIYUN::KAFKA::Instance': 'https://kafka.console.aliyun.com/region/@{RegionId}/instance/@{PhysicalId}/detail',
  'ALIYUN::KAFKA::Topic': 'https://kafka.console.aliyun.com/#/TopicManagement?regionId=@{RegionId}',
  'ALIYUN::KMS::Alias': 'https://kms.console.aliyun.com/@{RegionId}/key/detail/@{PhysicalId}',
  'ALIYUN::KMS::Key': 'https://kms.console.aliyun.com/@{RegionId}/key/detail/@{PhysicalId}',
  'ALIYUN::Lindorm::Instance': 'https://lindorm.console.aliyun.com/@{RegionId}/cluster/lindorm/@{PhysicalId}/info',
  'ALIYUN::MEMCACHE::Instance': 'https://kvstore.console.aliyun.com/?instanceType=Memcache#/home/<USER>',
  'ALIYUN::MNS::Queue': 'https://mns.console.aliyun.com/#/?regionId=@{RegionId}',
  'ALIYUN::MNS::Topic': 'https://mns.console.aliyun.com/#/Mnstheme?regionId=@{RegionId}',
  'ALIYUN::MONGODB::Instance': 'https://mongodb.console.aliyun.com/#/mongodb/detail/@{PhysicalId}/info',
  'ALIYUN::MONGODB::PrepayInstance': 'https://mongodb.console.aliyun.com/#/mongodb/detail/@{PhysicalId}/info',
  'ALIYUN::MONGODB::ServerlessInstance': 'https://mongodb.console.aliyun.com/serverless/@{RegionId}/instances/@{PhysicalId}/basicInfo',
  'ALIYUN::MONGODB::ShardingInstance': 'https://mongodb.console.aliyun.com/sharding/@{RegionId}/instances/@{PhysicalId}/basicInfo',
  'ALIYUN::MSE::Cluster': 'https://mse.console.aliyun.com/#/Home',
  'ALIYUN::MSE::Gateway': 'https://mse.console.aliyun.com/#/gateway/basicInfo?Id=@{PhysicalId}',
  'ALIYUN::NAS::AccessRule': 'https://nasnext.console.aliyun.com/@{RegionId}/access-group',
  'ALIYUN::NAS::DataFlow': 'https://nasnext.console.aliyun.com/@{RegionId}/filesystem/@{FileSystemId}/dataflow',
  'ALIYUN::NAS::Fileset': 'https://nasnext.console.aliyun.com/@{RegionId}/filesystem/@{FileSystemId}/fileset',
  'ALIYUN::NAS::FileSystem': 'https://nasnext.console.aliyun.com/@{RegionId}/filesystem/@{PhysicalId}/info?sourceUrl=FileSystem',
  'ALIYUN::NAS::MountTarget': 'https://nasnext.console.aliyun.com/@{RegionId}/filesystem/@{PhysicalId}/mount',
  'ALIYUN::NAS::ProtocolMountTarget': 'https://nasnext.console.aliyun.com/@{RegionId}/filesystem/@{FileSystemId}/protocolservice',
  'ALIYUN::NAS::ProtocolService': 'https://nasnext.console.aliyun.com/@{RegionId}/filesystem/@{FileSystemId}/protocolservice',
  'ALIYUN::NLB::LoadBalancer': 'https://slb.console.aliyun.com/nlb/@{RegionId}/nlbs/@{PhysicalId}',
  'ALIYUN::NLB::ServerGroup': 'https://slb.console.aliyun.com/nlb/@{RegionId}/server-groups/@{PhysicalId}',
  'ALIYUN::OOS::Execution': 'https://oos.console.aliyun.com/@{RegionId}/execution/detail/@{PhysicalId}',
  'ALIYUN::OOS::Parameter': 'https://oos.console.aliyun.com/@{RegionId}/parameter/detail/@{PhysicalId}',
  'ALIYUN::OOS::PatchBaseline': 'https://oos.console.aliyun.com/@{RegionId}/patch/baseline/detail/@{PhysicalId}',
  'ALIYUN::OOS::SecretParameter': 'https://oos.console.aliyun.com/@{RegionId}/parameter/detailSecret/@{PhysicalId}',
  'ALIYUN::OOS::StateConfiguration': 'https://oos.console.aliyun.com/@{RegionId}/inventory/modify/@{PhysicalId}',
  'ALIYUN::OOS::Template': 'https://oos.console.aliyun.com/@{RegionId}/template/private/detail/@{PhysicalId}',
  'ALIYUN::OSS::Bucket': 'https://oss.console.aliyun.com/bucket/oss-@{RegionId}/@{PhysicalId}/overview',
  'ALIYUN::OTS::Instance': 'https://ots.console.aliyun.com/index#/list/@{RegionId}',
  'ALIYUN::OTS::Table': 'https://ots.console.aliyun.com/index#/list/@{RegionId}',
  'ALIYUN::OTS::VpcBinder': 'https://ots.console.aliyun.com/index#/list/@{RegionId}',
  'ALIYUN::POLARDB::Account': 'https://polardb.console.aliyun.com/@{RegionId}/cluster/@{PhysicalId}/account',
  'ALIYUN::POLARDB::DBCluster': 'https://polardb.console.aliyun.com/@{RegionId}/cluster/@{PhysicalId}/baseInfo',
  'ALIYUN::POLARDB::DBClusterAccessWhiteList': 'https://polardb.console.aliyun.com/@{RegionId}/cluster/@{PhysicalId}/baseInfo',
  'ALIYUN::POLARDB::DBClusterEndpoint': 'https://polardb.console.aliyun.com/@{RegionId}/cluster/@{PhysicalId}/baseInfo',
  'ALIYUN::POLARDB::DBClusterEndpointAddress': 'https://polardb.console.aliyun.com/@{RegionId}/cluster/@{PhysicalId}/baseInfo',
  'ALIYUN::POLARDB::DBInstance': 'https://polardb.console.aliyun.com/@{RegionId}/cluster/@{PhysicalId}/database',
  'ALIYUN::PrivateLink::VpcEndpoint': 'https://vpc.console.aliyun.com/endpoint/@{RegionId}/endpoints/@{PhysicalId}',
  'ALIYUN::PrivateLink::VpcEndpointService': 'https://vpc.console.aliyun.com/endpointservice/@{RegionId}/endpointservices/@{PhysicalId}',
  'ALIYUN::PVTZ::Zone': 'https://dns.console.aliyun.com/#/privateZone/detail/@{PhysicalId}',
  'ALIYUN::PVTZ::ZoneRecord': 'https://dns.console.aliyun.com/#/privateZone/setting/record/@{PhysicalId}',
  'ALIYUN::RAM::AccessKey': 'https://usercenter.console.aliyun.com/#/manage/ak',
  'ALIYUN::RAM::Group': 'https://ram.console.aliyun.com/#/group/detail/@{PhysicalId}/info',
  'ALIYUN::RAM::ManagedPolicy': 'https://ram.console.aliyun.com/policies/@{PhysicalId}/Custom',
  'ALIYUN::RAM::Role': 'https://ram.console.aliyun.com/roles/@{PhysicalId}',
  'ALIYUN::RAM::User': 'https://ram.console.aliyun.com/#/user/detail/@{PhysicalId}/info',
  'ALIYUN::RAM::UserToGroupAddition': 'https://ram.console.aliyun.com/groups',
  'ALIYUN::RDS::DBInstance': 'https://rdsnext.console.aliyun.com/#/detail/@{PhysicalId}/basicInfo?region=@{RegionId}',
  'ALIYUN::RDS::DBInstanceClone': 'https://rdsnext.console.aliyun.com/#/detail/@{PhysicalId}/basicInfo?region=@{RegionId}',
  'ALIYUN::RDS::DBInstanceParameterGroup': 'https://rdsnext.console.aliyun.com/#/detail/@{PhysicalId}/basicInfo?region=@{RegionId}',
  'ALIYUN::RDS::DBInstanceSecurityIps': 'https://rdsnext.console.aliyun.com/#/detail/@{PhysicalId}/security/whiteList?region=@{RegionId}',
  'ALIYUN::RDS::PrepayDBInstance': 'https://rdsnext.console.aliyun.com/#/detail/@{PhysicalId}/basicInfo?region=@{RegionId}',
  'ALIYUN::RDS::ReadOnlyDBInstance': 'https://rdsnext.console.aliyun.com/#/detail/@{PhysicalId}/basicInfo?region=@{RegionId}',
  'ALIYUN::REDIS::Instance': 'https://kvstore.console.aliyun.com/#/detail/@{PhysicalId}/Normal/CLASSIC/info?regionId=@{RegionId}',
  'ALIYUN::REDIS::PrepayInstance': 'https://kvstorenext.console.aliyun.com/#/home/<USER>',
  'ALIYUN::ResourceManager::ResourceGroup': 'https://resourcemanager.console.aliyun.com/resource-groups/@{PhysicalId}',
  'ALIYUN::ROCKETMQ::Instance': 'https://ons.console.aliyun.com/#/InstanceManagement?instanceId=@{PhysicalId}&regionId=@{RegionId}',
  'ALIYUN::ROS::CommonProblem': 'https://help.aliyun.com/document_detail/28929.html',
  'ALIYUN::ROS::DocApi': 'https://help.aliyun.com/document_detail/131032.html',
  'ALIYUN::ROS::DocSDK': 'https://help.aliyun.com/document_detail/28928.html',
  'ALIYUN::ROS::GuideStart': 'https://help.aliyun.com/document_detail/48749.html',
  'ALIYUN::ROS::PackageOrder': 'https://expense.console.aliyun.com/order/pay.json?orderId=@{PhysicalId}',
  'ALIYUN::ROS::ResourcePackage': 'https://expense.console.aliyun.com/order/pay.json?orderId=@{PhysicalId}',
  'ALIYUN::ROS::StackGroup': 'https://ros.console.aliyun.com/@{RegionId}/stackGroup/@{PhysicalId}',
  'ALIYUN::ROS::StartUse': 'https://help.aliyun.com/product/28850.html',
  'ALIYUN::ROS::TemplateExample': 'https://rosnext.console.aliyun.com/samples',
  'ALIYUN::ROS::TemplateScratch': 'https://ros.console.aliyun.com/@{RegionId}/scratch/@{PhysicalId}',
  'ALIYUN::ROS::UnderstandingDetails': 'https://help.aliyun.com/product/28850.html',
  'ALIYUN::ROS::UserManual': 'https://help.aliyun.com/document_detail/28858.html',
  'ALIYUN::ROS::UseRosIntegrate': 'https://help.aliyun.com/knowledge_detail/67256.html',
  'ALIYUN::SAE::Application': 'https://sae.console.aliyun.com/#/AppList/AppDetail?appId=@{PhysicalId}&regionId=@{RegionId}',
  'ALIYUN::SAE::Namespace': 'https://sae.console.aliyun.com/#/NameSpace?regionId=@{RegionId}',
  'ALIYUN::SAG::ACL': 'https://smartag.console.aliyun.com/acl/@{RegionId}/acls/@{PhysicalId}',
  'ALIYUN::SAG::ACLRule': 'https://smartag.console.aliyun.com/acl/@{RegionId}/acls/@{PhysicalId}',
  'ALIYUN::SAG::CloudConnectNetwork': 'https://smartag.console.aliyun.com/ccn/@{RegionId}/ccns/@{PhysicalId}',
  'ALIYUN::SAG::SmartAccessGateway': 'https://smartag.console.aliyun.com/hardware/@{RegionId}/hardwares/@{PhysicalId}',
  'ALIYUN::SLB::AccessControl': 'https://slb.console.aliyun.com/slb/@{RegionId}/acls/@{PhysicalId}',
  'ALIYUN::SLB::BackendServerAttachment': 'https://slb.console.aliyun.com/slb/@{RegionId}/slbs/@{PhysicalId}/servers',
  'ALIYUN::SLB::Certificate': 'https://slb.console.aliyun.com/slb/@{RegionId}/certs',
  'ALIYUN::SLB::Listener': 'https://slb.console.aliyun.com/slb/@{RegionId}/slbs/@{PhysicalId}/listeners',
  'ALIYUN::SLB::LoadBalancer': 'https://slb.console.aliyun.com/slb/@{RegionId}/slbs/@{PhysicalId}',
  'ALIYUN::SLB::LoadBalancerClone': 'https://slb.console.aliyun.com/slb/@{RegionId}/slbs/@{PhysicalId}',
  'ALIYUN::SLB::MasterSlaveServerGroup': 'https://slb.console.aliyun.com/slb/@{RegionId}/slbs/@{PhysicalId}/ms-server-groups/',
  'ALIYUN::SLB::Rule': 'https://slb.console.aliyun.com/slb/@{RegionId}/slbs/@{PhysicalId}/listeners',
  'ALIYUN::SLB::VServerGroup': 'https://slb.console.aliyun.com/slb/@{RegionId}/slbs/@{LoadBalancerId}/v-server-groups/@{PhysicalId}/detail',
  'ALIYUN::SLS::Logstore': 'https://sls.console.aliyun.com/lognext/project/@{PhysicalId}/overview',
  'ALIYUN::SLS::MachineGroup': 'https://sls.console.aliyun.com/lognext/project/@{PhysicalId}/overview',
  'ALIYUN::SLS::Project': 'https://sls.console.aliyun.com/lognext/project/@{PhysicalId}/overview',
  'ALIYUN::TSDB::HiTSDBInstance': 'https://tsdb.console.aliyun.com/#/clusterDetail/@{RegionId}/@{PhysicalId}/tsdb_influxdb',
  'ALIYUN::VPC::CommonBandwidthPackage': 'https://vpcnext.console.aliyun.com/cbwp/@{RegionId}/cbwps/@{PhysicalId}',
  'ALIYUN::VPC::CustomerGateway': 'https://vpcnext.console.aliyun.com/vpn/@{RegionId}/vpn-clients',
  'ALIYUN::VPC::EIP': 'https://vpc.console.aliyun.com/eip/@{RegionId}/eips?AllocationId=@{PhysicalId}',
  'ALIYUN::VPC::HaVip': 'https://vpcnext.console.aliyun.com/vpc/@{RegionId}/havips/@{PhysicalId}',
  'ALIYUN::VPC::NatGateway': 'https://vpc.console.aliyun.com/nat/@{RegionId}/nats/@{PhysicalId}',
  'ALIYUN::VPC::RouterInterface': 'https://vpc.console.aliyun.com/vpc/@{RegionId}/route-tables/@{PhysicalId}',
  'ALIYUN::VPC::RouteTable': 'https://vpc.console.aliyun.com/vpc/@{RegionId}/route-tables//@{PhysicalId}',
  'ALIYUN::VPC::RouteTableAssociation': 'https://vpc.console.aliyun.com/vpc/@{RegionId}/route-tables/@{PhysicalId}',
  'ALIYUN::VPC::SnatEntry': 'https://vpc.console.aliyun.com/nat/@{RegionId}/nats/@{PhysicalId}/snats',
  'ALIYUN::VPC::SslVpnClientCert': 'https://vpc.console.aliyun.com/sslvpn/@{RegionId}/vpn-clients',
  'ALIYUN::VPC::SslVpnServer': 'https://vpc.console.aliyun.com/sslvpn/@{RegionId}/vpn-servers',
  'ALIYUN::VPC::VpnConnection': 'https://vpc.console.aliyun.com/vpn/@{RegionId}/vpn-connections',
  'ALIYUN::VPC::VpnGateway': 'https://vpc.console.aliyun.com/vpn/@{RegionId}/vpns/@{PhysicalId}',
  'ALIYUN::WAF::Instance': 'https://yundun.console.aliyun.com/?p=wafnext#/waf/@{RegionId}/manage/domain/list',
  cloudToolkitLink: 'https://help.aliyun.com/document_detail/171807.html',
  cloudToolkitLinkByTerraform: 'https://www.alibabacloud.com/help/zh/ros/user-guide/ros-features-and-resources-supported-by-terraform',
  computenest_app: 'https://computenest.console.aliyun.com/app/list',
  cwsUrl: 'https://cws.alicdn.com',
  driftResources: 'https://help.aliyun.com/document_detail/158317.html',
  ecsDiagnoseUrl: 'https://ecs.console.aliyun.com/legacy?hideTopbar=true&disableNavigation=true&showTabs=false#/diagnose/region/{@regionId}',
  enableServiceAccess: 'https://help.aliyun.com/document_detail/298229.html',
  'error.center': 'https://error-center.aliyun.com/troubleshoot?q={@code}',
  'error.center.ros': 'https://error-center.aliyun.com/status/product/ROS',
  'feature:design-editor.old': 'https://ros.console.aliyun.com/?oldVersion=true#/visualeditor/tool',
  'feature:nav.cloud_laboratory': 'https://developer.aliyun.com/adc/',
  'feature:nav.oos': 'https://oos.console.aliyun.com',
  'feature:nav.quotas': 'https://quotas.console.aliyun.com/products/ros/quotas',
  'feature:nav.workbench': 'https://workbench.aliyun.com',
  free_url: 'https://free.aliyun.com/',
  landingZone: 'https://open.aliyun.com/landing-zone',
  module_help: 'https://help.aliyun.com/document_detail/2503210.html',
  oosParameterLink: 'https://oos.console.aliyun.com/{@regionId}/parameter',
  'ore.actionDoc': 'https://www.alibabacloud.com/help/operation-orchestration-service/latest/{actionName}',
  'ore.actionUrl': 'https://help.aliyun.com/document_detail/176952.html',
  'ore.addRamPolicyForComputenest': 'https://help.aliyun.com/document_detail/433121.html',
  'ore.albInstanceUrl': 'https://slb.console.aliyun.com/alb/{regionId}/albs',
  'ore.alibabacloudFecsHost': 'https://fecs.console.alibabacloud.com',
  'ore.aliyunCloudMonitorSendOperationMessageToComputeNestRoleUrl': 'https://ram.console.aliyun.com/role/authorize?request=%7B%22ReturnUrl%22%3A%22https%3A%2F%2Fram.console.aliyun.com%22%2C%22Services%22%3A%5B%7B%22Roles%22%3A%5B%7B%22RoleName%22%3A%22AliyunCloudMonitorSendOperationMessageToComputeNestRole%22%2C%22TemplateId%22%3A%22AliyunCloudMonitorSendOperationMessageToComputeNestRole%22%7D%5D%2C%22Service%22%3A%22CloudMonitor%22%7D%5D%7D',
  'ore.applyDeploymentLinkPermissionUrl': 'https://help.aliyun.com/document_detail/446238.html',
  'ore.authenticationDoc': 'https://help.aliyun.com/document_detail/428525.html',
  'ore.authorizeUrl': 'https://computenest.console.aliyun.com/connected/{platform}',
  'ore.chargeOverviewUrl': 'https://help.aliyun.com/document_detail/438845.html',
  'ore.checkQuotasUrl': 'https://quotas.console.aliyun.com/products',
  'ore.cloudAssistantCommonErrorCodeDoc': 'https://help.aliyun.com/document_detail/87029.html#section-ar5-j06-zre',
  'ore.cloudMarketPurchase': 'https://market.aliyun.com/products/57252001/{commodityCode}.html',
  'ore.cloudMonitorRule': 'https://help.aliyun.com/document_detail/28619.html',
  'ore.composerUrl': 'https://ros.console.aliyun.com/composer',
  'ore.createAlbAclUrl': 'https://slb.console.aliyun.com/alb/{regionId}/acls/new',
  'ore.createAutoSnapshotPolicy': 'https://ecs.console.aliyun.com/autoSnapshotPolicy/region/{regionId}',
  'ore.createCluster': 'https://ehpc.console.aliyun.com/#/clustercreation?referrer=cluster&regionId={regionId}',
  'ore.createContactGroup': 'https://cloudmonitornext.console.aliyun.com/alert-contactGroup',
  'ore.createEASResourceLink': 'https://common-buy.aliyun.com/?commodityCode=learn_EasDedicatedPostpay_public_cn',
  'ore.createECSCommand': 'https://ecs.console.aliyun.com/#/cloudAssistant/region/{regionId}/',
  'ore.createEmrInstance': 'https://emr-next.console.aliyun.com/#/resource/all/create/ecs',
  'ore.createFileSystem': 'https://nasnext.console.aliyun.com/{regionId}/filesystem',
  'ore.createFlowOrg': 'https://accountid-devops.aliyun.com/create-org',
  'ore.createFunctionFC2': 'https://fcnext.console.aliyun.com/{regionId}/services/{serviceName}/function-create',
  'ore.createFunctionFC3': 'https://fcnext.console.aliyun.com/{regionId}/functions/create',
  'ore.createHologresInstance': 'https://hologram.console.aliyun.com/{regionId}/instance',
  'ore.createK8sCluster': 'https://cs.console.aliyun.com/#/k8s/cluster/list',
  'ore.createKafkaInstance': 'https://kafka.console.aliyun.com/region/{regionId}/instances',
  'ore.createKeyPair': 'https://ecs.console.aliyun.com/#/keyPair/region/{regionId}/create',
  'ore.createLaunchTemplate': 'https://ecs-buy.aliyun.com/template/#/{regionId}',
  'ore.createLaunchTemplateVersion': 'https://ecs-buy.aliyun.com/template/#/{regionId}?launchTemplateId={launchTemplateId}&launchTemplateCreateType=version',
  'ore.createLindormInstance': 'https://lindorm.console.aliyun.com/{regionId}/cluster',
  'ore.createManagedInstance': 'https://ecs.console.aliyun.com/cloud-assistant/region/{regionId}/managed?operation=',
  'ore.createNASFileSystemLink': 'https://common-buy.aliyun.com/?commodityCode=nas_standard_post&regionId={regionId}#/buy',
  'ore.createNatGatewayUrl': 'https://vpc.console.aliyun.com/vpc-nat/{regionId}/nats',
  'ore.createNSTArtifactUrl': 'https://computenest.console.aliyun.com/artifact/create/{regionId}',
  'ore.createOOSPackage': 'https://oos.console.aliyun.com/{regionId}/extension/create',
  'ore.createOOSParameter': 'https://oos.console.aliyun.com/{regionId}/parameter/create',
  'ore.createOOSSecretParameter': 'https://oos.console.aliyun.com/{regionId}/parameter/createSecret',
  'ore.createOOSTemplate': 'https://oos.console.aliyun.com/{regionId}/template/private',
  'ore.createOSSBucket': 'https://oss.console.aliyun.com/bucket',
  'ore.createOSSObject': 'https://oss.console.aliyun.com/bucket/oss-{regionId}/{bucketName}/object',
  'ore.createPatchBaselines': 'https://oos.console.aliyun.com/cn-beijing/patch/baseline/create',
  'ore.createRAMRoleLink': 'https://ram.console.aliyun.com/roles',
  'ore.createRAMUserLink': 'https://ram.console.aliyun.com/users/new',
  'ore.createRDS': 'https://rdsbuy.console.aliyun.com/',
  'ore.createResourceGroup': 'https://resourcemanager.console.aliyun.com/resource-groups',
  'ore.createROSModule': 'https://rosnext.console.aliyun.com/registration/_modules/create',
  'ore.createROSTemplate': 'https://rosnext.console.aliyun.com/{regionId}/templates/private',
  'ore.createScalingGroupUrl': 'https://essnew.console.aliyun.com/#/v3/group/list/{regionId}?sourceType={sourceType}&autoCreateScalingGroup=true',
  'ore.createSecurityGroupUrl': 'https://ecs.console.aliyun.com/#/securityGroup/region/{regionId}/create',
  'ore.createServerCertificatesUrl': 'https://slb.console.aliyun.com/slb/{regionId}/certs/new',
  'ore.createServiceFC2': 'https://fcnext.console.aliyun.com/{regionId}/services',
  'ore.createServiceInstanceUrl': 'https://help.aliyun.com/document_detail/444596.html',
  'ore.createSlbAclUrl': 'https://slb.console.aliyun.com/slb/{regionId}/acls/new',
  'ore.createTicketUrl': 'https://smartservice.console.aliyun.com/service/create-ticket',
  'ore.createVPCUrl': 'https://vpc.console.aliyun.com/vpc/{regionId}/vpcs/new',
  'ore.createVSwitchUrl': 'https://vpc.console.aliyun.com/vpc/{regionId}/switches/new',
  'ore.cronHelpDocLink': 'https://help.aliyun.com/document_detail/169784.html',
  'ore.domainRegistration': 'https://beian.aliyun.com/',
  'ore.ecdDesktopDetail': 'https://eds.console.aliyun.com/detail/{regionId}?id={desktopId}&type=deskTop',
  'ore.eciDetail': 'https://eci.console.aliyun.com/#/eci/{regionId}/detail/{instanceId}/containers',
  'ore.ecsBuy': 'https://ecs-buy.aliyun.com',
  'ore.ecsDetail': 'https://ecs.console.aliyun.com/#/server/{instanceId}/detail?regionId={regionId}',
  'ore.ecsInstanceList': 'https://ecs.console.aliyun.com/#/server/region/{regionId}',
  'ore.ehpcRamRoleLink': 'https://ram.console.aliyun.com/role/authorize?request=%7B%22ReturnUrl%22%3A%22https%3A%2F%2Fehpc.console.aliyun.com%22%2C%22Services%22%3A%5B%7B%22Roles%22%3A%5B%7B%22RoleName%22%3A%22AliyunEHPCDefaultRole%22%2C%22TemplateId%22%3A%22DefaultRole%22%7D%5D%2C%22Service%22%3A%22EHPC%22%7D%5D%7D',
  'ore.elasticStrengthUrl': 'https://help.aliyun.com/document_detail/2261416.html',
  'ore.errorCenter': 'https://error-center.aliyun.com/status/search?Keyword={code}',
  'ore.errorCenterTroubleshoot': 'https://error-center.aliyun.com/troubleshoot?q={query}',
  'ore.extensionDetailUrl': 'https://oos.console.aliyun.com/cn-hangzhou/extension/detail/{extensionName}',
  'ore.fecsHost': 'https://fecs.console.aliyun.com',
  'ore.flowServiceConnection': 'https://flow.aliyun.com/setting/service-connection',
  'ore.fpgaDocUrl': 'https://www.aliyun.com/product/ecs/fpga',
  'ore.giteeAuthorizeUrl': 'https://gitee.com/oauth/authorize?client_id=f6767dabdb987722f06b35c320935cb3ad8aa714efc8614a3604730a98ead6d2&redirect_uri={redirectUri}&response_type=code',
  'ore.githubAuthorizeUrl': 'https://github.com/login/oauth/authorize?client_id=205d4b734a2bdc2c1764&scope=admin:repo_hook,admin:org_hook,repo,admin:org&redirect_uri={redirectUri}',
  'ore.gpuDocUrl': 'https://www.aliyun.com/product/ecs/gpu',
  'ore.instanceTypeFamilyHelp': 'https://help.aliyun.com/document_detail/25378.html',
  'ore.inventoryHelpLink': 'https://help.aliyun.com/document_detail/208316.html',
  'ore.marketBizlistUrl': 'https://market.console.aliyun.com',
  'ore.marketLegalAgreementUrl': 'https://terms.aliyun.com/legal-agreement/terms/suit_bu1_ali_cloud/suit_bu1_ali_cloud201802011628_45290.html',
  'ore.mcmsURL': 'https://lang.alicdn.com/mcms/ecs-ore-store/0.0.66/ecs-ore-store.json',
  'ore.MemVergeDeployDoc': 'https://help.aliyun.com/document_detail/437332.html',
  'ore.modifyParameterDesc': 'https://help.aliyun.com/document_detail/609220.html',
  'ore.nestServiceDetailUrl': 'https://computenest.console.aliyun.com/{type}/{regionId}/serviceDetail/{serviceId}/{serviceVersion}',
  'ore.nestServiceInstanceDetailUrl': 'https://computenest.console.aliyun.com/{type}/{regionId}/serviceInstanceDetail/{serviceInstanceId}',
  'ore.nextApiURL': 'https://api.aliyun.com/api/{product}/{version}/{action}',
  'ore.nlbInstanceUrl': 'https://slb.console.aliyun.com/nlb/{regionId}/nlbs',
  'ore.nstLegalAgreementUrl': 'https://terms.aliyun.com/legal-agreement/terms/suit_bu1_ali_cloud/suit_bu1_ali_cloud202110281027_83591.html',
  'ore.nstServiceNaneWhiteList': '["PAI","GPDB"]',
  'ore.offlinePriceInfo': 'https://g.alicdn.com/aliyun/ecs-price-info/2.0.276/price/js_price/filter_regions_price/{region_id}.js',
  'ore.oosExecutionDetail': 'https://oos.console.aliyun.com/{regionId}/execution/detail/{id}',
  'ore.oosFAQUrl': 'https://help.aliyun.com/knowledge_detail/123154.html',
  'ore.oosForbiddenRAMDoc': 'https://help.aliyun.com/document_detail/123154.html#section-itk-vao-6v2',
  'ore.oosGenerateOSSUrl': 'https://oos.console.aliyun.com/?_MINI_CMP_=true&component={component}&props={props}',
  'ore.oosOverviewUrl': 'https://oos.console.aliyun.com/overview',
  'ore.oosParameterLink': 'https://oos.console.aliyun.com/{regionId}/parameter',
  'ore.oosPrivateExtensionDetail': 'https://oos.console.aliyun.com/{regionId}/extension/detail/{extensionName}',
  'ore.oosPublicExtensionDetail': 'https://oos.console.aliyun.com/{regionId}/extension/public_detail/{extensionName}',
  'ore.oosTemplateDetail': 'https://oos.console.aliyun.com/{regionId}/template/{shareType}/detail/{teplateName}',
  'ore.operationNoticeLink': 'https://help.aliyun.com/document_detail/437725.htm',
  'ore.orderPayUrl': 'https://finance.aliyun.com/order/trade_pay.htm?order_id={orderId}',
  'ore.orderTradePay': 'https://cashier.aliyun.com/order/trade_pay.htm?order_id={orderId}',
  'ore.OSSUrl': 'https://oss.console.aliyun.com/bucket/{regionId}/{bucketName}/object',
  'ore.parFreeLink': 'https://free.aliyun.com/?product=9602825&crowd=personal',
  'ore.pocPrivateLinkDesc': 'https://help.aliyun.com/document_detail/444596.html#section-ncr-aks-b7l',
  'ore.policyFullAccessUrl': 'https://ram.console.aliyun.com/policies/AliyunComputeNestPolicyForSupplierRole/System/content',
  'ore.predefinedParameterUrl': 'https://help.aliyun.com/document_detail/406729.html',
  'ore.privateZoneChargeUrl': 'https://help.aliyun.com/document_detail/71338.html',
  'ore.productLegalAgreementUrl': 'https://market.aliyun.com/product/protocol.htm?code={code}',
  'ore.quotaProductsCenterUrl': 'https://quotas.console.aliyun.com/products/computenest/quotas',
  'ore.quotaWhiteList': 'https://quotas.console.aliyun.com/white-list-products/csk/quotas',
  'ore.ramOverviewUrl': 'https://ram.console.aliyun.com/overview',
  'ore.ramPermissionUrl': 'https://ram.console.aliyun.com/permissions/troubleshoot?request={diagnosticMessage}',
  'ore.ramPoliciesUrl': 'https://ram.console.aliyun.com/policies',
  'ore.ramPolicyGroupDoc': 'https://help.aliyun.com/document_detail/93743.htm',
  'ore.ramRoleDetail': 'https://ram.console.aliyun.com/roles/{roleName}',
  'ore.ramRoleDetailUrl': 'https://ram.console.aliyun.com/roles/{roleName}',
  'ore.ramRoleInitHelpUrl': 'https://help.aliyun.com/document_detail/120810.htm',
  'ore.rdsInstanceDetail': 'https://rdsnext.console.aliyun.com/detail/{instanceId}/basicInfo?region={regionId}',
  'ore.realNameAuthentication': 'https://account.console.aliyun.com/v2#/authc/types',
  'ore.rechargeUrl': 'https://usercenter2.aliyun.com/finance/fund-management/recharge',
  'ore.redisInstanceDetail': 'https://kvstore.console.aliyun.com/Redis/instance/{regionId}/{instanceId}',
  'ore.replicateMongoDBDetail': 'https://mongodb.console.aliyun.com/replicate/{regionId}/instances/{DBInstanceId}/basicInfo',
  'ore.rosTemplateAssociationPropertyDoc': 'https://help.aliyun.com/zh/ros/user-guide/associationproperty-and-associationpropertymetadata',
  'ore.rosTemplateDoc': 'https://help.aliyun.com/document_detail/28858.htm?#concept-28858-zh',
  'ore.rosTemplateVisibleDoc': 'https://help.aliyun.com/document_detail/357710.html',
  'ore.ros_host': 'https://ros.console.aliyun.com',
  'ore.ros_intl_host': 'https://ros-intl.console.aliyun.com',
  'ore.serverlessMongoDBDetail': 'https://mongodb.console.aliyun.com/serverless/{regionId}/instances/{DBInstanceId}/basicInfo',
  'ore.setRoletoECSServiceAcountDoc': 'https://help.aliyun.com/zh/ecs/developer-reference/api-exportimage',
  'ore.shardingMongoDBDetail': 'https://mongodb.console.aliyun.com/sharding/{regionId}/instances/{DBInstanceId}/basicInfo',
  'ore.shenceAlertLink': 'https://help.aliyun.com/document_detail/434178.html',
  'ore.simpleParameterMappingFlowUrl': 'https://help.aliyun.com/zh/compute-nest/configure-parameter-mappings',
  'ore.slbInstanceUrl': 'https://slb.console.aliyun.com/slb/{regionId}/slbs',
  'ore.slrDocLink': 'https://help.aliyun.com/document_detail/302355.html',
  'ore.SLRHelpURL1': 'https://help.aliyun.com/document_detail/164115.html',
  'ore.snapshotHomeUrl': 'https://ecs.console.aliyun.com/snapshot/region/{regionId}',
  'ore.stackGroupCreateUrl': 'https://ros.console.aliyun.com/cn-hangzhou/stackGroup/create',
  'ore.stackGroupCreateVpcVswitchsSgAllZoneUrl': 'https://ros.console.aliyun.com/cn-hangzhou/stackGroup/create?templateUrl=https://computenest-templates.oss-cn-hangzhou.aliyuncs.com/create-vpc-vswitchs-sg-all-zone.yml',
  'ore.stackGroupCreateVpcVswitchsSgUrl': 'https://ros.console.aliyun.com/cn-hangzhou/stackGroup/create?templateUrl=https://computenest-templates.oss-cn-hangzhou.aliyuncs.com/create-vpc-vswitchs-sg.yml',
  'ore.usercenterHomeUrl': 'https://usercenter2.aliyun.com/home',
  'ore.vpcQuotaUrl': 'https://vpc.console.aliyun.com/quota',
  'ore.vscodeJsonSchema': 'https://g.alicdn.com/aliyun-ecs/ros-vscode-schema/0.0.20/ros.vscode.jsonSchema.json',
  'ore.vscodeJsonSchemaOOS': 'https://g.alicdn.com/aliyun-ecs/ros-vscode-schema/0.0.19/oos.vscode.jsonSchema.json',
  'ore.vscodeYamlSchema': 'https://g.alicdn.com/aliyun-ecs/ros-vscode-schema/0.0.20/ros.vscode.yamlSchema.json',
  'ore.vscodeYamlSchemaOOS': 'https://g.alicdn.com/aliyun-ecs/ros-vscode-schema/0.0.19/oos.vscode.yamlSchema.json',
  'ore.yamlWorkerVersion': '0.0.5',
  'ore.zoneIdDefaultValuesMap': '{\n  "cn-shanghai": ["cn-shanghai-b", "cn-shanghai-g"],\n  "cn-nanjing": ["cn-nanjing-a"],\n  "cn-fuzhou": ["cn-fuzhou-a"],\n  "cn-heyuan": ["cn-heyuan-a", "cn-heyuan-b"],\n  "cn-huhehaote": ["cn-huhehaote-a", "cn-huhehaote-b"],\n  "cn-shenzhen": ["cn-shenzhen-e"],\n  "cn-wulanchabu": ["cn-wulanchabu-c"],\n  "cn-guangzhou": ["cn-guangzhou-b"],\n  "cn-hangzhou": ["cn-hangzhou-h", "cn-hangzhou-i", "cn-hangzhou-j", "cn-hangzhou-k", "cn-hangzhou-g"],\n  "cn-chengdu": ["cn-chengdu-b"],\n  "cn-wuhan-lr": ["cn-wuhan-lr-b"],\n  "cn-qingdao": ["cn-qingdao-b"],\n  "cn-zhangjiakou": ["cn-zhangjiakou-a", "cn-zhangjiakou-b", "cn-zhangjiakou-c"],\n  "cn-beijing": ["cn-beijing-i", "cn-beijing-j"]\n}',
  orePodUrl: 'https://g.alicdn.com/aliyun-ecs/ore-store/0.2.77/ore-store.manifest.json',
  organizationShare: 'https://resourcemanager.console.aliyun.com/resource-share/cn-shanghai/my-list',
  ossCORSHelp: 'https://help.aliyun.com/document_detail/31870.html?#section-r96-3f5-hxy',
  ram_policies_system: 'https://ram.console.aliyun.com/policies/{@Name}/System/content',
  resourceManager: 'https://resourcemanager.console.aliyun.com/resource-groups',
  resourceTypePropertiesDoc: 'https://alibabacloud.com/help/ros/developer-reference/{@resourceTypeName}',
  resourceTypePropertiesOFFICIALDoc: 'https://help.aliyun.com/zh/ros/developer-reference/{@resourceTypeName}',
  rosDesignerLink: 'https://rosnext.console.aliyun.com/designer',
  rosOrderAlipay: 'https://ros.console.aliyun.com',
  rosPackageOrderLink: 'https://finance.aliyun.com/order/list_pay.htm?order_id={@id}',
  ROSPractice0: 'https://help.aliyun.com/knowledge_detail/67256.html',
  ROSPractice1: 'https://help.aliyun.com/document_detail/169287.html',
  ROSPractice2: 'https://help.aliyun.com/document_detail/171372.html',
  ROSPractice3: 'https://help.aliyun.com/document_detail/173527.html',
  ROSPractice4: 'https://help.aliyun.com/document_detail/177202.html',
  ROSPractice_1: 'https://developer.aliyun.com/article/770524?groupCode=cloud',
  rosServiceLink: 'https://help.aliyun.com/knowledge_detail/39967.html',
  ROSTool0: 'https://help.aliyun.com/document_detail/172714.html',
  ROSTool1: 'https://help.aliyun.com/document_detail/139508.html',
  ROSTool2: 'https://help.aliyun.com/document_detail/171807.html',
  ROSTool3: 'https://help.aliyun.com/document_detail/183801.html',
  ROSTool4: 'https://help.aliyun.com/document_detail/164353.html',
  ROSTool5: 'https://github.com/aliyun/alibabacloud-ros-tool-transformer',
  ros_create_stack: 'https://help.aliyun.com/ros/user-guide/create-a-stack-1',
  ros_create_template: 'https://help.aliyun.com/ros/user-guide/create-a-template-1',
  ros_host: 'https://ros.console.aliyun.com',
  ros_intl_host: 'https://ros-intl.console.aliyun.com',
  ros_product_introduction: 'https://help.aliyun.com/ros/product-overview/product-introduction',
  ros_quick_start: 'https://help.aliyun.com/ros/quick-start/',
  ros_stack_create: 'https://ros.console.aliyun.com/${RegionId}/stacks/create',
  ros_stack_details: 'https://ros.console.aliyun.com/${RegionId}/stacks/${StackId}',
  ros_stack_list_error_center: 'https://error-center.aliyun.com/troubleshoot?q=${code}',
  ROS_Template_LAMP: 'https://ros-template.cn-hangzhou.oss.aliyun-inc.com/LAMP_Basic.json',
  ros_template_syntax: 'https://help.aliyun.com/ros/user-guide/template-syntax-1/',
  ros_videos: 'https://help.aliyun.com/ros/videos',
  scratchResourceType: 'https://help.aliyun.com/document_detail/353175.html',
  sgCreateLink: 'https://ecs.console.aliyun.com/#/securityGroup/region/{@regionId}/create',
  'stack.group.help': 'https://help.aliyun.com/document_detail/154578.html',
  stack_manually_pay: 'https://finance.aliyun.com/order/trade_pay.htm?order_id={@orderId}',
  'test.xx': 'test.xx',
  vpcCreateLink: 'https://vpc.console.aliyun.com/vpc/{@regionId}/vpcs/new',
  vswitchCreateLink: 'https://vpc.console.aliyun.com/vpc/{@regionId}/switches/new',
};
