export const enum EQuickDeployType {
  FC = 0,
  ROS = 1
}

export const DeployTypes = [{
  label: '资源栈',
  value: 1,
}, {
  label: '应用',
  value: 0,
}];

export const enum ERosDeployStatus {
  PENDING = 'PENDING',
  CREATING = 'CREATE_IN_PROGRESS',
  CREATE_FAILED = 'CREATE_FAILED',
  CREATE_COMPLETE = 'CREATE_COMPLETE',
  DELETE_IN_PROGRESS = 'DELETE_IN_PROGRESS',
  DELETE_FAILED = 'DELETE_FAILED',
  DELETE_COMPLETE = 'DELETE_COMPLETE',
  CREATE_ROLLBACK_IN_PROGRESS = 'CREATE_ROLLBACK_IN_PROGRESS',
  CREATE_ROLLBACK_FAILED = 'CREATE_ROLLBACK_FAILED',
  CREATE_ROLLBACK_COMPLETE = 'CREATE_ROLLBACK_COMPLETE',
  ROLLBACK_IN_PROGRESS = 'ROLLBACK_IN_PROGRESS',
  ROLLBACK_FAILED = 'ROLLBACK_FAILED',
  ROLLBACK_COMPLETE = 'ROLLBACK_COMPLETE',
}
export const enum ERosSuccessStatus {
  CREATE_COMPLETE = 'CREATE_COMPLETE',
  UPDATE_COMPLETE = 'UPDATE_COMPLETE',
  CHECK_COMPLETE = 'CHECK_COMPLETE',
}

export const rosCreateStatus = {
  [ERosDeployStatus.CREATING]: {
    label: '资源栈创建中，一般需要2-5分钟时间，请耐心等待…',
    icon: 'help-icon-circle-loading',
    color: '#696969',
    progressColor: '#1366ec',
    backgroundImage: 'radial-gradient(circle at 85% 72%, #457AFF 0%, #1B58F4 75%)',
  },
  [ERosDeployStatus.CREATE_FAILED]: {
    label: '资源栈创建失败',
    icon: 'help-icon-shibai',
    color: '#F54745',
    progressColor: '#F54745',
    backgroundImage: 'radial-gradient(circle at 85% 72%, #EC4344 0%, #C82727 75%)',
  },
  [ERosDeployStatus.CREATE_COMPLETE]: {
    label: '资源栈创建成功',
    icon: 'help-icon-chenggong',
    color: '#06B624',
    progressColor: '#06B624',
  },
};

export const ResourceStatusMap = {
  INIT_COMPLETE: '资源待创建',
  CREATE_COMPLETE: '资源创建完成',
  CREATE_FAILED: '资源创建失败',
  CREATE_IN_PROGRESS: '资源创建中',
  UPDATE_IN_PROGRESS: '资源更新中',
  UPDATE_FAILED: '资源更新失败',
  UPDATE_COMPLETE: '资源更新完成',
  DELETE_IN_PROGRESS: '资源删除中',
  DELETE_FAILED: '资源删除失败',
  DELETE_COMPLETE: '资源删除完成',
  CHECK_IN_PROGRESS: '资源校验中',
  CHECK_FAILED: '资源校验失败',
  CHECK_COMPLETE: '资源校验完成',
  IMPORT_IN_PROGRESS: '资源导入中',
  IMPORT_FAILED: '资源导入失败',
  IMPORT_COMPLETE: '资源导入完成',
};
