import React, { Fragment, FunctionComponent, useEffect, useRef } from 'react';
import classNames from 'classnames';
import { isBoolean, isString } from 'lodash';
import { Button } from '@alifd/next';
import { ButtonProps } from '@alifd/next/lib/button/index.d';
import { MenuHideButton } from '@/help-fe-common/components/pc/Menu/MenuHideButton';
import useEventListener from '@/help-fe-common/hooks/useEventListener';
import { getCssNumber } from '@/help-fe-common/utils/global/style/cssValue';
import styles from './index.module.scss';

interface IProps {
  visible?: boolean;
  onVisibleChange?: () => void;
  onOk?: () => void;
  okProps?: ButtonProps;
  onCancel?: () => void;
  cancelProps?: ButtonProps;
  title: string | React.ReactNode;
  footer?: boolean | React.ReactElement;
  children: React.ReactElement;
}

const BaseDeploy: FunctionComponent<IProps> = (props) => {
  const {
    title,
    visible,
    onVisibleChange,
    footer,
    onOk,
    okProps,
    onCancel,
    cancelProps,
    children,
  } = props;

  const navHeight = getCssNumber('--default-nav-height');
  const headHeight = getCssNumber('--default-head-height');
  const wrapperRef = useRef<HTMLDivElement | null>(null);

  const onClose = () => {
    onVisibleChange && onVisibleChange();
  };

  const onCancelClick = () => {
    onCancel && onCancel();
  };

  const renderDefaultFooter = () => {
    return (
      <Fragment>
        <Button type="primary" onClick={onOk} className={styles.primaryBtn} {...okProps}>确定</Button>
        <Button type="normal" onClick={onCancelClick} {...cancelProps}>取消</Button>
      </Fragment>
    );
  };

  const onScroll = () => {
    const { scrollTop } = document.documentElement;
    if (!wrapperRef.current) return;
    const { parentNode } = wrapperRef.current;
    const { bottom } = (parentNode as HTMLElement)?.getBoundingClientRect();
    if (scrollTop > navHeight) {
      wrapperRef.current.style.height = `calc(100vh - ${headHeight}px)`;
      if (bottom < window.innerHeight) {
        const reset = window.innerHeight - bottom;
        wrapperRef.current.style.position = 'fixed';
        wrapperRef.current.style.top = `${headHeight - reset}px`;
      } else {
        wrapperRef.current.style.position = 'fixed';
        wrapperRef.current.style.top = `${headHeight}px`;
      }
    } else {
      wrapperRef.current.style.height = `calc(100vh - ${headHeight}px - ${navHeight}px + ${scrollTop}px)`;
      wrapperRef.current.style.position = 'relative';
      wrapperRef.current.style.top = 'unset';
    }
  };

  useEventListener(window, 'scroll', onScroll);

  useEffect(() => {
    const evt = window.document.createEvent('UIEvents');
    evt.initUIEvent('scroll', true, false, window, 0);
    window.dispatchEvent(evt);
  }, []);

  if (!visible) return null;

  return (
    <div className={classNames(styles.baseContainer)} ref={wrapperRef}>
      <section className={styles.wrapperInner}>
        <header className={styles.header}>
          <div>{isString(title) ? <h4 className={styles.title}>{title}</h4> : title}</div>
          <i className={classNames(styles.closeBtn, 'help-iconfont help-icon-delete')} onClick={onClose} />
        </header>
        <main className={styles.mainContext}>
          {children}
        </main>
        {
          Boolean(footer) &&
          <footer className={styles.footer}>
            {isBoolean(footer) ? renderDefaultFooter() : footer}
          </footer>
        }
      </section>
      <MenuHideButton />
    </div>);
};

export default BaseDeploy;
