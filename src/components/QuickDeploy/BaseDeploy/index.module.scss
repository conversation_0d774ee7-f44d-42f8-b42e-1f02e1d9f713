.baseContainer {
  width: 50vw;
  height: calc(100vh - var(--default-head-height) - var(--default-nav-height));
  background-color: #ffffff;
  position: relative;

  .wrapperInner {
    display: flex;
    flex-direction: column;
    height: 100%;

    .header {
      height: 56px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
      border-bottom: 1px solid #D1D5D9;

      .title {
        font-weight: 600;
        font-size: 16px;
        color: #333333;
        letter-spacing: 0;
        margin: 0;
      }

      .closeBtn {
        font-size: 13px;
        color: #A6A6A6;

        &:hover {
          cursor: pointer;
          color: #333;
        }
      }
    }

    .mainContext {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      scrollbar-width: 5px;
      overscroll-behavior: none;

      &::-webkit-scrollbar {
        width: 5px;
        height: 5px;
      }

      &::-webkit-scrollbar-thumb {
        background: #d8d8d8;
        border-radius: 3px;
      }
    }

    .footer {
      padding: 20px 60px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.10);
      background-color: #fff;
      //position: fixed;
      //bottom: 0;
      width: 50vw;
      text-align: right;

      .primaryBtn {
        background-color: #1366EC;
        margin-right: 16px;
      }
    }
  }
}
