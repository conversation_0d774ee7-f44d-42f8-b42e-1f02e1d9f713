@import "../../../help-fe-common/styles/variables";

.listApplication {

  .emptyTip {
    font-weight: 500;
    font-size: 12px;
    margin-right: 16px;
  }

  .noSearchRes {
    font-size: 12px;
    color: #808080;
  }

  .deployRecord {
    display: flex;
    align-items: center;
  }

  .clickRecord {
    color: #1366ec;
    cursor: pointer;
  }

  .totalRender {
    margin-left: 8px;
  }

  .pagination {
    text-align: right;
    margin-top: 8px;
  }

  .oneLine {
    width: 100%;
    display: inline-block;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  :global {
    th>.next-table-cell-wrapper {
      color: #333333;
      font-weight: 500;
    }

    .next-table th {
      background: #EFF3F8;
      border-bottom: 1px solid #CBCBCB;
    }

    .next-table td {
      border-bottom: 1px solid #CBCBCB;
    }

    .next-table-cell-wrapper {
      font-size: 12px;
      padding: 12px !important;
    }

    .next-table-sort .current .next-icon {
      color: $theme-color;
    }

    .next-pagination.next-medium .next-pagination-item {
      border-width: 0;
      padding: 0 12px;
    }

    .next-pagination .next-pagination-item.next-current {
      background: #fff;
      color: $theme-color;
      border: 1px solid $theme-color;
      border-radius: 2px;
    }

    .next-input.next-focus {
      border-color: $theme-color;
    }

    .next-menu-item.next-selected .next-menu-icon-selected {
      color: $theme-color;
    }

    .next-menu-item:not(.next-disabled):hover .next-menu-icon-selected,
    .next-menu-item:not(.next-disabled).next-selected:hover .next-menu-icon-selected {
      color: $theme-color;
    }

    .next-loading-dot {
      background-color: $theme-color;
    }

    .next-pagination-size-selector-title {
      font-weight: 400;
      font-size: 12px !important;
      color: #333333;
    }

    .next-input-control .next-icon {
      color: #333333;
    }

    .next-input.next-medium .next-icon:before {
      font-size: 10px;
    }

    .next-pagination .next-pagination-item {
      color: #AAAAAA;
    }

    .next-btn[disabled].next-btn-normal {
      background-color: unset;

      &:hover {
        background-color: unset;
        color: #AAAAAA;
      }
    }

    .next-pagination.next-medium .next-pagination-item.next-next:not([disabled]) i {
      color: #AAAAAA;
    }

    .next-pagination.next-medium .next-pagination-item.next-prev:not([disabled]) i {
      color: #AAAAAA;
    }

    .next-btn.next-medium>.next-btn-icon.next-icon-last {
      &:before {
        font-size: 10px;
      }
    }

    .next-btn.next-medium>.next-btn-icon.next-icon-first {
      &:before {
        font-size: 10px;
      }
    }

    .next-btn-helper {
      font-size: 12px;
    }
  }
}