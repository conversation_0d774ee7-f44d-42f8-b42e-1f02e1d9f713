import React, { Fragment, FunctionComponent, useEffect, useState } from 'react';
import { Pagination, Table } from '@alifd/next';
import { isNumber, isEmpty } from 'lodash';
import { useRequest } from 'ice';
import QuickDeployService from '@/services/helplab/quickDeploy';
import { DeployTypes, EQuickDeployType } from '@/components/QuickDeploy/constant';
import { renderStatus, renderRelateDoc } from './table';
import { renderFcDeployTitle } from '../FcQuickDeploy/UserApplication/CellRender';
import { renderRosDeployTitle } from '../RosQuickDeploy/UserApplication/CellRender';
import RosHeadSearch from '../RosQuickDeploy/UserApplication/HeadSearch';
import FailCheck from '@/components/QuickDeploy/FcQuickDeploy/UserApplication/FailCheck';
import FailReason from '@/components/QuickDeploy/RosQuickDeploy/UserApplication/FailReason';
import FcRecordAction from '../FcQuickDeploy/UserApplication/RecordAction';
import RosRecordAction from '../RosQuickDeploy/UserApplication/RecordAction';
import ResourceDetail from '@/components/QuickDeploy/RosQuickDeploy/ResourceDetail';
import ActionTooltip from '../RosQuickDeploy/UserApplication/ActionTooltip';
import PrimaryBtn from '@/components/QuickDeploy/FcQuickDeploy/PrimaryBtn';
import styles from './index.module.scss';

interface IProps {
  searchValue?: string;
  deployType: EQuickDeployType;
  onClose?: () => void;
}

const UserApplication: FunctionComponent<IProps> = (props) => {
  const {
    searchValue: initSearchValue,
    deployType,
    onClose,
  } = props;

  const {
    request: getRosListApplication,
    loading,
    data: listApplicationData,
  } = useRequest(QuickDeployService.getDeployList, { manual: true });

  const [pageValues, setPageValues] = useState({
    pageSize: 10,
    pageNum: 1,
  });

  const [searchValue, setSearchValue] = useState<string | undefined>(initSearchValue);

  const [applicationType, setApplicationType] = useState<EQuickDeployType>(deployType);

  const [visible, setVisible] = useState(false);

  const [rowData, setRowData] = useState({});

  const onFresh = () => {
    onQuery();
  };

  const onQuery = (values = {}) => {
    const query = {
      applicationType,
      searchValue,
      ...pageValues,
      ...values,
    };
    getRosListApplication(query);
  };

  const onApplicationTypeChange = (type: EQuickDeployType) => {
    const page = {
      ...pageValues,
      pageNum: 1,
    };
    setApplicationType(type);
    setPageValues(page);
  };

  const onSearchChange = (value: string) => {
    if (!value) {
      const page = {
        ...pageValues,
        pageNum: 1,
      };
      setPageValues(page);
      onQuery({
        ...page,
        searchValue: value,
      });
    }
    setSearchValue(value);
  };

  const onSearch = () => {
    const page = {
      ...pageValues,
      pageNum: 1,
    };
    setPageValues(page);
    onQuery(page);
  };

  const onPageSizeChange = (size: number) => {
    const page = {
      pageNum: 1,
      pageSize: size,
    };
    setPageValues(page);
    onQuery(page);
  };

  const onResourceClick = (record: Record<string, any>) => {
    setVisible(true);
    setRowData(record);
  };

  const onPageChange = (pageNum: number) => {
    const page = {
      ...pageValues,
      pageNum,
    };
    setPageValues(page);
    onQuery(page);
  };

  useEffect(() => {
    onQuery();
  }, [applicationType]);

  const renderDeployTitle = (value: string, index: number, record: Record<string, any>) => {
    if (record?.applicationType === EQuickDeployType.FC) {
      return renderFcDeployTitle(value);
    }
    return renderRosDeployTitle(value, record);
  };

  const renderRelateDocument = (value: string, index: number, record: Record<string, any>) => {
    return renderRelateDoc(value, record);
  };

  const renderApplicationType = (val: number) => {
    if (!isNumber(val)) return '-';
    const match = DeployTypes.find((item) => item.value === val);
    return match?.label;
  };

  const renderDeployStatus = (value: string, index: number, record: Record<string, any>) => {
    const reactNode = record?.applicationType === EQuickDeployType.FC ?
      <FailCheck record={record} /> :
      <FailReason applicationId={record?.applicationId} deployTags={record?.deployTags} />;
    return renderStatus(record, reactNode);
  };

  const renderOpt = (value: string, index: number, record: Record<string, any>) => {
    if (record.applicationType === EQuickDeployType.FC) {
      return <FcRecordAction record={record} />;
    }
    return <RosRecordAction record={record} onResourceClick={onResourceClick} />;
  };

  return (
    <div className={styles.listApplication}>
      <RosHeadSearch
        onFresh={onFresh}
        applicationType={applicationType}
        onApplicationTypeChange={onApplicationTypeChange}
        searchValue={searchValue}
        onSearchChange={onSearchChange}
        onSearch={onSearch}
      />
      <Table
        dataSource={listApplicationData?.data || []}
        hasBorder={false}
        tableLayout="fixed"
        loading={loading}
        emptyContent={!searchValue && isEmpty(listApplicationData?.data) ?
          <Fragment>
            <span className={styles.emptyTip}>您当前暂无已部署的应用和资源栈，您可以在您感兴趣的文档中一键部署您的应用</span>
            <PrimaryBtn text onClick={() => onClose && onClose()}>返回文档</PrimaryBtn>
          </Fragment> : <span className={styles.noSearchRes}>暂无匹配的搜索结果</span>}
      >
        <Table.Column
          dataIndex="applicationId"
          title="部署名称"
          cell={renderDeployTitle}
        // width="28%"
        />
        <Table.Column dataIndex="deployedNodeTitle" title="相关文档" cell={renderRelateDocument} />
        <Table.Column dataIndex="applicationType" title="部署对象" width={80} cell={renderApplicationType} />
        <Table.Column dataIndex="deployStatus" title="部署状态" cell={renderDeployStatus} width={120} />
        <Table.Column title={deployType === EQuickDeployType.ROS ? <ActionTooltip /> : '操作'} cell={renderOpt} width={170} />
      </Table>
      <Pagination
        className={styles.pagination}
        total={listApplicationData?.totalCount || 0}
        onPageSizeChange={onPageSizeChange}
        onChange={onPageChange}
        current={pageValues.pageNum}
        pageSize={pageValues.pageSize}
        hideOnlyOnePage
        pageSizeSelector="dropdown"
        popupProps={{
          container: (d: any) => d.parentNode,
        }}
      />
      <ResourceDetail visible={visible} onClose={() => setVisible(false)} rowData={rowData} />
    </div>);
};

export default UserApplication;
