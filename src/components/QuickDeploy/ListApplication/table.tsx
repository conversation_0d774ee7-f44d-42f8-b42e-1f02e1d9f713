import React from 'react';
import { isNumber } from 'lodash';
import Href from '@/components/QuickDeploy/FcQuickDeploy/Href';
import { EQuickDeployType } from '../constant';
import { renderRosDeployStatus } from '../utils/renderDeploy';
import { safeJSONParse } from '@/help-fe-common/utils/global/safeJson';
import styles from './index.module.scss';
import classNames from 'classnames';

// 任务枚举类
export enum ETaskStatus {
  FAIL = 2,
  SUCCESS = 1,
  PROCESSING = 0,
  CANCEL = 3
}

// 部署文案映射
export const mapNameByStatus = {
  [ETaskStatus.FAIL]: '部署失败',
  [ETaskStatus.SUCCESS]: '部署成功',
  [ETaskStatus.PROCESSING]: '部署中',
  [ETaskStatus.CANCEL]: '已取消',
};

// 部署文案样式映射
export const mapColorByStatus = {
  [ETaskStatus.FAIL]: '#E00000',
  [ETaskStatus.SUCCESS]: '#009431',
  [ETaskStatus.PROCESSING]: '#555555',
  [ETaskStatus.CANCEL]: '#333',
};

export const mapIconByStatus = {
  [ETaskStatus.FAIL]: <i className="help-iconfont help-icon-chuangjianshibai" style={{ color: mapColorByStatus[ETaskStatus.FAIL] }} />,
  [ETaskStatus.SUCCESS]: <i className="help-iconfont help-icon-chuangjianchenggong" style={{ color: mapColorByStatus[ETaskStatus.SUCCESS] }} />,
  [ETaskStatus.PROCESSING]: <i className="help-iconfont help-icon-circle-loading" style={{ color: mapColorByStatus[ETaskStatus.PROCESSING] }} />,
  [ETaskStatus.CANCEL]: <i className="help-iconfont help-icon-zhuyi" style={{ color: mapColorByStatus[ETaskStatus.CANCEL] }} />,
};

/**
 * 渲染部署状态
 * @param tableItem 表格列数据
 * @param reactNode 需要展示的状态组件
 * @returns
 */
export const renderStatus = (tableItem: Record<string, any>, reactNode: React.ReactElement) => {
  const parser: Record<string, any> = safeJSONParse(tableItem?.deployTags || null);

  return isNumber(tableItem?.deployStatus) ?
    <section
      className={classNames(styles.deployRecord,
        tableItem?.applicationType === EQuickDeployType.ROS ? styles.clickRecord : '')}
      onClick={() => {
        if (tableItem?.applicationType === EQuickDeployType.ROS) {
          renderRosDeployStatus({
            title: tableItem?.deployedNodeTitle,
            templateId: '',
            stackId: tableItem?.applicationId,
            regionId: parser?.regionId,
          });
        }
      }}
    >
      {mapIconByStatus[tableItem.deployStatus]}
      <span
        style={{
          marginLeft: 4,
        }}
      >
        {mapNameByStatus[tableItem?.deployStatus]}
      </span>
      {tableItem?.deployStatus === ETaskStatus.FAIL && reactNode}
    </section>
    : '-';
};

export const renderRelateDoc = (value: string, tableItem: Record<string, any>) => {
  return value ?
    <Href
      href={`/document_detail/${tableItem?.deployedNodeId}.html`}
      target="_blank"
      rel="noreferrer"
      className={styles.oneLine}
    >
      {value}
    </Href> : '-';
};
