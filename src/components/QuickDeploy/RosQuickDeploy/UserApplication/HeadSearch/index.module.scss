.actionBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .label {
    margin-right: 8px;
    font-weight: 400;
    font-size: 12px;
    color: #333333;
    letter-spacing: 0.4px;
  }

  .select {
    max-width: 180px;
    width: 10vw;

    em {
      font-size: 12px;
    }

    input::placeholder {
      font-size: 12px;
    }

    :global {
      .next-icon-arrow-down {
        &:before {
          font-size: 10px;
        }
      }

      .next-select-inner {
        color: #666666;
        border-color: #D1D5D9;
        border-radius: 2px;
        box-shadow: none;
      }
    }
  }

  .rightSearch {
    display: flex;
    align-items: center;

    .refreshBtn {
      margin-left: 8px;
      border-radius: 2px;
    }

    .iconSearch {
      font-size: 10px;
    }
  }
}
