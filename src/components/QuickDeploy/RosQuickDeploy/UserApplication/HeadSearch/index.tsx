import React, { FunctionComponent } from 'react';
import { Button, Select } from '@alifd/next';
import { DeployTypes, EQuickDeployType } from '@/components/QuickDeploy/constant';
import Search from '@/components/QuickDeploy/FcQuickDeploy/UserApplication/Search';
import styles from './index.module.scss';

interface IProps {
  onFresh?: () => void;
  applicationType: EQuickDeployType;
  onApplicationTypeChange: (type: EQuickDeployType) => void;
  searchValue: string | undefined;
  onSearchChange: (value: string) => void;
  onSearch: () => void;
}

const HeadSearch: FunctionComponent<IProps> = (props) => {
  const {
    onFresh,
    applicationType,
    onApplicationTypeChange,
    searchValue,
    onSearchChange,
    onSearch,
  } = props;

  return (<div className={styles.actionBar}>
    <div>
      <label className={styles.label}>部署对象</label>
      <Select
        placeholder="选择部署对象"
        dataSource={DeployTypes}
        className={styles.select}
        value={applicationType}
        onChange={onApplicationTypeChange}
        popupContainer={(d: any) => d.parentNode}
        hasClear
      />
    </div>
    <div className={styles.rightSearch}>
      <Search
        value={searchValue}
        onChange={onSearchChange}
        onSearch={onSearch}
        hasClear
        placeholder="搜索资源栈或应用"
      />
      <Button
        type="normal"
        className={styles.refreshBtn}
        iconSize="xs"
        onClick={onFresh}
      >
        <i className={`help-iconfont help-icon-shuaxin ${styles.iconSearch}`} />
      </Button>
    </div>
  </div>);
};

export default HeadSearch;
