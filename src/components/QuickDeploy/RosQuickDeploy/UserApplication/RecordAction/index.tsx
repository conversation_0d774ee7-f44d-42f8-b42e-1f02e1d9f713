import React, { FunctionComponent, useMemo } from 'react';
import { isEmpty } from 'lodash'
import PrimaryBtn from '@/components/QuickDeploy/FcQuickDeploy/PrimaryBtn';
import { safeJSONParse } from '@/help-fe-common/utils/global/safeJson';
import { getStackOutputUrl } from '@/components/QuickDeploy/utils';
import styles from './index.module.scss';

interface IProps {
  record: Record<string, any>;
  onResourceClick: (record: Record<string, any>) => void;
}

const RecordAction: FunctionComponent<IProps> = (props) => {
  const { record, onResourceClick } = props;

  const { deployTags } = record;

  const stackUrl = useMemo(() => {
    if (!deployTags) return null;
    const parser: Record<string, any> = safeJSONParse(deployTags);
    if (isEmpty(parser?.outputs)) return null;
    return getStackOutputUrl(parser?.outputs);
  }, [deployTags]);

  return (<div>
    <PrimaryBtn
      text
      onClick={() => onResourceClick(record)}
    >
      资源详情
    </PrimaryBtn>
    <span className={styles.btnGap} />
    <PrimaryBtn
      disabled={!stackUrl}
      text
      onClick={() => stackUrl && window.open(stackUrl)}
    >
      查看效果地址
    </PrimaryBtn>
  </div>);
};

export default RecordAction;
