import React, { FunctionComponent } from 'react';
import { useRequest } from 'ice';
import QuickDeployService from '@/services/helplab/quickDeploy';
import PrimaryBtn from '@/components/QuickDeploy/FcQuickDeploy/PrimaryBtn';
import Confirm from '@/components/Common/Confirm';

interface IProps {
  record: Record<string, any>;
  onQuery?: Function;
}

const DelDeploy: FunctionComponent<IProps> = (props) => {
  const {
    record,
    onQuery,
  } = props;

  const {
    request: deleteDeploy,
    loading: delLoading,
  } = useRequest(QuickDeployService.deleteDeploy, { manual: true });

  const onDel = (values: Record<string, any>) => {
    const {
      applicationId,
      applicationType,
    } = values;
    if (!applicationId) return;
    deleteDeploy({
      applicationId,
      applicationType,
    })
      .then((res) => {
        if (res?.success) {
          onQuery && onQuery();
        }
      });
  };

  return (<Confirm
    trigger={<PrimaryBtn text>删除</PrimaryBtn>}
    tip="确定要删除当前资源栈"
    onOk={() => onDel(record)}
    okProps={{ loading: delLoading }}
  />);
};

export default DelDeploy;
