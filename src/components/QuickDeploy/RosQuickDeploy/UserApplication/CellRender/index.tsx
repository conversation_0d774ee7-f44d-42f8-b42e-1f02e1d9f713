import React from 'react';
import Href from '@/components/QuickDeploy/FcQuickDeploy/Href';
import { safeJSONParse } from '@/help-fe-common/utils/global/safeJson';
import { getRosConsoleUrl } from '@/components/QuickDeploy/utils';
import styles from './index.module.scss';

export function renderRosDeployTitle(value: string, record: Record<string, any>) {
  const parser: Record<string, any> = safeJSONParse(record?.deployTags || null);

  return (
    <Href
      href={getRosConsoleUrl(parser?.regionId, value)}
      target="_blank"
      rel="noreferrer"
      className={styles.oneLine}
    >
      {record?.applicationName}
    </Href>
  );
}
