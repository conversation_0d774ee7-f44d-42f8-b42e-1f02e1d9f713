import React, { FunctionComponent } from 'react';
import { Balloon } from '@alifd/next';
import PrimaryBtn from '@/components/QuickDeploy/FcQuickDeploy/PrimaryBtn';
import { rosConsoleHost } from '@/components/QuickDeploy/utils/consoleEnv';
import styles from './index.module.scss';

const ActionTooltip: FunctionComponent = () => {
  return (<div className={styles.actionTooltip}>
    <span className={styles.cellTitle}>操作</span>
    <Balloon
      trigger={<i className="help-iconfont help-icon-help" />}
      v2
      title="提示"
      align="tr"
      needAdjust
      offset={[16, 18]}
      popupProps={{
        wrapperClassName: styles.actionTooltip,
      }}
    >
      <p className={styles.tip}>当前操作仅展示资源栈详情和查看效果地址，若需删除还请前往控制台进行删除。</p>
      <PrimaryBtn className={styles.primaryBtn} onClick={() => window.open(rosConsoleHost)}>立即前往</PrimaryBtn>
    </Balloon>
  </div>);
};

export default ActionTooltip;
