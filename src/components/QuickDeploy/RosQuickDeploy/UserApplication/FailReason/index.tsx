import React, { FunctionComponent, useEffect, useState } from 'react';
import { Balloon, Icon } from '@alifd/next';
import { useRequest } from 'ice';
import RosConsoleService from '@/services/helplab/rosConsole';
import { safeJSONParse } from '@/help-fe-common/utils/global/safeJson';
import styles from './index.module.scss';

interface IProps {
  applicationId: string;
  deployTags?: string;
}

const FailReason: FunctionComponent<IProps> = (props) => {
  const {
    applicationId,
    deployTags,
  } = props;

  const [visible, setVisible] = useState<boolean>(false);

  const {
    request: getRosStack,
    data: stackData,
    loading,
  } = useRequest(RosConsoleService.getRosStack, { manual: true });

  const onVisibleChange = (vis: boolean) => {
    setVisible(vis);
  };

  useEffect(() => {
    if (!visible || !deployTags || !applicationId) return;
    const parser: Record<string, any> = safeJSONParse(deployTags);
    if (!parser?.regionId) return;
    getRosStack({
      StackId: applicationId,
      RegionId: parser?.regionId,
    });
  }, [visible, applicationId, deployTags]);

  if (!deployTags || !applicationId) return null;

  return (
    <Balloon
      trigger={<Icon type="help" size="xs" className={styles.triggerIcon} />}
      v2
      visible={visible}
      onVisibleChange={onVisibleChange}
      closable={false}
      offset={[0, -18]}
    >
      {loading ? 'loading...' : stackData?.StatusReason}
    </Balloon>);
};

export default FailReason;
