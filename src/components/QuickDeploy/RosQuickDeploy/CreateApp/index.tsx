import React from 'react';
import { isServer } from '@/help-fe-common/utils/node/getContext';
import { useAlfaApp } from '@alicloud/alfa-react';

export const OreAlfaApp = (props: any) => {
  const CreateApp = useAlfaApp({
    name: '@ali/alfa-cloud-ros-app-ore-form',
    env: 'prod',
    locale: 'zh-CN',
    noCache: true,
    sandbox: {
      disableFakeBody: true,
      allowResources: [
        'https://g.alicdn.com/aliyun-ecs/monaco-editor/0.33.0/vs/loader.js',
      ],
      externalsVars: [
        'goldlog',
        'addEventListener',
        'getUA',
        'require',
      ],
      sandBoxUrl: 'about:blank',
    },
  });

  if (isServer()) {
    return null;
  }

  return <CreateApp {...props} />;
};
