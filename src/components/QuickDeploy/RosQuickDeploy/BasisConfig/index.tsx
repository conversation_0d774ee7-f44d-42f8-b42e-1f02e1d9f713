import React, { FunctionComponent, useMemo, useImperativeHandle } from 'react';
import { Field, Form, Input, Select } from '@alifd/next';
import { useRequest } from 'ice';
import RosConsoleService from '@/services/helplab/rosConsole';
import { getInitStackName } from '@/components/QuickDeploy/utils';
import styles from './index.module.scss';

const { Item } = Form;

interface IProps {
  defaultRegion?: string;
  onRef: any;
  onChangeRegion: (value: string) => void;
}

const BasisConfig: FunctionComponent<IProps> = ({ defaultRegion, onRef, onChangeRegion }) => {
  const field = Field.useField();

  const { init, getValues, validate } = field;

  const { data: rosRes } = useRequest(RosConsoleService.getRosListRegion, { manual: false });

  const formatRegions = useMemo(() => {
    const regions = rosRes?.data?.Regions;
    if (!Array.isArray(regions)) return [];
    return regions.map((item) => ({
      label: item.LocalName,
      value: item.RegionId,
    }));
  }, [rosRes?.data?.Regions]);

  useImperativeHandle(onRef, () => {
    return {
      getValues,
      validate,
    };
  });

  return (
    <Form field={field} className={styles.form}>
      <Item
        label="资源栈名称"
        required
        help="名称可包含数字、字母（大小写敏感）、连字符、下划线。必须以字母开头，且长度必须小于255个字符"
      >
        <Input
          hasClear
          {...init('stackName', {
            initValue: getInitStackName(),
            rules: [{
              required: true,
              message: '请填写资源栈名称',
            }],
          })}
        />
      </Item>
      <Item label="地域" required>
        <Select
          hasClear
          dataSource={formatRegions}
          style={{ width: '100%' }}
          {...init('regionId', {
            initValue: defaultRegion || 'cn-beijing',
            rules: [{
              required: true,
              message: '请选择地域！',
            }],
            props: {
              onChange: onChangeRegion,
            },
          })}
        />
      </Item>
    </Form>
  );
};

export default BasisConfig;
