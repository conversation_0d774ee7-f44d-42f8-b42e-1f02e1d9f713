import React, { useRef, useCallback, FunctionComponent, useState, useEffect } from 'react';
import { Box } from '@alifd/next';
import { useRequest } from 'ice';
import BaseDeploy from '@/components/QuickDeploy/BaseDeploy';
import PrimaryBtn from '../FcQuickDeploy/PrimaryBtn';
import StepContext from '../FcQuickDeploy/StepContext';
import BasisConfig from './BasisConfig';
import Footer from './Footer';
import SuccessView from './SuccessView';
import QuickDialog from '@/components/QuickDeploy/FcQuickDeploy/QuickDialog';
import RosConsoleService from '@/services/helplab/rosConsole';
import QuickDeployService from '@/services/helplab/quickDeploy';
import { OreAlfaApp } from '@/components/QuickDeploy/RosQuickDeploy/CreateApp';
import { onUnmounted, renderQuickDeployForm, renderUserApplication } from '@/components/QuickDeploy/utils/renderDeploy';
import { transRosParams } from '@/components/QuickDeploy/utils';
import { EQuickDeployType } from '@/components/QuickDeploy/constant';
import { isServer } from '@/help-fe-common/utils/node/getContext';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';

interface IProps {
  visible: boolean;
  onClose: () => void;
  deployId: string;
  deployType: EQuickDeployType;
  templateId: string;
  deployedNodeTitle: string;
  deployedNodeId: string;
  defaultRegionId?: string;
  successViewProps?: {
    regionId: string;
    stackId: string;
  };
}

const RosQuickDeploy: FunctionComponent<IProps> = (props) => {
  const {
    onClose,
    deployedNodeTitle,
    visible,
    deployId,
    deployType,
    templateId,
    deployedNodeId,
    defaultRegionId,
    successViewProps,
  } = props;

  const ref = useRef<any>();
  const baseRef = useRef<any>();
  const [regionId, setRegionId] = useState('cn-beijing');
  const [parameter, setParameter] = useState();
  const [formState, setFormState] = useState(0);// 表单状态：初始未提交0、提交中1、提交成功2
  const [rosStackInfo, setRosStackInfo] = useState<any>({});
  const { data: rosCreateInfo, loading, request: createRosStack } = useRequest(RosConsoleService.createRosStack);

  const formRef = useCallback((form) => {
    ref.current = form;
  }, [regionId]);

  // onValuesChange 是传给ROSForm的参数，用于接收参数的变化
  const onValuesChange = useCallback((v) => {
    const { StackName: _name, ...restValues } = v || {};
    setParameter(restValues);
  }, []);

  const onOK = useCallback(() => {
    const form = ref.current;
    const baseForm = baseRef.current;
    let baseValues = {} as any;

    // 触发form的validate
    baseForm.validate((error, value) => {
      if (error) return;
      const values = baseForm?.getValues();
      baseValues = values;
    });

    form.validate().then(async () => {
      const values = form.getState()?.getValues();
      const { stackName } = baseValues;
      const formValues = {
        StackName: stackName,
        RegionId: regionId,
        TemplateURL: templateId,
        ...transRosParams(values),
        'NotificationURLs.1': 'help',
      };
      // 切换地域错误时提示
      if (values?.ZoneId && !values?.ZoneId.includes(regionId)) {
        QuickDialog({
          type: 'notice',
          title: '地域切换失败',
          content: '您可以点击按钮重试',
          okProps: {
            children: '重试',
          },
          cancelProps: {
            children: '取消',
          },
          onOk: () => {
            renderQuickDeployForm({ defaultRegionId: regionId, ...props });
          },
          onCancel: () => {
            onUnmounted();
          },
        });
        sendSlsLog({
          page: 'documentDetail',
          section: 'quickDeploy',
          action: 'regionError',
          userParams1: JSON.stringify(formValues),
          userParams2: templateId,
        });
      } else {
        // 参数校验通过创建ros资源栈
        createRosStack(formValues);

        setRosStackInfo({ ...rosStackInfo, ...baseValues, regionId, templateId });
        // setFormState(1);
      }
    }).catch((e) => {
      if (Array.isArray(e)) {
        const selector = `__attr__${e[0]?.path}`;
        const element = document.querySelector(`.${selector}`);
        if (element) {
          element.scrollIntoView();
        }
      }
    });
  }, [regionId]);

  useEffect(() => {
    if (!rosCreateInfo) return;
    if (rosCreateInfo?.code === '200') {
      const StackId = rosCreateInfo?.data?.StackId;
      const StackName = baseRef.current?.getValues()?.stackName;
      QuickDeployService.saveDeployRecord({
        applicationId: StackId,
        applicationName: StackName,
        applicationType: deployType,
        deployId,
        deployedNodeId,
        deployedNodeTitle,
        regionId,
      });
      setFormState(2);
      setRosStackInfo({ ...rosStackInfo, stackId: StackId });
    } else {
      QuickDialog({
        type: 'notice',
        title: '创建失败',
        content: rosCreateInfo?.message,
      });
      setFormState(0);
    }
  }, [rosCreateInfo]);

  useEffect(() => {
    defaultRegionId && setRegionId(defaultRegionId);
  }, [defaultRegionId]);

  useEffect(() => {
    if (successViewProps) {
      setRosStackInfo(successViewProps);
      setFormState(2);
    }
  }, [successViewProps]);

  const renderHeader = () => {
    return (
      <Box align="center" direction="row" spacing={8}>
        <h3>一键部署</h3>
        <PrimaryBtn
          text
          onClick={(e: MouseEvent) => {
            renderUserApplication({ searchValue: deployedNodeTitle, deployType: EQuickDeployType.ROS });
          }}
        >
          查看已部署的资源栈或应用
        </PrimaryBtn>
      </Box>
    );
  };

  if (isServer()) return null;

  return (
    <BaseDeploy
      title={renderHeader()}
      onVisibleChange={onClose}
      visible={visible}
      onOk={onOK}
      footer={
        formState !== 2 ?
          <Footer
            onSubmit={onOK}
            submitLoading={loading}
            showAction={formState !== 2}
            oreProps={{
              componentKey: 'CostEstimate',
              regionId,
              templateURL: templateId,
              parameters: parameter,
              form: formRef,
              useFecs: true,
            }}
          /> : false
      }
    >
      {
        formState === 2 ?
          <SuccessView rosStackInfo={rosStackInfo} onReset={() => setFormState(0)} /> :
          <StepContext stepList={[{
            title: '基础配置',
            content: <BasisConfig defaultRegion={defaultRegionId} onRef={baseRef} onChangeRegion={setRegionId} />,
          }, {
            title: '资源配置',
            content: <OreAlfaApp
              componentKey="ROSForm"
              formRef={formRef}
              showStackName={false}
              showFormButtonGroup={false}
              useUniqueKey
              templateURL={templateId} // 模板内容或模板url
              regionId={regionId}
              onValuesChange={onValuesChange}
              useFecs
            />,
          }]}
          />}
    </BaseDeploy>);
};

export default RosQuickDeploy;
