@import "../../../../help-fe-common/styles/variables";

.wrapper {

  .item {
    line-height: 20px;
    font-size: 12px;
    margin-bottom: 10px;

    .label {
      color: #373D41;
      font-weight: 600;
    }

    .value {
      color: #333333;
    }
  }

  .subTitle {
    font-weight: 600;
    font-size: 12px;
    color: #373D41;
  }

  .href.link {
    font-size: 12px;
    color: $theme-color;
    text-decoration: none;

    &:link {
      color: $theme-color;
    }
  }

  .resourceStatus {
    display: flex;
    align-items: center;

    >:first-child {
      margin-right: 4px;
    }
  }

  :global {
    .next-dialog-header {
      border-bottom: none;
      font-size: 18px;
      color: #111111;
    }

    .next-dialog-body {
      padding: 0 20px !important;
    }

    .next-dialog-close .next-dialog-close-icon.next-icon:before {
      font-size: 14px;
      width: 14px;
      height: 14px;
    }

    .next-dialog-footer {
      .next-dialog-btn {
        padding: 0 25px;
      }
    }

    th > .next-table-cell-wrapper {
      color: #333333;
      font-weight: 500;
    }

    .next-table th {
      background: #EFF3F8;
      border-bottom: 1px solid #CBCBCB;
    }

    .next-table td {
      border-bottom: 1px solid #CBCBCB;
    }

    .next-table-cell-wrapper {
      font-size: 12px;
    }

    .next-table-sort .current .next-icon {
      color: $theme-color;
    }

    .next-pagination.next-medium .next-pagination-item {
      border-width: 0;
      padding: 0 12px;
    }

    .next-pagination .next-pagination-item.next-current {
      background: #fff;
      color: $theme-color;
      border: 1px solid $theme-color;
      border-radius: 2px;
    }

    .next-input.next-focus {
      border-color: $theme-color;
    }

    .next-menu-item.next-selected .next-menu-icon-selected {
      color: $theme-color;
    }

    .next-loading-dot {
      background-color: $theme-color;
    }
    .next-table-header-fixer {
      background: #EFF3F8;
      border-bottom: 1px solid #CBCBCB;
    }
    .next-table-body {
      &::-webkit-scrollbar {
        width: 5px;
        height: 5px;
      }
      &::-webkit-scrollbar-thumb {
        background: #CBCBCB;
        border-radius: 4px;
      }
    }
  }
}
