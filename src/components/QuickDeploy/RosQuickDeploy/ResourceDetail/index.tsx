import React, { FunctionComponent, useEffect, useMemo, useState } from 'react';
import { Dialog, Grid, Table } from '@alifd/next';
import { useRequest } from 'ice';
import { isEmpty } from 'lodash';
import dayjs from 'dayjs';
import Href from '@/components/QuickDeploy/FcQuickDeploy/Href';
import { safeJSONParse } from '@/help-fe-common/utils/global/safeJson';
import RosConsoleService from '@/services/helplab/rosConsole';
import { renderRelateDoc, mapIconByStatus, mapColorByStatus, ETaskStatus } from '../../ListApplication/table';
import { getRosConsoleUrl, getStackOutputUrl, getStackResourceUrl } from '@/components/QuickDeploy/utils';
import { ResourceStatusMap } from '@/components/QuickDeploy/constant';
import styles from './index.module.scss';

const {
  Row,
  Col,
} = Grid;

interface IProps {
  visible: boolean;
  onClose: () => void;
  rowData: Record<string, any>;
}

const ReSourceDetail: FunctionComponent<IProps> = (props) => {
  const [regionId, setRegionId] = useState('');
  const {
    visible,
    onClose,
    rowData,
  } = props;

  const {
    applicationId,
    applicationName,
    deployTags,
    gmtCreate,
    deployedNodeTitle,
  } = rowData;

  const {
    request: listStackResources,
    loading,
  } = useRequest(RosConsoleService.listStackResources, { manual: true });

  const [resources, setResources] = useState([]);

  useEffect(() => {
    if (!visible || !applicationId || !deployTags) {
      setResources([]);
      return;
    }
    const parser: Record<string, any> = safeJSONParse(deployTags);
    if (!parser?.regionId) return;
    setRegionId(parser?.regionId);
    listStackResources({
      StackId: applicationId,
      RegionId: parser?.regionId,
    }).then((res) => {
      setResources(res?.data?.Resources || []);
    });
  }, [visible, applicationId, deployTags]);

  const stackUrl = useMemo(() => {
    if (!deployTags) return null;
    const parser: Record<string, any> = safeJSONParse(deployTags);
    if (isEmpty(parser?.outputs)) return null;
    return getStackOutputUrl(parser?.outputs);
  }, [deployTags]);

  const renderResourceId = (value: string, index, obj) => {
    const url = getStackResourceUrl({ regionId, ...obj });
    return url ? <Href href={url}>{value}</Href> : value || '-';
  };

  const renderOpt = () => {
    const url = getRosConsoleUrl(regionId, applicationId);
    return url ? <Href href={url}>去控制台查看详情</Href> : '-';
  };

  const renderResourceStatus = (status: string) => {
    let matchStatus: number;
    if (!status) return '-';
    if (status.includes('FAILED')) {
      matchStatus = ETaskStatus.FAIL;
    } else if (status.includes('COMPLETE')) {
      matchStatus = ETaskStatus.SUCCESS;
    } else if (status.includes('PROGRESS')) {
      matchStatus = ETaskStatus.PROCESSING;
    } else {
      matchStatus = ETaskStatus.CANCEL;
    }
    return (
      <div className={styles.resourceStatus}>
        <span style={{ color: mapColorByStatus[matchStatus] }}>{mapIconByStatus[matchStatus]}</span>
        <span>{ResourceStatusMap[status]}</span>
      </div>);
  };

  return (
    <Dialog
      title={`${applicationName || '-'}详情`}
      width={680}
      visible={visible}
      onClose={onClose}
      onCancel={onClose}
      footerActions={['cancel']}
      className={styles.wrapper}
      v2
    >
      <Row wrap gutter={24}>
        <Col span={12} className={styles.item}>
          <label className={styles.label}>资源栈名称：</label>
          <span className={styles.value}>{applicationName || '-'}</span>
        </Col>
        <Col span={12} className={styles.item}>
          <label className={styles.label}>资源栈Id：</label>
          <span className={styles.value}>{applicationId || '-'}</span>
        </Col>
        <Col span={12} className={styles.item}>
          <label className={styles.label}>创建时间：</label>
          <span className={styles.value}>{
            gmtCreate ? dayjs(gmtCreate)
              .format('YYYY.MM.DD HH:mm:ss') : '-'}
          </span>
        </Col>
        <Col span={12} className={styles.item}>
          <label className={styles.label}>效果地址：</label>
          {
            stackUrl ? <Href href={stackUrl}>{stackUrl}</Href> : <span className={styles.value}>{stackUrl || '-'}</span>
          }
        </Col>
        <Col span={12} className={styles.item}>
          <label className={styles.label}>相关文档：</label>
          {renderRelateDoc(deployedNodeTitle, rowData)}
        </Col>
      </Row>
      <h5 className={styles.subTitle}>资源清单</h5>
      <Table
        hasBorder={false}
        dataSource={resources}
        loading={loading}
        fixedHeader
        maxBodyHeight={340}
      // useVirtual
      >
        <Table.Column title="资源ID" dataIndex="PhysicalResourceId" cell={renderResourceId} />
        <Table.Column title="资源类型" dataIndex="ResourceType" />
        <Table.Column title="资源状态" dataIndex="Status" cell={renderResourceStatus} />
        <Table.Column title="操作" cell={renderOpt} />
      </Table>
    </Dialog>);
};

export default ReSourceDetail;
