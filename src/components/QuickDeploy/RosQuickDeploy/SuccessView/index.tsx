import React, { FunctionComponent, useEffect, useMemo, useState } from 'react';
import { useRequest } from 'ice';
import { Balloon, Box, Button, Loading, Tab } from '@alifd/next';
import RosConsoleService from '@/services/helplab/rosConsole';
import { renderUserApplication } from '@/components/QuickDeploy/utils/renderDeploy';
import { EQuickDeployType, ERosDeployStatus, ERosSuccessStatus, rosCreateStatus } from '@/components/QuickDeploy/constant';
import tabStyles from '@/solution/components/Common/Tab/tab.module.scss';
import SpecificResources from './SpecificResources';
import Output from './Output';
import { getStackOutputUrl, getRosConsoleUrl } from '@/components/QuickDeploy/utils';
import Href from '@/components/QuickDeploy/FcQuickDeploy/Href';
import styles from './index.module.scss';
import './icon.scss';


interface IProps {
  rosStackInfo: Record<string, any>;
  onReset: () => void;
}

const sleep = (time: number) => {
  return new Promise((resolve) => {
    setTimeout(resolve, time);
  });
};

const SuccessView: FunctionComponent<IProps> = ({ rosStackInfo, onReset }) => {
  const [activeKey, setActiveKey] = useState('resource');
  const { data: rosApiInfo, request: getRosStack } = useRequest(RosConsoleService.getRosStack);
  const { data: rosResourceInfo, request: getRosStackResource } = useRequest(RosConsoleService.listStackResources);

  const isFailed = ![
    ERosSuccessStatus.CHECK_COMPLETE,
    ERosSuccessStatus.CREATE_COMPLETE,
    ERosSuccessStatus.UPDATE_COMPLETE,
  ]?.some((item) => rosApiInfo?.Status?.includes(item));

  const createStatusInfo = useMemo(() => {
    if (!rosApiInfo?.Status) {
      return null;
    } else if (rosCreateStatus?.[rosApiInfo?.Status]) {
      return rosCreateStatus[rosApiInfo?.Status];
    } else if (isFailed) {
      return rosCreateStatus[ERosDeployStatus.CREATE_FAILED];
    } else {
      return rosCreateStatus?.[ERosDeployStatus.CREATING];
    }
  }, [rosApiInfo]);

  const getRosStackData = async () => {
    if (!rosStackInfo) {
      return;
    }

    const { stackId, regionId } = rosStackInfo;

    const rst = await getRosStack({ StackId: stackId, RegionId: regionId });
    getRosStackResource({ StackId: stackId, RegionId: regionId });
    if (rst?.Status?.includes('IN_PROGRESS')) {
      await sleep(3000);
      await getRosStackData();
    }
  };

  useEffect(() => {
    getRosStackData();
  }, [rosStackInfo]);


  const outputUrl = getStackOutputUrl(rosApiInfo?.Outputs || []);


  return (
    <div className={styles.viewContext} >
      {
        createStatusInfo ?
          <>
            <div className={styles.top}>
              <Box align="center" direction="row" spacing={8} style={{ marginBottom: 18 }}>
                <i
                  className={`help-iconfont ${createStatusInfo?.icon}`}
                  style={{ color: `${createStatusInfo?.color}`, fontSize: 24 }}
                />
                <h2 className={styles.title}>{createStatusInfo?.label}</h2>
              </Box>
              <div className={styles.content}>
                <div className={styles.name}>
                  <div className={styles.nameLeft}>
                    资源栈名称：
                  </div >
                  <Href
                    href={getRosConsoleUrl(rosApiInfo?.RegionId, rosApiInfo?.StackId)}
                    target="_blank"
                    rel="noreferrer"
                  >
                    {rosApiInfo?.StackName}
                  </Href>
                </div>
                <div className={styles.info}>
                  <div className={styles.infoContent}>
                    <div className={styles.progress}>
                      <span
                        className={styles.progressBar}
                        style={{
                          width: `${rosApiInfo?.ResourceProgress?.StackOperationProgress}%`,
                          backgroundColor: `${createStatusInfo?.color}`,
                          backgroundImage: createStatusInfo?.backgroundImage,
                        }}
                      />
                    </div>
                    <span className={styles.number}>{Math.floor(rosApiInfo?.ResourceProgress?.StackOperationProgress || 0)}%</span>
                    {isFailed && (
                      <Balloon
                        v2
                        trigger={(
                          <i className={`help-iconfont help-icon-chuangjianshibai ${styles.failIcon}`} />
                        )}
                        closable={false}
                        triggerType="hover"
                      >
                        <p className={styles.reason} dangerouslySetInnerHTML={{ __html: rosApiInfo?.StatusReason }} />
                      </Balloon>
                    )}
                  </div>
                  {isFailed && (
                    <Button
                      className={styles.detailBtn}
                      type="primary"
                      onClick={onReset}
                      style={{ marginTop: 14 }}
                    >
                      重新部署
                    </Button>
                  )}
                  {rosApiInfo?.Status === ERosDeployStatus.CREATE_COMPLETE && (
                    <div className={styles.infoContent}>
                      <Button
                        className={styles.detailBtn}
                        type="primary"
                        onClick={() => {
                          renderUserApplication({ searchValue: rosStackInfo?.stackName, deployType: EQuickDeployType.ROS });
                        }}
                      >
                        查看资源栈详情
                      </Button>
                      {outputUrl && (
                        <Button
                          component="a"
                          target="_blank"
                          href={outputUrl}
                          className={styles.viewBtn}
                        >
                          查看效果地址
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className={styles.tabContent}>
              <div className={styles.tabContainer}>
                <Tab
                  className={`${tabStyles.tab} ${styles.tab}`}
                  activeKey={activeKey}
                  onChange={setActiveKey}
                >
                  <Tab.Item title="具体资源" key="resource">
                    <SpecificResources rosResourceInfo={rosResourceInfo?.data} />
                  </Tab.Item>
                  <Tab.Item title="查看输出" key="console">
                    <Output rosApiInfo={rosApiInfo} />
                  </Tab.Item>
                </Tab>
              </div>
            </div>
          </> :
          <Loading className={styles.loading} />
      }

    </div>
  );
};

export default SuccessView;
