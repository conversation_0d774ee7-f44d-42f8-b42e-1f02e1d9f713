$primary-color: #1366ec;

@mixin primaryStyle {

  &:active {
    color: $primary-color;
  }

  &:visited {
    color: $primary-color;
  }

  &:hover {
    color: $primary-color;
  }
}

.primaryBtn {
  background: $primary-color !important;
  color: #fff;

  @include primaryStyle;
}

.viewContext {
  padding: 20px 0 0;

  .loading {
    width: 100%;
    height: 50vh;

    :global {
      .next-loading-dot {
        background: #1366ec;
      }
    }
  }

  .top {
    height: 188px;
  }

  .content {
    padding-left: 32px;
  }

  .title {
    font-weight: 500;
    font-size: 22px;
    color: #333333;
    line-height: 24px;
  }

  .name {
    font-weight: 500;
    font-size: 12px;
    color: #474A52;
    line-height: 20px;
    margin-bottom: 16px;
    .nameLeft{
      float: left;
    }
  }

  .infoContent {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }

  .info {
    .detailBtn:global(.next-btn) {
      height: 32px;
      padding: 0 16px;
      background: #1366EC;
      font-weight: 400;
      font-size: 12px;
      color: #fff;

      &:not(:disabled) {

        &:hover,
        &:focus,
        &:active {
          background: #0F52BD;
        }
      }
    }

    .viewBtn:global(.next-btn) {
      height: 32px;
      padding: 0 16px;
      background: #fff;
      font-weight: 400;
      font-size: 12px;
      color: #1366ec;
      border: 1px solid #1366EC;
      margin-left: 12px;

      &:not(:disabled) {

        &:hover,
        &:focus,
        &:active {
          background: #1366ec;
          color: #fff;
        }
      }
    }

    .progress {
      height: 8px;
      width: 245px;
      margin-right: 8px;
      background-color: #E6E8EB;
      border-radius: 64px;

      .progressBar {
        display: inline-block;
        height: 8px;
        border-radius: 64px;
        position: relative;
        top: -6px;
      }
    }

    .number {
      font-size: 14px;
      color: #474A52;
      letter-spacing: 0.25px;
      line-height: 20px;
      margin-right: 8px;
    }
  }
}

.tabContent {
  padding-left: 32px;
}

.tabContainer {
  padding-top: 12px;
  border-top: 1px solid #E9E9E9;
}

.tab:global(.next-tabs-pure) {
  :global {
    .next-tabs-bar {
      padding-left: 0;
      padding-right: 0;
      margin-left: 0;
      margin-right: 0;
    }

    .next-tabs-bar .next-tabs-nav-container .next-tabs-tab {
      min-width: auto;
      padding: 0 16px;

      &:not(.active) {
        .next-tabs-tab-inner {
          font-weight: 400;
        }
      }

      .next-tabs-tab-inner {
        padding: 9px 0;
        line-height: 24px;
      }
    }
  }
}

.failIcon {
  color: #C82727;
  cursor: pointer;
}

.reason {
  font-weight: 400;
  font-size: 12px;
  color: #808080;
  line-height: 18px;
}
