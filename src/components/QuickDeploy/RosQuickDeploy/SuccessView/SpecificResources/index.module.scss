.resourceList {
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.resource {
  padding: 10px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;

  &.resourceLoading {
    background-color: rgba(241,242,243,0.6);
  }

  &.resourceSuccess {
    background-color: rgba(6,182,36,0.06);
  }

  &.resourceFailed {
    background-color: rgba(217,48,38,0.08);
  }
}

.resourceLeft {
  div {
    font-weight: 500;
    font-size: 12px;
    color: #333333;
    line-height: 22px;
  }

  p {
    font-weight: 400;
    font-size: 12px;
    color: #81848F;
    line-height: 22px;
    margin-top: 2px;
    margin-bottom: 0;
  }
}

.resourceRight {
  font-weight: 400;
  font-size: 12px;
  flex: none;

  > div {
    display: flex;
    align-items: center;
    gap: 4px;
  }
}

.resourceLoadingIcon {
  color: #FF8A00;

  i {
    color: #696969;
  }
}

.resourceSuccessIcon {
  color: #06B624;
}

.resourceFailedIcon {
  color: #C82727;
  cursor: pointer;

  i {
    color: #D93026;;
  }
}

.reason {
  font-weight: 400;
  font-size: 12px;
  color: #808080;
  line-height: 18px;
}
