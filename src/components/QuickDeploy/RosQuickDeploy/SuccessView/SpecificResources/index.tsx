import React from 'react';
import { Balloon } from '@alifd/next';
import styles from './index.module.scss';
import _ from 'lodash';
import { ERosDeployStatus, ResourceStatusMap } from '@/components/QuickDeploy/constant';
import { ETaskStatus } from '@/components/QuickDeploy/ListApplication/table';

const ResourceStatus = ({ resource }) => {
  let matchStatus;
  if (resource?.Status?.includes('FAILED')) {
    matchStatus = ETaskStatus.FAIL;
  } else if (resource?.Status?.includes('COMPLETE')) {
    matchStatus = ETaskStatus.SUCCESS;
  } else if (resource?.Status?.includes('PROGRESS')) {
    matchStatus = ETaskStatus.PROCESSING;
  } else {
    matchStatus = ETaskStatus.CANCEL;
  }

  const resourceStatusMap = {
    [ETaskStatus.PROCESSING]: {
      className: styles.resourceLoadingIcon,
      icon: 'help-icon-circle-loading',
      balloon: null,
    },
    [ETaskStatus.SUCCESS]: {
      className: styles.resourceSuccessIcon,
      icon: 'help-icon-chuangjianchenggong',
      balloon: null,
    },
    [ETaskStatus.FAIL]: {
      className: styles.resourceFailedIcon,
      icon: 'help-icon-chuangjianshibai',
      balloon: <p className={styles.reason} dangerouslySetInnerHTML={{ __html: resource?.StatusReason }} />,
    },
    [ETaskStatus.CANCEL]: {
      className: styles.resourceLoadingIcon,
      icon: 'help-icon-zhuyi',
      balloon: null,
    },
  };

  const statusItem = resourceStatusMap?.[matchStatus];

  const iconEle = (
    <div className={statusItem.className}>
      <i className={`help-iconfont ${statusItem.icon}`} />
      {ResourceStatusMap[resource?.Status]}
    </div>
  );

  if (!statusItem?.balloon) {
    return iconEle;
  }

  return (
    <Balloon
      v2
      trigger={iconEle}
      closable={false}
      triggerType="hover"
    >
      {statusItem.balloon}
    </Balloon>
  );
};

export default function SpecificResources({ rosResourceInfo }) {
  const classStatusMap = {
    [ERosDeployStatus.CREATING]: styles.resourceLoading,
    [ERosDeployStatus.CREATE_COMPLETE]: styles.resourceSuccess,
    [ERosDeployStatus.CREATE_FAILED]: styles.resourceFailed,
  };

  return (
    <div className={styles.resourceList}>
      {_.map(rosResourceInfo?.Resources, (resource) => {
        const classStatus = classStatusMap?.[resource?.Status || ERosDeployStatus.CREATING];

        return (
          <div className={`${styles.resource} ${classStatus || styles.resourceLoading}`}>
            <div className={styles.resourceLeft}>
              <div>
                资源ID名称：{resource?.PhysicalResourceId}
              </div>
              <p>
                {resource?.ResourceType}
              </p>
            </div>
            <div className={styles.resourceRight}>
              <ResourceStatus resource={resource} />
            </div>
          </div>
        );
      })}
    </div>
  );
}
