import React from 'react';
import styles from './index.module.scss';
import { ERosDeployStatus } from '@/components/QuickDeploy/constant';
import { Table } from '@alifd/next';

export default function Output({ rosApiInfo }) {
  if (rosApiInfo?.Status === ERosDeployStatus.CREATING) {
    return (
      <div className={styles.error}>
        <img src="https://img.alicdn.com/imgextra/i2/O1CN01B0nt381j2vhkaNq4h_!!6000000004491-0-tps-284-224.jpg" alt="" />
        <p>当前资源栈创建中，请稍后查看</p>
      </div>
    );
  }

  if (rosApiInfo?.Status === ERosDeployStatus.CREATE_FAILED) {
    return (
      <div className={styles.error}>
        <img src="https://img.alicdn.com/imgextra/i2/O1CN01B0nt381j2vhkaNq4h_!!6000000004491-0-tps-284-224.jpg" alt="" />
        <p>当前资源栈创建失败，无输出数据</p>
      </div>
    );
  }

  return (
    <div className={styles.main}>
      <Table
        className={styles.table}
        dataSource={rosApiInfo?.Outputs}
        hasBorder={false}
      >
        <Table.Column title="输出关键字" dataIndex="OutputKey" />
        <Table.Column title="值" dataIndex="OutputValue" />
        <Table.Column
          title="描述"
          dataIndex="Description"
          cell={(value) => {
            return typeof value === 'string' ? value : value?.['zh-cn'];
          }}
        />
        <Table.Column title="错误信息" dataIndex="error" cell={(value) => (value || '-')} />
      </Table>
    </div>
  );
}
