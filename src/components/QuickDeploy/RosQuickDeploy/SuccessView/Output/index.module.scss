.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding: 35px 16px;

  img {
    width: 105px;
  }

  p {
    font-size: 14px;
    color: #81848F;
    text-align: left;
    line-height: 24px;
  }
}

.main {
  padding: 16px 0;
}

.table:global(.next-table) {
  :global {
    .next-table-header {
      font-size: 12px;

      th {
        background: #EFF3F8;
        white-space: nowrap;
      }
    }

    td {
      font-size: 12px;
    }
  }
}

