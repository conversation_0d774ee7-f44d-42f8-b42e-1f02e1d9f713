import React, { FunctionComponent } from 'react';
import { Button } from '@alifd/next';
import styles from './index.module.scss';
import { OreAlfaApp } from '@/components/QuickDeploy/RosQuickDeploy/CreateApp';
import { isServer } from '@/help-fe-common/utils/node/getContext';

interface IProps {
  children?: any;
  onSubmit: () => void;
  submitLoading: boolean;
  showAction: boolean;
  oreProps?: any;
}

const Footer: FunctionComponent<IProps> = ({ onSubmit, submitLoading, showAction, oreProps, children }) => {
  if (!showAction) return null;
  return (
    <div className={styles.bottomDiv}>
      <div className={styles.priceDiv}>
        {
          !isServer() &&
          <OreAlfaApp {...oreProps} />
        }
      </div>
      {
        showAction &&
        <Button
          type="primary"
          onClick={onSubmit}
          loading={submitLoading}
          className={[styles.btn, styles.primaryBtn].join(' ')}
        >
          立即部署
        </Button>
      }
    </div>
  );
};

export default Footer;
