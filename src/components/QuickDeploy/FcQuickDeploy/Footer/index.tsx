import React, { FunctionComponent } from 'react';
import { Balloon, Box, Button } from '@alifd/next';
import { RequiredProductRecord } from '@/services/helplab/fcConsole';
import Href from '../Href';
import styles from './index.module.scss';

interface IProps {
  requiredProducts: RequiredProductRecord[];
  onSubmit: () => void;
  submitLoading: boolean;
  submitDisabled: boolean;
  onClose: () => void;
  showAction: boolean;
}

const Footer: FunctionComponent<IProps> = ({ onSubmit, submitLoading, submitDisabled, onClose, requiredProducts, showAction }) => (
  <Box align="center" direction="row" justify="space-between" className={styles.footer}>
    {
      showAction && <Box align="center" direction="row" spacing={8}>
        <Button
          type="primary"
          onClick={onSubmit}
          loading={submitLoading}
          disabled={submitDisabled}
          className={styles.primaryBtn}
        >
          立即部署
        </Button>
        <Button type="normal" onClick={onClose}>取消</Button>
      </Box>
    }
    <div className={styles.productAccount}>
      <span>相关云服务可能会产生计费，可在此 </span>
      <Balloon
        trigger={<Button text type="primary" size="small" className={styles.accountBtn}>查看计费说明</Button>}
        triggerType="hover"
        closable={false}
        popupContainer={(d: any) => d?.parentNode}
        align="tr"
        v2
      >
        {
        Array.isArray(requiredProducts) && requiredProducts.map((item) => (
          <div key={item?.serviceCode} className={styles.accountItem}>
            <span>{item?.serviceName}</span>
            <Href
              style={{ marginLeft: 4 }}
              href={item.serviceBillUrl}
            >
              计费文档
            </Href>
          </div>))
      }
      </Balloon>
    </div>
  </Box>);

export default Footer;
