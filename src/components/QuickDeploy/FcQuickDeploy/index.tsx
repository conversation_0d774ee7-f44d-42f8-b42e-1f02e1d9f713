import React, { Fragment, FunctionComponent, useEffect, useMemo, useState } from 'react';
import { Box, Field } from '@alifd/next';
import { useRequest } from 'ice';
import { compact, isEmpty, pick } from 'lodash';
import QuickDialog from '@/components/QuickDeploy/FcQuickDeploy/QuickDialog';
import SuccessView from './SuccessView';
import RoleName from './RoleName';
import BaseDeploy from '@/components/QuickDeploy/BaseDeploy';
import StepContext from './StepContext';
import StepOne from './StepOne';
import StepThree from './StepThree';
import Footer from './Footer';
import PrimaryBtn from './PrimaryBtn';
import { EQuickDeployType } from '@/components/QuickDeploy/constant';
import { loginDetector } from '@/help-fe-common/utils/global/user';
import QuickDeployService from '@/services/helplab/quickDeploy';
import FcDeployService, { RequiredProductRecord } from '@/services/helplab/fcConsole';
import { safeJSONParse } from '@/help-fe-common/utils/global/safeJson';
import { renderUserApplication } from '@/components/QuickDeploy/utils/renderDeploy';


interface IProps {
  visible: boolean | undefined;
  onClose: () => void;
  deployId: string;
  templateId: string;
  deployType: EQuickDeployType;
  deployedNodeId: string;
  deployedNodeTitle: string;
  testStepAnchor?: string; // 测试跳转文档标记
  codeSelectionList?: [ISelection];
}

interface ISelection {
  templateName: string;
  label: string;
  selected?: boolean;
}

const QuickDeploy: FunctionComponent<IProps> = (props) => {
  const {
    visible,
    onClose,
    deployId,
    templateId: initTemplate,
    deployType,
    deployedNodeId,
    deployedNodeTitle,
    codeSelectionList,
    testStepAnchor,
  } = props;

  // 获取一键部署动态表单配置信息
  const {
    request: getDeployFormConfig,
    data: formConfigRes,
    loading: formCfgLoading,
  } = useRequest(QuickDeployService.getDeployFormConfig, { manual: true });

  // 提交表单创建应用
  const {
    request: createApplication,
  } = useRequest(FcDeployService.createApplication, { manual: true });

  const field = Field.useField();

  const [submitLoading, setFormLoading] = useState(false);

  const [successViewName, setSuccessView] = useState();

  const [template, setTemplate] = useState(initTemplate);

  const [productOpenStatus, setProductOpenStatus] = useState<boolean>();
  const [policyRequired, setPolicyRequired] = useState<boolean>();

  const formValues: Record<string, any> = field.getValues();

  const formErrors: Record<string, any> = field.getErrors();

  const deployCfgData = formConfigRes?.data;

  const formConfig = deployCfgData?.params;

  const requiredProducts: RequiredProductRecord[] = deployCfgData?.cloudServices;

  // 暂时去掉filed提交校验触发二次校验导致表单部分组件loading
  const onSubmit = () => {
    setFormLoading(true);
    const values: any = field.getValues();
    const params = {
      ...values,
      template,
    };
    loginDetector(() => {
      createApplication(params)
        .then((res) => {
          const message: Record<string, any> = safeJSONParse(res?.message);
          if (res.code === 'RunFcInnerError' && message?.data?.code === 'RAMAuthDenied') {
            const requiredAuthority = message?.data?.accessDeniedDetail?.AuthAction;
            QuickDialog({
              type: 'error',
              title: '没有权限',
              width: 500,
              content:
                (
                  <span style={{ wordBreak: 'break-all' }}>
                    当前用户没有{requiredAuthority}权限，请联系主账号为您添加上述权限，
                    或者直接赋予您 AliyunFCFullAccess 权限。解决后请重新进行创建应用提交。
                  </span>),
            });
            FcDeployService.runFcApp('deleteApplication', { name: params?.name });
            FcDeployService.runFcApp('deleteEnvironment', {
              appName: params?.name,
              envName: 'default',
            });
            setFormLoading(false);
            return;
          }
          if (res?.code === '200') {
            const name = res?.data?.name;
            QuickDeployService.saveDeployRecord({
              applicationId: name,
              applicationType: deployType,
              deployId,
              deployedNodeId,
              deployedNodeTitle,
              testStepAnchor,
            });
            setSuccessView(name);
            setFormLoading(false);
            return;
          }
          QuickDialog({
            type: 'notice',
            title: '应用创建失败提示',
            content: '应用创建失败, 请稍后重新尝试!',
          });
          setFormLoading(false);
        });
    });
  };

  const onTemplateChange = (templateName: string) => {
    setTemplate(templateName);
  };

  const onProductOpenStatus = (status: boolean) => {
    setProductOpenStatus(status);
  };

  const onPolicyRequired = (required: boolean) => {
    setPolicyRequired(required);
  };

  /**
   * 提交按钮是否置灰逻辑
   */
  const submitDisabled = useMemo(() => {
    // 第一步产品是否都开通
    const stepOneStatus = productOpenStatus;
    // 第二步所需的策略都已授权
    const stepTwoStatus = policyRequired;
    // 必填值不为空以及错误校验
    const requiredKeys = (formConfig || []).filter((x: any) => x.required)
      .map((y: any) => y.property);
    const requiredValues = pick(formValues, [...requiredKeys, 'name']);
    const validateValue = Object.values(requiredValues)
      .every((item) => !isEmpty(item));

    const hasErrs = !isEmpty(compact(Object.values(formErrors)));

    return submitLoading || !stepOneStatus || stepTwoStatus || hasErrs || !validateValue;
  }, [productOpenStatus, submitLoading, policyRequired, formConfig, formValues, formErrors]);

  useEffect(() => {
    if (!template || !visible) return;
    getDeployFormConfig({ template });
  }, [template, visible]);

  const renderHeader = () => {
    return (
      <Box align="center" direction="row" spacing={8}>
        <h3>一键部署</h3>
        <PrimaryBtn
          text
          onClick={(e: MouseEvent) => {
            // onClose();
            renderUserApplication({ searchValue: deployedNodeTitle, deployType: EQuickDeployType.FC });
          }}
        >
          查看已创建的应用
        </PrimaryBtn>
      </Box>
    );
  };

  return (
    <BaseDeploy
      title={renderHeader()}
      footer={<Footer
        requiredProducts={requiredProducts}
        onClose={onClose}
        onSubmit={onSubmit}
        submitLoading={submitLoading}
        submitDisabled={submitDisabled}
        showAction={!successViewName}
      />}
      visible={visible}
      onVisibleChange={onClose}
    >
      {successViewName ?
        <SuccessView reviewName={successViewName} onClose={onClose} /> :
        <Fragment>
          <StepContext
            stepList={[{
              title: '快速开通服务',
              description: '部分产品可免费试用，如您具有免费试用资格，可开通产品试用。直接开通下列云产品可能会产生费用，您可以查看每个产品的计费文档了解详情。',
              content: <StepOne requiredProducts={requiredProducts} onProductOpenStatus={onProductOpenStatus} />,
            },
            {
              title: '快速创建应用角色',
              description: '一键部署需要通过角色扮演获取临时身份凭证从而访问您的的云服务。 为简化授权步骤，我们希望您能够使用系统默认角色AliyunFCServerlessDevsRole， 从而拥有访问的部分云资源的权限。',
              content: <RoleName applicationRole={deployCfgData?.applicationRole} onPolicyRequired={onPolicyRequired} />,
            },
            {
              title: '创建应用',
              description: '下方是为您默认配置的对应应用模版、部署类型和应用名称，您可以继续配置变量。',
              content: <StepThree
                deployCfgData={deployCfgData}
                template={template}
                codeSelectionList={codeSelectionList}
                field={field}
                loading={formCfgLoading}
                onTemplateChange={onTemplateChange}
              />,
            },
            ]}
          />
        </Fragment>
      }
    </BaseDeploy>);
};

export default QuickDeploy;
