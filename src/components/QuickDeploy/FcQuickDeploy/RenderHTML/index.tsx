import React, { FunctionComponent } from 'react';
import styles from './index.module.scss';

interface IProps {
  content: string | undefined;
}

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
// eslint-disable-next-line react/no-danger
const RenderHTML: FunctionComponent<IProps> = (props) => (<div dangerouslySetInnerHTML={{ __html: props.content }} className={styles.richText}/>)

export default RenderHTML;
