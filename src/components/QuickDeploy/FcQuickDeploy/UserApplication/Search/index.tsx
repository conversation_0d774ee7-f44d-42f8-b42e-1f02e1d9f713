import React, { FunctionComponent } from 'react';
import { Input } from '@alifd/next';
import classnames from 'classnames';
import { InputProps } from '@alifd/next/lib/input/index.d';
import styles from './index.module.scss';

interface IProps extends InputProps {
  onSearch: () => void;
}

const Search: FunctionComponent<IProps> = (props) => {
  const { onSearch, ...reset } = props;

  return (
    <section className={styles.searchRoot}>
      <Input
        className={styles.search}
        onPressEnter={onSearch}
        {...reset}
        addonAfter={
          <i className={classnames('help-iconfont help-icon-sousuoicon', styles.searchBtn)} onClick={onSearch} />}
      />
    </section>);
};

export default Search;
