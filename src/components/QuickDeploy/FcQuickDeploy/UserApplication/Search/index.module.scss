.searchRoot {
  display: flex;
  align-items: center;

  .search {
    max-width: 300px;
    width: 20vw;
    border-radius: 2px;

    & > span:nth-child(1) {
      border: 1px solid rgb(203, 203, 203);
      box-shadow: none;
    }

    input {
      font-size: 12px;

      &::placeholder {
        color: #808080;
        font-size: 12px;
      }
    }

    :global {
      .next-input-control .next-input-clear-icon::before {
        content: "\e626";
        font-size: 10px;
      }
    }
  }

  .searchBtn {
    height: 32px;
    line-height: 32px;
    text-align: center;
    padding: 0 10px;
    display: inline-block;
    color: #333333;
    border: 1px solid rgb(203, 203, 203);
    font-size: 10px;

    &:hover {
      cursor: pointer;
    }
  }
}
