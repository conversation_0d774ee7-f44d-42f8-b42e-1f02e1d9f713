import React, { FunctionComponent } from 'react';
import { Button } from '@alifd/next';
import FcConsoleService from '@/services/helplab/fcConsole';
import { fcConsoleHost } from '@/components/QuickDeploy/utils/consoleEnv';
import styles from './index.module.scss';

interface IProps {
  record: Record<string, any>;
}

const FailCheck: FunctionComponent<IProps> = (props) => {
  const { record } = props;

  const onFailCheck = () => {
    FcConsoleService.runFcApp('describeApplication', { name: record?.applicationId })
      .then((res: any) => {
        if (res?.data) {
          const versionId = res?.data?.lastRelease?.versionId;
          const { applicationId } = record;
          if (versionId && applicationId) {
            window.open(`${fcConsoleHost}/applications/${applicationId}/release/${versionId}`);
          }
        }
      });
  };

  return (<Button
    type="primary"
    text
    style={{ marginLeft: 4 }}
    onClick={() => onFailCheck()}
    className={[styles.href, styles.link].join(' ')}
  >
    查看
  </Button>);
};

export default FailCheck;
