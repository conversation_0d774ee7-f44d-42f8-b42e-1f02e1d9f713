$primary-color: #0064C8;

:global {
  .nextTooltip.next-balloon-tooltip {
    background-color: #fff;
    font-weight: 500;
    font-size: 12px;
    color: #808080;
    letter-spacing: 0.1px;
    line-height: 24px;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.10);
    border: 1px solid #e6e6e6;

    .next-balloon-arrow-content {
      background-color: #fff;
      border: 1px solid #e6e6e6;
    }
  }
}

.href.link {
  font-size: 12px;
  color: $primary-color;
  text-decoration: none;

  &:link {
    color: $primary-color;
  }

  &:visited {
    color: $primary-color;
  }
}

.href.link[disabled] {
  color: #cccccc;
}

.optCell {
  display: flex;
  align-items: center;

  .btnGap {
    width: 1px;
    display: inline-block;
    height: 12px;
    background: #CBCBCB;
    margin: 0 8px;
  }
}
