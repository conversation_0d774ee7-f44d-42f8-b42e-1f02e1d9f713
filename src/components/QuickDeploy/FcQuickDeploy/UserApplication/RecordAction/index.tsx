import React, { FunctionComponent } from 'react';
import { Button, Balloon } from '@alifd/next';
import { debounce, toPairs } from 'lodash';
import FcConsoleService from '@/services/helplab/fcConsole';
import QuickDeployService from '@/services/helplab/quickDeploy';
import { fcConsoleHost } from '@/components/QuickDeploy/utils/consoleEnv';
import { ETaskStatus } from '../../../ListApplication/table';
import styles from './index.module.scss';

const { Tooltip } = Balloon;

interface IProps {
  record: Record<string, any>;
}

const RecordAction: FunctionComponent<IProps> = (props) => {
  const { record } = props;

  const onTestClick = async (item: Record<string, any>) => {
    if (item.docUrl) {
      window.open(item.docUrl);
      return;
    }
    Promise.all([
      FcConsoleService.runFcApp('describeApplication', { name: item?.applicationId }),
      QuickDeployService.getEventJson(item?.templateName)])
      .then((values: any) => {
        const [deployRes, eventRes] = values;
        if (deployRes?.data) {
          const deployContext = deployRes?.data?.lastRelease?.output?.deploy || {};
          const deployList = toPairs(deployContext);
          const matchDomain = deployList.find(([, val]) => val?.domainName);
          if (matchDomain) {
            const [, val] = matchDomain;
            window.open(`${val?.protocol?.toLowerCase()}://${val?.domainName}`);
            return;
          }
          const matchFunction = deployList.find(([, val]) => val?.functionName && val.region);
          if (matchFunction) {
            const [, val] = matchFunction || [];
            const {
              functionName,
              region,
            } = val || {};
            const url = `${fcConsoleHost}/${region}/functions/${functionName}`;
            const search = eventRes?.data ? `?tab=testing&defaultEvent=${encodeURIComponent(eventRes?.data)}` : '?tab=testing';
            window.open(url + search);
          }
        }
      });
  };

  const disabled = record?.deployStatus === ETaskStatus.PROCESSING;
  // eslint-disable-next-line no-nested-ternary
  const testButtonTip = disabled ? '您的应用正在部署中，暂时无法进行测试，请等待部署完成后操作。' : record?.docUrl ? '参考文档完成测试' : '';
  const testButtonVisible = record?.deployStatus in [ETaskStatus.SUCCESS, ETaskStatus.PROCESSING];
  const testActionBtn = (
    <Button
      type="primary"
      text
      disabled={record?.deployStatus === ETaskStatus.PROCESSING}
      onClick={debounce(() => onTestClick(record), 300)}
      className={[styles.href, styles.link].join(' ')}
    >
      测试
    </Button>);

  return (
    <section className={styles.optCell}>
      {
        testButtonVisible && (testButtonTip ? <Tooltip
          trigger={testActionBtn}
          popupClassName="nextTooltip"
          v2
          align="r"
        >
          {testButtonTip}
        </Tooltip> : testActionBtn)
      }
      {
        testButtonVisible && <span className={styles.btnGap} />
      }
      <Button
        type="primary"
        text
        component="a"
        target="_blank"
        href={`${fcConsoleHost}/applications/${record?.applicationId}/overview`}
        className={[styles.href, styles.link].join(' ')}
      >
        详情
      </Button>
    </section>
  );
}

export default RecordAction
