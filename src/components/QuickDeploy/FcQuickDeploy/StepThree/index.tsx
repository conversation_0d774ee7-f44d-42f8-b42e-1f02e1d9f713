import React, { FunctionComponent, useEffect, useMemo, useState } from 'react';
import { Form, Input, Select, Switch } from '@alifd/next';
import { isEmpty } from 'lodash';
import { generateRandom } from '../../utils';
import FcDeployService from '@/services/helplab/fcConsole';
import XRole from '../XRole';
import XBucket from '../XBucket';
import LoadingWrapper from '../LoadingWrapper';
import SwitchTab from './SwitchTab';
import RenderHTML from '../RenderHTML';
import styles from './index.module.scss';

interface ISelection {
  templateName: string;
  label: string;
  selected?: boolean;
}

interface IProps {
  template: string;
  codeSelectionList?: [ISelection];
  deployCfgData: Record<string, any>;
  field: Record<string, any>;
  loading: boolean;
  onTemplateChange: Function;
}

const { Item } = Form;

const StepThree: FunctionComponent<IProps> = (props) => {
  const {
    template,
    codeSelectionList,
    deployCfgData,
    field,
    loading,
    onTemplateChange,
  } = props;

  const formConfig = deployCfgData?.params;

  const {
    getError,
    init,
    getValue,
    validate,
    setValue,
    resetToDefault,
  } = field;

  const [selectLang, setSelectLang] = useState<string>();

  const onValidatorName = async (rules: Record<string, any>, value: string) => {
    if (!value) {
      return Promise.resolve(null);
    }
    const regionId: string = getValue('region');
    const res = await FcDeployService.checkDuplicateName({
      regionId,
      name: value.trim(),
    });
    if (!isEmpty(res?.data)) {
      return Promise.reject('该应用名称已被占用');
    }
    return Promise.resolve(null);
  };

  const onLangChange = (lang: string, item: any) => {
    setSelectLang(lang);
    onTemplateChange(item.templateName);
  };

  const random = useMemo(() => {
    return generateRandom();
  }, [template]);

  // 配置更新则重置默认值
  useEffect(() => {
    if (loading) return;
    resetToDefault();
  }, [loading]);

  const formatSwitchSource = useMemo(() => {
    const selectedItem = codeSelectionList?.find((item) => item.selected);
    setSelectLang(selectedItem?.label);
    return codeSelectionList?.map((item) => ({
      ...item,
      label: item.label,
      value: item.label,
    })) || [];
  }, [codeSelectionList]);

  const renderFormComponent = (configItem: any) => {
    const {
      title,
      property,
      defaultValue,
      required,
      description,
    } = configItem;
    if (configItem?.customTag === 'x-role') {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      return <XRole init={init} {...configItem} validate={validate} setValue={setValue} />;
    }
    if (configItem?.customTag === 'x-bucket') {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      return <XBucket init={init} setValue={setValue} getValue={getValue} {...configItem} />;
    }
    if (configItem.type === 'select') {
      const { options } = configItem;
      return (<Select
        placeholder={`请选择${title}`}
        dataSource={options || []}
        style={{ width: '100%' }}
        menuProps={{ hasSelectedIcon: false }}
        popupContainer={(d: any) => d.parentNode}
        {...init(`${property}`, {
          initValue: defaultValue,
          rules: [
            {
              required,
              message: `${title}不能为空`,
            },
          ],
        })}
      />);
    }
    if (configItem.type === 'boolean') {
      return (<Switch
        size="small"
        autoWidth
        className={styles.switchBtn}
        {...init(`${property}`, {
          initValue: !!defaultValue,
          rules: [{
            required,
            message: `请选择${title}`,
          }],
        })
        }
      />);
    }
    const { pattern } = configItem;
    return (<Input
      placeholder={`请输入${title}`}
      {...init(`${property}`, {
        initValue: defaultValue,
        rules: [
          {
            required,
            message: `${title}不能为空`,
          },
          {
            pattern,
            message: description,
          },
        ],
      })}
    />);
  };

  return (
    <LoadingWrapper loading={loading}>
      {
        formatSwitchSource?.length > 1 &&
        <Item required label="语言">
          <SwitchTab
            value={selectLang}
            onChange={onLangChange}
            dataSource={formatSwitchSource}
          />
        </Item>
      }

      <Form field={field}>
        <Item
          label="应用名称"
          required
          help={<RenderHTML content={getError('name') || '必须以字母开头，可含数字、字母（大小写敏感）、连字符，长度小于64个字符。'} />}
        >
          <Input
            {...init('name', {
              initValue: `${template?.replace(/[^a-zA-Z0-9-]/g, '-')}-${random}`,
              rules: [
                {
                  required: true,
                  message: '请输入应用名称',
                },
                {
                  pattern: /^[a-zA-Z_][a-zA-Z0-9-_]{0,63}$/,
                  message: '必须以字母开头，可含数字、字母（大小写敏感）、连字符，长度小于64个字符。',
                },
                {
                  validator: onValidatorName,
                  trigger: 'onBlur',
                },
              ],
            })}
          />
        </Item>
        <Item label="描述">
          <Input
            {...init('description', { initValue: deployCfgData?.description })}
          />
        </Item>
        {
          Array.isArray(formConfig) && formConfig.map((configItem) => {
            return (
              <Item
                key={configItem?.property}
                label={configItem?.title}
                required={configItem?.required}
                help={getError(configItem?.property) || <RenderHTML content={configItem?.description} />}
              >
                {renderFormComponent(configItem)}
              </Item>);
          })
        }
      </Form>
    </LoadingWrapper>);
};

export default StepThree;
