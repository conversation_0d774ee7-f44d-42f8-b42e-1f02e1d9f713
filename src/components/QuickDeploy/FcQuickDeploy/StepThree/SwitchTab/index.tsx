import React, { FunctionComponent } from 'react';
import styles from './index.module.scss';

interface SourceRecord extends Record<string, any>{
  label: string;
  value: string;
}

interface IProps {
  dataSource: SourceRecord[];
  value: string | number | undefined;
  onChange: Function;
}

const SwitchTab: FunctionComponent<IProps> = (props) => {
  const {
    dataSource,
    value,
    onChange,
  } = props;

  const onItemClick = (item: SourceRecord) => {
    if (value !== item.value) {
      onChange && onChange(item.value, item);
    }
  };

  return (<ul className={styles.switchTab}>
    {
      Array.isArray(dataSource) && dataSource.map((item) => (
        <li
          key={item.value}
          className={value === item.value ? [styles.activeItem, styles.normalItem].join(' ') : styles.normalItem}
          onClick={() => onItemClick(item)}
        >
          {item.label}
        </li>))
    }
  </ul>);
};

export default SwitchTab;
