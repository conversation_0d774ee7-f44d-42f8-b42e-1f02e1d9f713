import React, { FunctionComponent } from 'react';
import styles from './index.module.scss';


interface IProps extends Record<string, any> {
  children: string | React.ReactElement;
  style?: React.CSSProperties;
  className?: string;
}

const Href: FunctionComponent<IProps> = (props) => {
  const { children, target = '_blank', style, className, ...reset } = props;
  return (<a target={target} rel="noreferrer" className={[styles.link, styles.customerLink, className].join(' ')} style={style} {...reset}>{children}</a>);
};

export default Href;
