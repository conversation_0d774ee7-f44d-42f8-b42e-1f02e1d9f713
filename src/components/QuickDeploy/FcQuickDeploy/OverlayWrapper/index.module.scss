.wrapperContext {
  position: relative;

  :global {
    .next-drawer-close .next-drawer-close-icon.next-icon:before {
      width: 12px;
      height: 12px;
      font-size: 12px;
      line-height: inherit;
      vertical-align: bottom;
    }
  }

  .title {
    font-weight: 600;
    font-size: 16px;
    color: #333333;
    letter-spacing: 0;
    margin: 0;
  }

  .primaryBtn {
    background: #1366EC;

    &:hover {
      background:  #1366EC;
    }

    &[disabled] {
      background: #EDEDED;
      color: #999999;
    }
  }

  .content {
    padding: 20px;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    overscroll-behavior: contain;
  }

  .wrapperContent {
    height: calc(100vh - 123px);
  }

  .fullContent {
    height: calc(100vh - 51px);
  }

  .footer {
    width: 100%;
    position: absolute;
    bottom: 0;
    right: 0;
    padding: 20px;
  }

  .footerShadow {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.10);
  }
}
