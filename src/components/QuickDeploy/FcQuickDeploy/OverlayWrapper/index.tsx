import React, { FunctionComponent, useRef, useState } from 'react';
import { Box, Drawer, Button } from '@alifd/next';
import { debounce, isString, isBoolean } from 'lodash';
import { DrawerProps } from '@alifd/next/lib/drawer/index.d';
import { ButtonProps } from '@alifd/next/lib/button/index.d';
import useEventListener from '@/help-fe-common/hooks/useEventListener';
import classnames from 'classnames';
import styles from './index.module.scss';

interface IProps extends DrawerProps {
  title: React.ReactNode | string;
  footer: boolean | React.ReactElement;
  children: React.ReactElement;
  onOk?: () => void;
  onCancel?: () => void;
  okProps?: ButtonProps;
  cancelProps?: ButtonProps;
}

const OverlayWrapper: FunctionComponent<IProps> = (props) => {
  const {
    title,
    children,
    footer,
    onOk,
    onCancel,
    okProps = {},
    cancelProps = {},
    ...reset
  } = props;

  const contentRef = useRef<any>();

  const [hasShadow, setShadow] = useState<boolean>(false);

  const onScroll = debounce((ev: any) => {
    if (ev.target.scrollTop > 0) {
      setShadow(true);
    } else {
      setShadow(false);
    }
  }, 100, { leading: true });

  useEventListener(contentRef?.current, 'scroll', onScroll);

  const renderDefaultFooter = () => {
    return (<Box align="center" direction="row" spacing={8}>
      <Button type="primary" onClick={onOk} className={styles.primaryBtn} {...okProps}>确定</Button>
      <Button type="normal" onClick={onCancel} {...cancelProps}>取消</Button>
    </Box>);
  }

  return (<Drawer
    title={isString(title) ? <h4 className={styles.title}>{title}</h4> : title}
    width={620}
    bodyStyle={{ padding: 0 }}
    wrapperClassName={styles.wrapperContext}
    {...reset}
  >
    <section className={classnames(styles.content, footer ? styles.wrapperContent : styles.fullContent)} ref={contentRef}>
      {children}
    </section>
    {
      Boolean(footer) && <footer className={classnames(styles.footer, hasShadow ? styles.footerShadow : null)}>
        { isBoolean(footer) ? renderDefaultFooter() : footer }
      </footer>
    }
  </Drawer>);
};

export default OverlayWrapper;
