import React, { FunctionComponent } from 'react';
import classnames from 'classnames';
import styles from './index.module.scss';

export enum NoticeType {
  'ERROR' = 'error',
  'NOTICE' = 'notice',
}

interface IProps {
  title: string | React.ReactElement;
  desc: string | React.ReactElement;
  type?: NoticeType;
  style?: React.CSSProperties;
}

const MessageBar: FunctionComponent<IProps> = (props) => {
  const { title, desc, type = NoticeType.ERROR, style } = props;
  return (<div className={classnames(styles.messageBar, styles[type])} style={style}>
    { Boolean(title) && <h4 className={styles.title}>{title}</h4> }
    { Boolean(desc) && <span className={styles.desc}>{desc}</span> }
  </div>);
};

export default MessageBar;
