import React, { FunctionComponent } from 'react';
import { Loading } from '@alifd/next';
import styles from './index.module.scss';

interface IProps {
  children: React.ReactNode;
  loading: boolean;
  style?: React.CSSProperties;
}

const LoadingWrapper: FunctionComponent<IProps> = ({ children, loading, style }) => (
  <Loading className={styles.loadingWrapper} visible={loading} style={style}> {children} </Loading>
);

export default LoadingWrapper;
