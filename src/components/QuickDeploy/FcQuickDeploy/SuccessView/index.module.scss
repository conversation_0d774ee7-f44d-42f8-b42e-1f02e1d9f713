$primary-color: #1366ec;

@mixin primaryStyle {

  &:active {
    color: $primary-color;
  }
  &:visited {
    color: $primary-color;
  }
  &:hover {
    color: $primary-color;
  }
}

.primaryBtn {
  background: $primary-color !important;
  color: #fff;

  @include primaryStyle;
}

.viewContext {
  padding: 128px 20px 0;

  .successIcon {
    color: #06B624;
  }

  .title {
    font-weight: 500;
    font-size: 22px;
    color: #333333;
    line-height: 24px;
  }

  .desc {
    font-weight: 400;
    font-size: 12px;
    color: #555555;
    line-height: 20px;
    margin-bottom: 30px;
  }

  .content {
    padding-left: 28px;
  }
}
