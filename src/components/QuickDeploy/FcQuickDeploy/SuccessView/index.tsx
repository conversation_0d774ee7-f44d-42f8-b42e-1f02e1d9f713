import React, { FunctionComponent } from 'react';
import { Box, Button, Icon } from '@alifd/next';
import { renderUserApplication } from '@/components/QuickDeploy/utils/renderDeploy';
import { EQuickDeployType } from '@/components/QuickDeploy/constant';
import styles from './index.module.scss';


interface IProps {
  reviewName: string;
  onClose: () => void;
}

const SuccessView: FunctionComponent<IProps> = ({ reviewName, onClose }) => (
  <div className={styles.viewContext}>
    <Box align="center" direction="row" spacing={8} style={{ marginBottom: 20 }}>
      <Icon type="success" className={styles.successIcon} />
      <h2 className={styles.title}>创建应用成功</h2>
    </Box>
    <div className={styles.content}>
      <p className={styles.desc}>
        您可以点击<b>查看应用</b>，查看部署状态。<br />
        部署成功后，您还可以点击<b>【测试】</b>，快速验证应用效果。
      </p>
      <Button
        type="primary"
        component="a"
        className={styles.primaryBtn}
        onClick={(e) => {
          renderUserApplication({ searchValue: reviewName, deployType: EQuickDeployType.FC });
        }}
      >
        查看应用
      </Button>
    </div>
  </div>
);

export default SuccessView;
