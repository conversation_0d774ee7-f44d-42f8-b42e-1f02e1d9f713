import React, { FunctionComponent } from 'react';
import { Icon } from '@alifd/next';
import PrimaryBtn from '../PrimaryBtn';

interface IProps {
  onClick?: () => void;
  style?: React.CSSProperties;
}

const FreshBtn: FunctionComponent<IProps> = ({ onClick, style }) => {
  return (<PrimaryBtn onClick={onClick} style={style} text>
    <span style={{ marginRight: 4 }} >刷新</span>
    <Icon type="refresh" size="xxs" />
  </PrimaryBtn>);
};

export default FreshBtn;
