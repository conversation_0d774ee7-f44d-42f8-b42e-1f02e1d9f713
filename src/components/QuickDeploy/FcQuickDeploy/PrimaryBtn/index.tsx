import React, { FunctionComponent } from 'react';
import classnames from 'classnames';
import styles from './index.module.scss';

interface IProps {
  children: React.ReactNode;
  text?: boolean;
  onClick?: (e: any) => void;
  style?: React.CSSProperties;
  disabled?: boolean;
  className?: string;
}

const PrimaryBtn: FunctionComponent<IProps> = (props) => {
  const { children, text = false, onClick, style, disabled = false, className, ...reset } = props;

  return (<span
    className={classnames(styles.primaryBtn, text ? styles.textBtn : styles.normalBtn, disabled ? styles.disabledBtn : null, className)}
    onClick={disabled ? () => {} : onClick}
    style={style}
    {...reset}
  >
    {children}
  </span>);
};

export default PrimaryBtn;
