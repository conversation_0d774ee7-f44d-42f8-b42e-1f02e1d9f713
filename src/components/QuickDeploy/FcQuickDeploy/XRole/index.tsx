import React, { useEffect, useMemo } from 'react';
import { Box, Select, Balloon } from '@alifd/next';
import { useRequest } from 'ice';
import { difference, isEmpty } from 'lodash';
import { generateAuthorizeUrl } from '../../utils';
import useEventListener from '@/help-fe-common/hooks/useEventListener';
import FcDeployService, { XRoleCustomValues } from '@/services/helplab/fcConsole';

import MessageBar from '../MessageBar';
import PrimaryBtn from '../PrimaryBtn';
import LoadingWrapper from '../LoadingWrapper';
import FreshBtn from '../FreshBtn';
import Href from '../Href';
import styles from './index.module.scss';

interface IProps {
  defaultValue: string;
  description?: string;
  pattern?: RegExp;
  property: string;
  required: boolean;
  title: string;
  init: Function;
  type: string;
  customValues: XRoleCustomValues;
  validate: Function;
  setValue?: Function;
}

declare global {
  interface window {
    location: Record<string, any>;
  }
}

enum ActionEnum {
  AUTHORIZE = 'fc-policy-authorize',
  CREATE_ROLE = 'fc-role-create'
}

const XRole: (props: IProps) => [JSX.Element, JSX.Element] = (props) => {
  const {
    property,
    required,
    title,
    description,
    customValues,
    init,
    validate,
    setValue,
  } = props;

  const {
    request: getUserRoleList,
    data: userRoleListRes,
    loading: getRoleListLoading,
  } = useRequest(FcDeployService.getUserRoleList, { manual: true });

  const {
    loading: getPolicyListLoading,
    request: getListPolicyAttachments,
  } = useRequest(FcDeployService.getListPolicyAttachments, { manual: true });

  const tip = `请选择${title}`;

  const onFresh = async () => {
    await getUserRoleList(customValues?.service);
    validate(property);
  };

  const onCreateRole = () => {
    generateAuthorizeUrl({
      service: customValues?.service,
      roleName: customValues?.name,
      requiredPolicy: customValues.authorities,
      description,
      actionType: ActionEnum.CREATE_ROLE,
    });
  };

  const onAuthorize = (requiredPolicy: string[], roleName: string) => {
    generateAuthorizeUrl({
      service: customValues?.service,
      roleName,
      requiredPolicy,
      description,
      actionType: ActionEnum.AUTHORIZE,
    });
  };

  const onValidatePolicy = async (rules, value) => {
    if (!value) return Promise.resolve(null);
    const selectItem = formatDataSource.find((item) => item.value === value);
    const principalName = selectItem?.extra?.RolePrincipalName;
    const res = await getListPolicyAttachments(principalName);
    if (res?.code === '200') {
      const currentPolicy = res?.data?.PolicyAttachments?.PolicyAttachment || [];
      const hasPolicy = currentPolicy.map((item: any) => item?.PolicyName);
      const requiredPolicy = difference(customValues?.authorities, hasPolicy);
      if (!isEmpty(requiredPolicy)) {
        const roleName = formatDataSource.find((item) => item.value === value)?.label;
        return Promise.reject(
          <div className={styles.requiredPolicyAttach}>
            <p className={styles.requiredPolicy}>您当前选择的应用还需要额外一些权限：{requiredPolicy.join('、')}</p>
            <PrimaryBtn
              text
              onClick={() => onAuthorize(requiredPolicy, roleName)}
            >
              前往授权
            </PrimaryBtn>
          </div>,
        );
      }
      return Promise.resolve(null);
    }
    return Promise.reject('获取权限接口失败!');
  };

  const onMessageListen = (ev) => {
    if (ev?.data?.actionType === ActionEnum.AUTHORIZE) {
      validate(property);
    }
    if (ev.data.actionType === ActionEnum.CREATE_ROLE) {
      getUserRoleList(customValues?.service);
    }
  };

  const disableCreateRole = useMemo(() => {
    const roles = userRoleListRes?.data?.Roles?.Role || [];
    const defaultRole = customValues?.name;
    const match = roles.find((item) => item.RoleName === defaultRole);
    return !!match;
  }, [customValues, userRoleListRes]);

  useEventListener(window, 'message', onMessageListen);

  useEffect(() => {
    const roles = userRoleListRes?.data?.Roles?.Role || [];
    const matchRole = customValues?.name;
    if (!matchRole || isEmpty(roles)) return;
    const match = roles.find((item) => item.RoleName === matchRole);
    if (match) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      setValue(property, match?.Arn);
    } else {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      setValue(property, roles?.[0]?.Arn);
    }
    validate(property);
  }, [customValues, userRoleListRes]);

  useEffect(() => {
    if (!customValues?.service) return;
    getUserRoleList(customValues?.service);
  }, [customValues]);

  const formatDataSource = useMemo(() => {
    const source = userRoleListRes?.data?.Roles?.Role || [];
    return source.map((item: any) => ({
      label: item.RoleName,
      value: item.Arn,
      extra: item,
    }));
  }, [userRoleListRes]);

  const renderCreateRole = (disabled: boolean) => {
    return (
      <PrimaryBtn
        text
        onClick={onCreateRole}
        disabled={disabled}
      >
        创建新的服务角色
      </PrimaryBtn>);
  };

  return [
    <Select
      placeholder={tip}
      dataSource={formatDataSource}
      style={{ width: '100%' }}
      hasClear
      popupContainer={(d: any) => d.parentNode}
      {...init(`${property}`, {
        rules: [{
          required,
          message: tip,
        },
        { validator: onValidatePolicy },
        ],
      })}
    />,
    <LoadingWrapper loading={getRoleListLoading || getPolicyListLoading}>
      <Box direction="row" align="center" spacing={20} style={{ marginTop: 8 }}>
        <FreshBtn onClick={onFresh} />
        {
          disableCreateRole ?
            <Balloon
              trigger={renderCreateRole(true)}
              triggerType="hover"
              closable={false}
              align="t"
              popupContainer={(d: any) => d.parentNode}
            >
              您已创建系统推荐角色 {customValues?.name}，推荐并使用该角色，暂不支持创建其他角色。
            </Balloon> : renderCreateRole(false)
        }
      </Box>
      {
        userRoleListRes?.code === 'NoPermission' ? <MessageBar
          title="无法获取角色"
          desc={
            <span>
              您当前登录的是RAM子账号，请您确保授权主体为函数计算，点击
              <PrimaryBtn text onClick={onCreateRole}>这里</PrimaryBtn>
              复制授权链接，发给您的主账号进行授权。并请联系主账号通过
              <Href href="https://ram.console.aliyun.com/users">RAM</Href>
              控制台赋予您的子账号AliyunRAMReadOnlyAccess,AliyunFCFullAccess权限，添加权限后请刷新当前页面进行重试。
            </span>}
        /> : null
      }
    </LoadingWrapper>,
  ];
};

export default XRole;
