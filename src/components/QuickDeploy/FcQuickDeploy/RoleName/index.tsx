import React, { useEffect, useMemo, Fragment, FunctionComponent } from 'react';
import { Form } from '@alifd/next';
import { useRequest } from 'ice';
import { isEmpty, difference } from 'lodash';
import { generateAuthorizeUrl } from '../../utils';
import FcDeployService from '@/services/helplab/fcConsole';
import MessageBar, { NoticeType } from '../MessageBar';
import PrimaryBtn from '../PrimaryBtn';
import FreshBtn from '../FreshBtn';
import LoadingWrapper from '../LoadingWrapper';
import useEventListener from '@/help-fe-common/hooks/useEventListener';
import styles from './index.module.scss';

interface IProps {
  applicationRole: Record<string, any>;
  onPolicyRequired: Function;
}

enum ActionEnum {
  AUTHORIZE = 'roleName-policy-authorize',
  CREATE_ROLE = 'default-roleName-create'
}

const { Item: FormItem } = Form;

const RoleName: FunctionComponent<IProps> = (props) => {
  const { applicationRole, onPolicyRequired } = props;

  const { roleName, authorities, description } = applicationRole || {};

  const { request: getUserRole, data: getUserRoleRes, loading: getUserRoleLoading } = useRequest(FcDeployService.getUserRole, { manual: true });

  const {
    request: getUserRoleList,
    data: roleListRes,
    loading: getRoleListLoading } = useRequest(FcDeployService.getListPolicyAttachments, { manual: true });

  const rolePrincipalName = getUserRoleRes?.data?.Role?.RolePrincipalName;

  const userHasPolicyList = roleListRes?.data?.PolicyAttachments?.PolicyAttachment;

  const onAuthorize = () => {
    generateAuthorizeUrl({
      roleName,
      requiredPolicy,
      description,
      actionType: ActionEnum.AUTHORIZE,
    });
  };

  const onCreateRole = () => {
    generateAuthorizeUrl({
      roleName,
      requiredPolicy: authorities,
      description,
      actionType: ActionEnum.CREATE_ROLE,
    });
  };

  const onFresh = async () => {
    if (!roleName) return;
    await getUserRole(roleName);
    if (!rolePrincipalName) return;
    getUserRoleList(rolePrincipalName);
  };

  const onMessageListen = async (ev) => {
    if (ev?.data?.actionType === ActionEnum.AUTHORIZE) {
      getUserRoleList(rolePrincipalName);
    }
    if (ev.data.actionType === ActionEnum.CREATE_ROLE) {
      const useRoleRes = await getUserRole(roleName);
      const principalName = useRoleRes?.data?.Role?.RolePrincipalName;
      getUserRoleList(principalName);
    }
  };

  const requiredPolicy = useMemo(() => {
    const hasPolicy = (userHasPolicyList || []).map((item: any) => item.PolicyName);
    return difference(authorities, hasPolicy);
  }, [authorities, userHasPolicyList]);

  useEventListener(window, 'message', onMessageListen);

  useEffect(() => {
    onPolicyRequired(!isEmpty(requiredPolicy));
  }, [requiredPolicy]);

  useEffect(() => {
    if (!rolePrincipalName) return;
    getUserRoleList(rolePrincipalName);
  }, [rolePrincipalName]);

  useEffect(() => {
    if (!roleName) return;
    getUserRole(roleName);
  }, [roleName]);

  const renderFreshBtn = (style = {}) => {
    return (<FreshBtn
      onClick={onFresh}
      style={style}
    />);
  };

  const renderContext = () => {
    if (getUserRoleRes?.code === 'NoPermission') {
      return (
        <Fragment>
          <MessageBar
            title="子账号暂未授权 AliyunRAMReadOnlyAccess 策略"
            desc="检测子账号暂未授权 AliyunRAMReadOnlyAccess 策略,请联系主账户授权"
          />
          {renderFreshBtn({ marginTop: 8 })}
        </Fragment>);
    }
    if (getUserRoleRes?.code === 'EntityNotExist.Role') {
      return (
        <Fragment>
          <MessageBar
            title={`暂未创建 ${roleName} 角色`}
            desc={`应用中心需要您的角色中包含应用所需策略, 推荐创建并使用系统默认角色 ${roleName},请您确保授权主体为函数计算`}
            type={NoticeType.NOTICE}
          />
          <div style={{ marginTop: 8 }}>
            {renderFreshBtn()}
            <PrimaryBtn
              onClick={onCreateRole}
              style={{ marginLeft: 8 }}
              text
            >
              前往授权
            </PrimaryBtn>
          </div>
        </Fragment>);
    }
    return (
      <FormItem required label="角色名称" style={{ marginBottom: 0 }}>
        <h4 className={isEmpty(requiredPolicy) ? styles.normalRole : styles.errRole}>{roleName || '-'}</h4>
      </FormItem>);
  };

  return (
    <LoadingWrapper loading={getUserRoleLoading || getRoleListLoading} style={{ marginTop: 12 }}>
      {renderContext()}
      {
        !isEmpty(requiredPolicy) && roleListRes?.code === '200' &&
        <div className={styles.requiredPolicyAttach}>
          <p className={styles.requiredPolicy}>您当前选择的应用还需要额外一些权限：{requiredPolicy.join('、')}</p>
          {renderFreshBtn({ text: true })}
          <PrimaryBtn
            onClick={onAuthorize}
            text
            style={{ marginLeft: 8 }}
          >
            前往授权
          </PrimaryBtn>
        </div>
      }
    </LoadingWrapper>);
};

export default RoleName;
