import React, { FunctionComponent } from 'react';
import styles from './index.module.scss';

interface StepItem {
  title: string | React.ReactElement;
  description?: string | React.ReactElement;
  content: React.ReactNode;
}

interface IProps {
  stepList: StepItem[];
}

const StepContext: FunctionComponent<IProps> = ({ stepList }) => {
  if (!Array.isArray(stepList)) return null;
  return (
    <div className={styles.stepContainer}>
      {
        stepList.map((stepItem, index) => (
          <div key={`stepItem-${index}`} className={styles.stepItem}>
            <div className={styles.stepLeft}>
              <span className={styles.stepNumber}>
                {index + 1}
              </span>
              {stepList?.length > index + 1 && <span className={styles.stepTail} />}
            </div>
            <div className={styles.stepRight}>
              <h4 className={styles.title}>{stepItem.title}</h4>
              {Boolean(stepItem.description) && <p className={styles.description}>{stepItem.description}</p>}
              <section className={styles.content}>
                {stepItem.content}
              </section>
            </div>
          </div>))
      }
    </div>);
};

export default StepContext;
