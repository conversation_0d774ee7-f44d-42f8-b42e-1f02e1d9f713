import React, { FunctionComponent } from 'react';
import BaseDeploy from '@/components/QuickDeploy/BaseDeploy';
import { EQuickDeployType } from '@/components/QuickDeploy/constant';

interface IProps {
  visible: boolean;
  onClose?: () => void;
  children?: any;
  deployType: EQuickDeployType;
}

const DeployApplication: FunctionComponent<IProps> = (props) => {
  const {
    visible,
    onClose,
    children,
    deployType,
  } = props;

  return (
    <BaseDeploy
      title={deployType === EQuickDeployType.FC ? '我的应用' : '我的部署'}
      visible={visible}
      onVisibleChange={onClose}
      footer={false}
    >
      {children}
    </BaseDeploy>
  );
};

export default DeployApplication;
