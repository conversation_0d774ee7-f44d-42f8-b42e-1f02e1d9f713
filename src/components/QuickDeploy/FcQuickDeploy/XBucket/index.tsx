import React, { useEffect, useMemo } from 'react';
import { Select, Box, Loading } from '@alifd/next';
import { useRequest } from 'ice';
import { isEmpty } from 'lodash';
import FcConsoleService from '@/services/helplab/fcConsole';
import FreshBtn from '../FreshBtn';
import Href from '../Href';
import MessageBar from '../MessageBar';

interface IProps {
  title: string;
  init: Function;
  required: boolean;
  property: string;
  defaultValue: string;
  getValue: Function;
  setValue: Function;
}

const XBucket: (props: IProps) => [JSX.Element, JSX.Element] = (props) => {
  const { data: userBucketRes, loading, request: getUserBuckets } = useRequest(FcConsoleService.getUserBuckets, { manual: true });

  const { property, defaultValue, required, init, title, getValue, setValue } = props;

  const region = getValue('region');

  const tip = `请选择${title}`;

  /**
   * 刷新检查当前值是否在列表中,否则清除
   * @param source
   * @param value
   */
  const bucketExist = (source: Array<Record<string, any>>, value: string) => {
    const isExist = source.filter((x: any) => x.Region === region).find((y) => y.Name === value);
    if (!isExist) {
      setValue(property, null);
    }
  };

  const onFresh = () => {
    getUserBuckets().then((res: any) => {
      const buckets = res?.data?.ListAllMyBucketsResult?.Buckets?.Bucket || [];
      const source = Array.isArray(buckets) ? buckets : [buckets];
      bucketExist(source, getValue(property));
    });
  };

  const formatSource = useMemo(() => {
    const buckets = userBucketRes?.data?.ListAllMyBucketsResult?.Buckets?.Bucket;
    if (isEmpty(buckets)) return [];
    const source = Array.isArray(buckets) ? buckets : [buckets];
    return source.filter((x: any) => x.Region === region).map((item: any) => ({ label: item.Name, value: item.Name }));
  }, [userBucketRes, region]);

  useEffect(() => {
    getUserBuckets();
  }, []);

  useEffect(() => {
    setValue(property, null);
  }, [region]);

  return [
    <Select
      placeholder={tip}
      hasClear
      dataSource={formatSource}
      popupContainer={(d: any) => d.parentNode}
      style={{ width: '100%' }}
      {...init(`${property}`, {
        initValue: defaultValue,
        rules: [{
          required,
          message: tip,
        }],
      })}
    />,
    <Loading visible={loading} style={{ width: '100%' }}>
      <Box direction="row" align="center" spacing={20} style={{ marginTop: 8 }}>
        <FreshBtn onClick={onFresh} />
        <Href
          href="https://oss.console.aliyun.com/bucket"
        >
          创建新的OSS存储桶
        </Href>
      </Box>
      {
        userBucketRes?.code === 'AccessDenied' ? <MessageBar title="无法获取存储桶" desc="您没有列取 OSS Buckets 的权限。请联系您的主账号为您添加权限。" /> : null
      }
    </Loading>,
  ];
};

export default XBucket;
