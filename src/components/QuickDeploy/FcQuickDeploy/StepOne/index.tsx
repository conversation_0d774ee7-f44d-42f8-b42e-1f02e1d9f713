import React, { Fragment, useEffect, useState, useMemo, FunctionComponent } from 'react';
import { isEmpty } from 'lodash';
import classnames from 'classnames';
import { Icon } from '@alifd/next';
import FcDeployService, { RequiredProductRecord } from '@/services/helplab/fcConsole';
import LoadingWrapper from '../LoadingWrapper';
import Href from '../Href';
import FreshBtn from '../FreshBtn';
import styles from './index.module.scss';

interface IProps {
  requiredProducts: RequiredProductRecord[];
  onProductOpenStatus: Function;
}

const StepOne: FunctionComponent<IProps> = (props) => {
  const { requiredProducts, onProductOpenStatus } = props;

  const [productOpenStatus, setProductOpenMap] = useState<Record<string, boolean>>({});

  const [loading, setLoading] = useState<boolean>(false);

  const isAllOpen = useMemo(() => {
    return Object.values(productOpenStatus).every((item) => item);
  }, [productOpenStatus]);

  const getRequiredProductOpenStatus = async () => {
    setLoading(true);
    const promiseStatus = await Promise.all(
      requiredProducts.map((item) =>
        FcDeployService.getProductOpenStatus(item?.serviceCode)),
    );
    const mapStatus = {};
    Array.isArray(promiseStatus) && promiseStatus.forEach((item) => {
      const { ServiceCode, Statuses } = item?.data || {};
      const match = (Statuses?.Status || []).find((x: any) => x.StatusKey === 'enabled');
      mapStatus[ServiceCode] = match?.StatusValue === 'true';
    });
    setProductOpenMap(mapStatus);
    setLoading(false);
  };

  // 刷新更新状态
  const onFresh = () => {
    getRequiredProductOpenStatus();
  };

  useEffect(() => {
    onProductOpenStatus(isAllOpen);
  }, [isAllOpen]);

  useEffect(() => {
    if (isEmpty(requiredProducts)) return;
    getRequiredProductOpenStatus();
  }, [requiredProducts]);

  return (
    <LoadingWrapper loading={loading}>
      <ul className={styles.requiredProducts}>
        {
          Array.isArray(requiredProducts) && requiredProducts.map((item) => {
            const productIsOpen = productOpenStatus[item?.serviceCode] || false;
            return (
              <li
                key={item?.serviceCode}
                className={classnames(productIsOpen ? styles.openProductModule : styles.closeProductModule, styles.productItem)}
              >
                <span className={styles.productName}>{item?.serviceName}</span>
                {/* eslint-disable-next-line no-nested-ternary */}
                {productIsOpen ? <span className={styles.productOpened}>已开通</span> : item.freeUrl ?
                  <Fragment>
                    <Href
                      href={item?.freeUrl}
                      style={{ marginRight: 20 }}
                    >
                      免费试用
                    </Href>
                    <Href
                      href={item?.openUrl}
                    >
                      直接开通
                    </Href>
                  </Fragment> :
                  <Href
                    href={item?.openUrl}
                  >
                    立即开通
                  </Href>
                }
                {productIsOpen && <Icon type="success" size="xs" className={styles.successIcon} />}
              </li>
            );
          })
        }
      </ul>
      {
        !isAllOpen && <FreshBtn
          onClick={onFresh}
          style={{ marginTop: 8 }}
        />
      }
    </LoadingWrapper>);
};

export default StepOne;
