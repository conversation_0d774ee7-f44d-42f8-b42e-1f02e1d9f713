div[class*='help-doc-deploy-button'] {
  display: none;
}

.container {
  display: block;
  width: 138px;
  position: relative;
  margin-right: 12px;
  display: flex;
  align-items: center;

  &::after {
    position: absolute;
    content: "";
    height: 8px;
    width: 1px;
    background: #999;
    top: 8px;
    right: 0;
    margin-right: -12px;
  }

  .deployButton {
    width: 80px;
    height: 24px;
    line-height: 24px;
    padding: 0 6px 0 12px;
    margin-right: 8px;
    color: #ffffff;
    background-color: #1366EC;
    cursor: pointer;
    display: flex;
    align-items: center;

    &:hover {
      background: #1154C0;
    }

    i {
      color: #ffffff;
      transform: rotate(90deg);
      font-size: 13px;
      transition: transform 0.2s ease-out;
    }

    .action {
      transform: rotate(270deg);
      transition: transform 0.2s ease-in;
    }

    .deployCollapse {
      min-height: 76px;
      width: 300px;
      padding: 10px 0;
      background-color: #FFFFFF;
      box-shadow: 0 3px 12px 0 rgba(47, 49, 51, 0.12);
      border-radius: 4px;
      z-index: 1;
      position: absolute;
      right: 0;
      top: 24px;

      .deployItem {
        height: 56px;
        width: 300px;
        padding: 8px 16px;
        cursor: pointer;

        &:hover {
          background-color: #F1F2F3;
        }

        .title,
        .desc {
          font-size: 12px;
          letter-spacing: 0.4px;
          line-height: 20px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .title {
          font-weight: 500;
          color: #181818;
        }

        .desc {
          color: #81848F;
        }
      }
    }
  }

  .buttonText {
    color: #A4A4A4;
    letter-spacing: 0.34px;
    cursor: pointer;

    &:hover {
      color: #1366EC;
    }

    .preLoad {
      visibility: hidden;
      width: 0;
      height: 0;
    }
  }
}



//移动端
@media screen and (max-width: 1055px) {
  .container {
    display: none;
  }
}