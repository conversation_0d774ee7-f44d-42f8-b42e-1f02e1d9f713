
import React, { useState, useEffect } from 'react';
import classnames from 'classnames';
import { FormattedMessage, useIntl } from 'react-intl';
import { onDeployClick, onUnmounted, renderUserApplication } from '@/components/QuickDeploy/utils/renderDeploy';
import { onShowMenuSide } from '@/help-fe-common/utils/helpDoc/docDetail';
import { OreAlfaApp } from '@/components/QuickDeploy/RosQuickDeploy/CreateApp';
import styles from './index.module.scss';

const DeployButton = ({ docInfo }) => {
  const { deploys, title, nodeId } = docInfo;
  const intl = useIntl();
  const [showCollapse, setShowCollapse] = useState(false);

  useEffect(() => {
    // 组件销毁时重置状态
    return () => {
      onUnmounted();
      onShowMenuSide(2);
    };
  }, []);

  return (
    <div className={classnames(styles.container, 'help-doc-deploy')}>
      <div
        className={styles.deployButton}
        title={intl.formatMessage({ id: 'help.doc.deployButtonTip' })}
        onMouseEnter={() => { setShowCollapse(true); }}
        onMouseLeave={() => { setShowCollapse(false); }}
      >
        <FormattedMessage id="help.doc.deployButton" />
        <i className={classnames('help-iconfont help-icon-right-arrow', showCollapse ? styles.action : '')} />
        {
          showCollapse &&
          <div
            className={styles.deployCollapse}
            onMouseEnter={() => { setShowCollapse(true); }}
            onMouseLeave={() => { setShowCollapse(false); }}
          >
            {
              deploys?.map((item, index) => (
                <div
                  className={styles.deployItem}
                  onClick={() => { onDeployClick(item, title, nodeId); }}
                  key={index}
                  title={item?.desc}
                >
                  <div className={styles.title}>{item?.name}</div>
                  <div className={styles.desc}>{item?.desc}</div>
                </div>
              ))
            }
          </div>
        }
      </div>
      <div className={styles.buttonText} onClick={renderUserApplication}>
        <FormattedMessage id="help.my.deploy" />
        <div className={styles.preLoad}>
          <OreAlfaApp />
        </div>
      </div>
    </div>
  );
};

export default DeployButton;
