.helpTopSearch {
  height: auto;
  padding: 0 15px;

  .helpTopSearchBtn {
    height: 24px;
    border: none;
    outline: none;
    line-height: 24px;
    font-size: 12px;
    color: #979797;
    letter-spacing: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;

    .searchIcon {
      width: 24px; //根据视觉要求和搜索框高度保持一致
      height: 24px;
      font-size: 14px;
      text-align: center;
      line-height: 24px;
      cursor: pointer;
    }
  }

  .searchSuggest {
    position: absolute;
    top: -8px;
    right: 0;
    left: -286px;
    width: 624px;
    z-index: 10;
  }

  .suggestContainerActive {
    animation-name: suggest-container-active;
    animation-iteration-count: 1;
    animation-duration: 0.2s;
    animation-delay: 0s;
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-fill-mode: both;
  }

  &:hover {
    background-color: #ffffff;
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.1);

    .helpTopSearchBtn {
      color: #1366ec;
    }
  }
}

@keyframes suggest-container-active {
  from {
    opacity: 0;
    z-index: -1;
    width: 0;
  }

  to {
    opacity: 1;
    z-index: 99;
    width: 624px;
  }
}