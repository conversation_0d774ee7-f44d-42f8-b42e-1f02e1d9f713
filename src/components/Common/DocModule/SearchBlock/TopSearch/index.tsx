import React, { useEffect, useState } from 'react';
import classnames from 'classnames';
import { FormattedMessage } from 'react-intl';
import { getLocalSearchValue } from '@/help-fe-common/utils/helpDoc/searchStorage';
import SideSearch from '@/components/Common/DocModule/SearchBlock/SideSearch';
import styles from './index.module.scss';

interface IProps {
  defaultFilter?: string;
}

export default ({
  defaultFilter,
}: IProps) => {
  const [showSearchType, setShowSearchType] = useState<number>(0); // 搜索展示类型，0：不展示，1：下拉展示，2：侧边栏展示
  const [keywords, setKeywords] = useState('');

  const initKeywords = () => {
    const searchValue = getLocalSearchValue();
    // 从首页搜索跳转且在非首页点击，弹出侧边搜索
    if (defaultFilter !== 'all' && searchValue?.from === 'menu') {
      setKeywords(searchValue?.keywords || '');
      setShowSearchType(2);
    }
  };

  const onClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setShowSearchType(2);
    initKeywords();
  };

  const onCancelClick = () => {
    setShowSearchType(0);
    setKeywords('');
  };

  useEffect(() => {
    defaultFilter && initKeywords();
  }, [defaultFilter]);

  return (
    <div className={classnames(styles.helpTopSearch, 'help-search')} data-spm="d_help_search">
      <div
        className={classnames(styles.helpTopSearchBtn, 'help-search-btn')}
        onClick={(e) => onClick(e)}
      >
        <span>
          <FormattedMessage id="help.search.placeholder" />
        </span>
        <i className={classnames(styles.searchIcon, 'help-iconfont help-icon-sousuoicon')} />
      </div>
      {
        showSearchType === 2 &&
        <SideSearch
          keywords={keywords}
          defaultFilter={defaultFilter}
          onSelectChange={onCancelClick}
        />
      }
    </div>
  );
};
