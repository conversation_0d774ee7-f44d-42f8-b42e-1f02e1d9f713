.helpSideSearch {
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  width: 40%;
  min-width: 460px;
  height: 100vh;
  background-color: #fff;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.1);
  z-index: 1001;
  box-sizing: border-box;

  .cancelButton {
    position: absolute;
    top: 7px;
    right: 7px;
    cursor: pointer;
    color: #999;

    &:hover {
      color: #1366ec;
    }

    i {
      display: inline-block;
      font-size: 10px;
    }
  }

  .head {
    margin-top: 10px;

    .searchFilter {
      height: 44px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      position: relative;
      margin: 10px 0 3px 0;

      .filterItem {
        cursor: pointer;
        width: auto;
        height: 43px;
        line-height: 43px;
        font-size: 14px;
        color: #666;
        letter-spacing: 0;
        padding: 0 8px;
        margin: 0 10px;
        text-align: center;
        display: flex;
        justify-content: center;

        &:hover {
          color: #1366ec;
        }
      }

      .selected {
        color: #1366ec;
        border-bottom: 1px solid #1366ec;
        transition: border-bottom 0.2s ease-in-out;
      }
    }
  }

  .mainContainer {
    width: 100%;
    height: calc(100vh - 111px);
    position: relative;

    .categoryContainer {
      background-color: #f6f6f6;
      margin-top: 16px;
      padding: 20px 20px 8px 20px;
      font-size: 12px;

      .title {
        margin-bottom: 10px;
        color: #666;
        display: flex;
        justify-content: space-between;

        span {
          i {
            display: inline-block;
            cursor: pointer;

            &:hover {
              color: #1366ec;
            }
          }
        }
      }

      .content {
        .categoryItem {
          display: inline-block;
          padding-right: 20px;
          margin-bottom: 12px;
          cursor: pointer;

          &:hover {
            color: #1366ec;
          }
        }

        .highlight {
          color: #1366ec;
        }
      }
    }

    .suggestContainer {
      width: 100%;
      overflow-y: auto;
      overflow-y: overlay;
      position: relative;
      overscroll-behavior: none;

      .resultCount {
        font-size: 12px;
        color: #666;
        padding: 20px 20px 0 20px;
      }

      .suggestTip {
        width: 240px;
        position: absolute;
        top: 40%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 12px;
        color: #999;

        i {
          color: #f51212;
          margin-right: 5px;
        }
      }

      &::-webkit-scrollbar {
        height: 5px;
        width: 5px;
      }

      &::-webkit-scrollbar-thumb {
        background: #dfdfdf;
        border-radius: 5px;
      }
    }
  }

  @keyframes search-container-active {
    from {
      opacity: 0;
      z-index: -1;
      width: 0;
    }

    to {
      opacity: 1;
      z-index: 1001;
      width: 460px;
    }
  }
}
