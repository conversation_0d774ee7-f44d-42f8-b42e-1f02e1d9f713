.suggest {
  width: 100%;
  padding-top: 16px;

  .searchProduct {
    padding: 0 20px;
  }

  .searchContent {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    align-items: center;

    .productItem {
      line-height: 18px;
      height: 26px;
      font-size: 12px;
      color: #181818;
      display: inline-block;
      letter-spacing: 0;
      padding: 3px 8px;
      border: 1px solid #d8d8d8;
      margin-right: 12px;
      margin-bottom: 12px;
      cursor: pointer;
      user-select: none;
      text-align: center;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:hover {
        border: 1px solid #1366ec;
        color: #1366ec;
      }
    }

    .docItem {
      width: 100%;
      padding: 8px 20px;
      cursor: pointer;

      &:hover {
        background-color: #F5F5F6;
      }

      span {
        color: #1366ec;
      }

      .itemTitle {
        height: 30px;
        max-width: 100%;
        line-height: 30px;
        margin-right: 20px;
        font-size: 14px;
        color: #181818;
        letter-spacing: 0;
        display: inline-block;
        letter-spacing: 0;
        cursor: pointer;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        user-select: none;

        &:hover {
          color: #1366ec;
        }
      }

      .content {
        height: auto;
        line-height: 24px;
        max-width: 100%;
        font-size: 12px;
        color: #181818;
        letter-spacing: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        word-break: break-all;
        word-wrap: break-word;
        -webkit-box-orient: vertical;
        user-select: none;
      }
    }
  }

  .product {
    margin: 0 20px 10px 20px;
    padding: 20px;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.10);
    height: 80px;
    background-image: url(https://img.alicdn.com/imgextra/i3/O1CN01wBb3Do1CxpMz8yipX_!!6000000000148-2-tps-920-160.png);
    background-repeat: no-repeat;
    background-size: cover;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &:hover {
      box-shadow: 0 4px 6px 0 rgba(0, 0, 0, 0.10);
    }

    span {
      font-weight: 500;
      font-size: 16px;
      color: #181818;
    }

    .productItem {
      height: 30px;
      line-height: 30px;
      width: 120px;
      background-color: #1366ec;
      font-size: 14px;
      color: #FFF;
      text-align: center;
      cursor: pointer;

      &:hover {
        background: #1154c0;
      }
    }
  }
}