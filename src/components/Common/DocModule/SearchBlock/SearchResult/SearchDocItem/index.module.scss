.docItem {
  width: 100%;
  padding: 7px 20px 12px 20px;
  cursor: pointer;

  &:hover {
    background-color: #F5F5F6;
  }

  em {
    color: #1366ec;
    font-style: normal;
    font-weight: normal;
  }

  .itemTitle {
    height: 30px;
    max-width: 100%;
    line-height: 30px;
    margin-right: 20px;
    font-size: 14px;
    color: #181818;
    letter-spacing: 0;
    display: inline-block;
    letter-spacing: 0;
    cursor: pointer;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    user-select: none;

    &:hover {
      color: #1366ec;
    }
  }

  .content {
    height: auto;
    line-height: 24px;
    max-width: 100%;
    font-size: 12px;
    color: #666;
    letter-spacing: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    word-break: break-all;
    word-wrap: break-word;
    -webkit-box-orient: vertical;
    user-select: none;
  }

  .crumbContainer {
    margin-top: 4px;
    color: #9B9B9B;
    display: flex;
    justify-content: space-between;

    .breadcrumb {
      font-size: 12px;
      line-height: 20px;
      align-items: center;
      max-width: 100%;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
      overflow: hidden;

      .arrow {
        margin: 0 4px;
      }

      .crumb {
        color: #9b9b9b;
      }

      a {
        color: #1366ec;

        &:hover {
          color: #1366ec;
          text-decoration: underline;

          em {
            color: #1366ec;
          }
        }
      }


    }

    .button {
      height: 20px;
      width: 18px;
      text-align: right;

      i {
        font-size: 13px;
      }
    }
  }
}