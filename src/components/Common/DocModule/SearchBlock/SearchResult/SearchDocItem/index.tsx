import React, { useEffect, useMemo, useState } from 'react';
import { useHistory } from 'ice';
import { FormattedMessage } from 'react-intl';
import { extractDocumentDetail } from '@/help-fe-common/utils/helpDoc/docDetail';
import { addQuery } from '@/help-fe-common/utils/global/url/parseQuery';
import { onUrlClick, onUrlFocus } from '@/help-fe-common/utils/helpDoc/searchClick';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import { replaceScmHash } from '@/help-fe-common/utils/helpDoc/menu';
import styles from './index.module.scss';

export default ({ docData, resultIndex, keywords }) => {
  const history = useHistory();
  const [shownBreadcrumbData, setShownBreadcrumbData] = useState<any>([]);
  const [showAll, setShowAll] = useState(false);

  const breadcrumbData = useMemo(() => {
    const { breadcrumb } = extractDocumentDetail(docData);
    return breadcrumb;
  }, [docData]);

  useEffect(() => {
    if (showAll && breadcrumbData.length > 3) {
      setShownBreadcrumbData(breadcrumbData.slice(1));
    } else {
      const data = [...breadcrumbData.slice(1, 3), ...breadcrumbData.slice(-1)];
      setShownBreadcrumbData(data);
    }
  }, [breadcrumbData, showAll]);

  /** 曝光埋点
   * @param url 需要跳转的链接
   * @param index 当前搜索结果序号
   */
  const sendViewSls = (url, index?) => {
    let slsParam = {};
    if (index !== undefined) {
      slsParam = { resultIndex: index };
    }
    sendSlsLog({ page: '', action: 'focus', section: 'SuggestUrl', userParams1: 'document', userParams2: url, ...slsParam, keywords });
  };

  return (
    <div
      className={styles.docItem}
      onClick={(e) => onUrlClick(e, `${addQuery(docData?.url, 'scm', docData?.scm || '')}`, history, 'document', resultIndex, keywords)}
      data-tracker-scm={replaceScmHash(docData?.scm) || ''}
      onMouseEnter={(e) => { sendViewSls(docData?.url, resultIndex); setShowAll(true); }}
      onMouseLeave={() => { setShowAll(false); }}
    >
      <div
        className={styles.itemTitle}
        dangerouslySetInnerHTML={{ __html: docData?.title }}
      />
      <div
        className={styles.content}
        dangerouslySetInnerHTML={{ __html: docData?.content }}
      />
      <div className={styles.crumbContainer}>
        <div className={styles.breadcrumb} style={{ WebkitLineClamp: showAll ? 2 : 1 }}>
          <span className={styles.crumb}>
            <FormattedMessage id="help.search.from" />
          </span>
          {
            shownBreadcrumbData?.map((item, index) => (
              <span key={index}>
                {
                  item?.title &&
                  <>
                    {index !== 0 &&
                      <span className={styles.arrow}>&gt;</span>
                    }
                    {item?.url ?
                      <a
                        href={item?.url}
                        onMouseEnter={(e) => { onUrlFocus(e, item?.url, 'breadcrumb', resultIndex, keywords); }}
                        onClick={(e) => onUrlClick(e, `${addQuery(item?.url, 'scm', docData?.scm || '')}`,
                          history, 'breadcrumb', resultIndex, keywords)}
                        className={styles.crumb}
                        dangerouslySetInnerHTML={{ __html: item.title }}
                      /> :
                      <span className={styles.crumb} dangerouslySetInnerHTML={{ __html: item.title }} />
                    }
                  </>
                }
              </span>
            ))
          }
        </div>
        {
          !showAll &&
          <div className={styles.button}>
            <i className="help-iconfont help-icon-search-more" />
          </div>
        }
      </div>
    </div>
  );
};
