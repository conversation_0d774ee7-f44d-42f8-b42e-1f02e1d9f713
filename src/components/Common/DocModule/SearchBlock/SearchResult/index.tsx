import React from 'react';
import classnames from 'classnames';
import { useHistory } from 'ice';
import SearchDocItem from './SearchDocItem';
import { onUrlClick, onUrlFocus } from '@/help-fe-common/utils/helpDoc/searchClick';
import styles from './index.module.scss';

export default ({ data, onSelectChange }) => {
  const history = useHistory();

  return (
    <div className={styles.suggest}>
      {
        data?.products?.data?.length ?
          <ul className={classnames(styles.searchContent, styles.searchProduct)}>
            {
              data?.products?.data?.map((item, index) => (
                <div
                  key={index}
                  className={styles.productItem}
                  onMouseEnter={(e) => {
                    onUrlFocus(e, item?.url, 'product', index, data?.keywords);
                  }}
                  onClick={(e) => onUrlClick(e, item?.url, history, 'product', index, data?.keywords)}
                >
                  {item?.title}
                </div>
              ))
            }
          </ul>
          :
          null
      }
      {
        data?.documents?.data?.length ?
          <ul className={styles.searchContent} data-spm="help-search">
            {
              data?.documents?.data?.map((item, index) => (
                <SearchDocItem docData={item} key={index} resultIndex={index} keywords={data?.keywords || ''} />
              ))
            }
          </ul>
          :
          null
      }
    </div>
  );
};
