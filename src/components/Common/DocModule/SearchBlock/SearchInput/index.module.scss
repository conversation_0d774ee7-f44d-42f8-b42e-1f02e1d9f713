.searchInputContainer {
  outline: none;
  border-bottom: 1px solid #1366ec;
  margin: 0 20px;

  .searchInputElem {
    outline: none;
    padding: 7px 0 10px 0;
    width: 100%;
    font-size: 16px;
    color: #bababa;
    letter-spacing: 0.5px;
    line-height: 24px;
    background: transparent;

    .searchInputWrapper {
      width: 100%;
      height: 36px;
      line-height: 36px;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      i {
        font-size: 20px;
        color: #1366ec;
        vertical-align: sub;
        float: right;
      }

      .searchInput {
        padding-left: 10px;
        height: 36px;
        width: 92%;
        color: #3d3d3d;
        outline: none;
        border: none;
        line-height: 24px;
        background: transparent;
        font-size: 14px;
        letter-spacing: 0;
      }

      .searchButton {
        height: 36px;
        width: 36px;
        margin: 0 5px;
        color: #1366ec;
        text-align: right;
        cursor: pointer;
        line-height: 36px;
        user-select: none;
        font-size: 14px;
        border: 0;
        background-color: transparent;
        outline: none;

        &:hover {
          color: #1154c0;
        }
      }
    }
  }
}