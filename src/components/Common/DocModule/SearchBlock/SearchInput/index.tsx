import React, { useState, useEffect } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import classnames from 'classnames';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import styles from './index.module.scss';

/**
 * allowPop:输入框回车或点击按钮允许弹出侧边抽屉
 */
export default ({ keywords, allowPop, currentFilter, categoryId, level, onChange, onPop }) => {
  const intl = useIntl();
  const [searchValue, setSearchValue] = useState('');

  // input相关操作
  const onInputChange = (e) => {
    const value = e?.target?.value;
    setSearchValue(value);
  };
  const focus = () => {
    sendSlsLog({ page: '', section: 'sideSearch', userParams1: 'onFocus' });
  };

  // 搜索跳出
  const onSearchClick = (e, keyword, behavior) => {
    e.preventDefault();
    if (allowPop) {
      setTimeout(() => {
        onPop(keyword || searchValue);
      }, 20);
      sendSlsLog({ page: '', section: 'sideSearch', userParams1: behavior, userParams2: keyword || searchValue });
    }
  };

  const onKeyDownchange = (e) => {
    if (e.keyCode === 13) {
      onSearchClick(e, searchValue, 'enterKey');
    }
  };

  useEffect(() => {
    setSearchValue(keywords);
  }, [keywords]);

  useEffect(() => {
    if (!(currentFilter && categoryId)) return;
    // categoryId为-1时，表示相关分类为“全部”
    const newCategoryId = categoryId === '-1' ? '' : categoryId;
    const param = {
      keywords: searchValue?.trim() || '',
      categoryId: newCategoryId,
      topics: 'DOCUMENT,PRODUCT',
      level,
    };
    onChange(param);
  }, [searchValue, currentFilter, categoryId]);

  return (
    <div className={styles.searchInputContainer}>
      <div className={styles.searchInputElem}>
        <div className={styles.searchInputWrapper}>
          <i className="help-iconfont help-icon-sousuoicon" />
          <input
            id="top-search-input"
            value={searchValue}
            onChange={(e) => onInputChange(e)}
            placeholder={intl.formatMessage({ id: 'help.search.placeholder' })}
            className={classnames(styles.searchInput, 'helpTopSearchInput')}
            autoFocus
            onFocus={() => focus()}
            onKeyDown={(e) => onKeyDownchange(e)}
            autoComplete="off"
          />
          <button
            className={styles.searchButton}
            onClick={(e) => onSearchClick(e, searchValue, 'searchButton')}
          >
            <FormattedMessage id="help.search.searchBtnText" />
          </button>
        </div>
      </div>
    </div>
  );
};
