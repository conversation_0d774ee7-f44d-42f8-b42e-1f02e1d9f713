import React, { useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import classnames from 'classnames';
import QRCode from 'qrcode.react';
import { generateWeiboUrl } from '@/utils/helpDoc/share';
import styles from './index.module.scss';

interface IProps {
  docTitle: string;
  productTitle?: string;
}

export default ({ docTitle, productTitle }: IProps) => {
  const intl = useIntl();
  const [wxBarcode, setWxBarcode] = useState(false);
  const [ddBarcode, setDdBarcode] = useState(false);
  const [shareSwitch, setShareSwitch] = useState(false);

  return (
    <div className={styles.container}>
      <FormattedMessage id="help.doc.share">
        {(txt) => (
          <span
            className={styles.shareBtn}
            title={`${txt}`}
            data-tooltip-content="#helper-sharelist"
            onMouseEnter={() => setShareSwitch(true)}
            onMouseLeave={() => setShareSwitch(false)}
          >
            <i className={classnames('help-iconfont', 'help-icon-fenxiang', shareSwitch ? styles.action : '')} />
          </span>
        )}
      </FormattedMessage>
      {
        shareSwitch ?
          <div
            className={styles.helperSharelist}
            onMouseEnter={() => setShareSwitch(true)}
            onMouseLeave={() => setShareSwitch(false)}
          >
            <a
              className={classnames('weibo-btn', styles.shareLink)}
              target="_blank"
              rel="noreferrer"
              href={
                generateWeiboUrl(
                  intl,
                  docTitle,
                  productTitle,
                  window.location.href,
                )
              }
              data-spm-click="gostr=/aliyun;locaid=share"
            >
              <i className={`help-iconfont help-icon-xinlangicon ${styles.ribbonBtnIcon}`} />
              <FormattedMessage id="help.doc.weibo" />
            </a>
            <span
              className={classnames('weixin-btn', styles.shareLink)}
              data-tooltip-content="#tooltip_content"
              onMouseEnter={() => setWxBarcode(true)}
              onMouseLeave={() => setWxBarcode(false)}
            >
              <i className={`help-iconfont help-icon-weixinicon ${styles.ribbonBtnIcon}`} />
              <FormattedMessage id="help.doc.weixin" />
              {
                wxBarcode ?
                  <div className={styles.barcodeBox}>
                    <QRCode value={window.location.href} size={100} fgColor="#000000" />
                  </div> :
                  ''
              }
            </span>
            <span
              className={classnames('dingtalk-btn', styles.shareLink)}
              data-tooltip-content="#tooltip_content"
              onMouseEnter={() => setDdBarcode(true)}
              onMouseLeave={() => setDdBarcode(false)}
            >
              <i className={`help-iconfont help-icon-dingdingicon ${styles.ribbonBtnIcon}`} />
              <FormattedMessage id="help.doc.dingding" />
              {
                ddBarcode ?
                  <div className={styles.barcodeBox}>
                    <QRCode value={window.location.href} size={100} fgColor="#000000" />
                  </div> :
                  null
              }
            </span>
          </div> : ''
      }
    </div>
  );
};
