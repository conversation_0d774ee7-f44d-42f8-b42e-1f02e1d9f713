import React, { useMemo } from 'react';
import { FormattedMessage } from 'react-intl';
import classnames from 'classnames';
import { useLocation, useHistory } from 'ice';
import TabList from './TabList';
import TopSearch from '@/components/Common/DocModule/SearchBlock/TopSearch';
import { moduleEnum, getModuleType } from '@/utils/getModuleType';
import { extractUrlParam } from '@/help-fe-common/utils/global/url/urlExtract';
import styles from './index.module.scss';

const HelpBodyHead = ({ showSearch }) => {
  const location = useLocation();
  const history = useHistory();

  const identifier = useMemo(() => extractUrlParam(location?.pathname), [location?.pathname]);

  const highlightStatus: moduleEnum = useMemo(() => {
    return getModuleType(location?.pathname);
  }, [location?.pathname]);

  const onLinkClick = (e) => {
    e?.preventDefault();
    const href = e?.currentTarget?.getAttribute('href');
    history.push(href);
  };

  return (
    <div className={classnames(styles.helpBodyHeadBox, 'help-body-head')} data-spm="help-sub-nav">
      <div className={classnames(styles.helpBodyHead, 'help-body-head-content')}>
        <div className={styles.helpHeadDocName}>
          <a
            href="/"
            data-spm="d_logo"
            onClick={onLinkClick}
          >
            <FormattedMessage id="help.index.helpDoc" />
          </a>
        </div>
        <TabList highlightStatus={highlightStatus} />
        <div className={styles.bodyHeadRight}>
          {
            showSearch &&
            <div className={styles.helpBodyBoxHeaderSearch}>
              <TopSearch
                defaultFilter={(identifier?.nodeId || identifier?.alias) ? 'product' : 'all'}
              />
            </div>
          }
        </div>
      </div>
    </div>
  );
};


export default HelpBodyHead;
