import React, { FunctionComponent } from 'react';
import { FormattedMessage } from 'react-intl';
import { useHistory } from 'ice';
import { moduleEnum } from '@/utils/getModuleType';
import styles from './index.module.scss';

interface IProps {
  highlightStatus: moduleEnum;
}

// tab页资源
const tabArr = [
  // { id: 'help.head.helpDoc', url: '/', identify: moduleEnum.DOC_MODULE },
];

const TabList: FunctionComponent<IProps> = ({ highlightStatus }) => {
  const history = useHistory();

  const onLinkClick = (e, tabItem) => {
    e?.preventDefault();
    const href = e?.currentTarget?.getAttribute('href');
    history.push(href);
  };

  return (
    <>
      <div className={styles.tabListContainer}>
        {tabArr?.map(
          (tabItem: any, index) =>
            tabItem && (
              <a
                href={tabItem?.url}
                key={index}
                className={highlightStatus === tabItem?.identify ? styles.highlight : ''}
                onClick={(e) => onLinkClick(e, tabItem)}
              >
                <FormattedMessage id={tabItem?.id} />
              </a>
            ),
        )}
      </div>
    </>
  );
};
export default TabList;
