import React from 'react';
import { debounce, omit } from 'lodash';
import { Balloon, Button, Icon } from '@alifd/next';
import classnames from 'classnames';
import styles from './index.module.scss';

export default (props) => {

  const {
    align = 'bl',
    trigger,
    triggerClass,
    triggerType = 'click',
    tip,
    title,
    children,
    key,
    onTriggerClick = () => {},
    onCancel = () => document?.body?.click?.(),
    onOk = () => {},
    hasCancel = true,
    okProps = {},
    cancelProps = {},
    popupContainer = d => d.parentNode,
    ...reset
  } = props;

  const renderTrigger = (trigger: any, onTriggerClick: any) => (
    <div className={classnames(styles.triggerEle, triggerClass)} onClick={onTriggerClick}>
      {trigger || (
        <Button text type="primary">
          确定
        </Button>
      )}
    </div>
  );

  return <Balloon
    key={key}
    className={styles.confirm}
    offset={[0, -4]}
    closable={false}
    align={align}
    trigger={renderTrigger(trigger, onTriggerClick)}
    triggerType={triggerType}
    popupContainer={popupContainer}
    autoFocus={false}
    {...omit(reset, ['trigger', 'align', 'closable', 'triggerType'])}
  >
    <div className={styles.confirmContext}>
      <div className={styles.message}>
        <Icon type="warning" size="small" />
        <div className={styles.tips}>
          { Boolean(title) && <span className={styles.title}>{title}</span> }
          { Boolean(tip) && <span className={styles.tip}>{tip}</span> }
          { Boolean(children) && children }
        </div>
      </div>
      <div className={styles.actionBtn}>
        {hasCancel && (
          <Button size="small" onClick={onCancel} {...cancelProps}>
            {cancelProps.children || '取消'}
          </Button>
        )}
        <Button
          size="small"
          type="primary"
          onClick={debounce(onOk, 300)}
          {...okProps}
          className={[styles.btn, styles.primaryBtn].join(' ')}
        >
          {okProps.children || '确定'}
        </Button>
      </div>
    </div>
  </Balloon>
}
