$primary-color: #1366EC;

.confirm {
  min-width: 240px;
  max-width: 320px;
  padding: 16px 24px;
  border-radius: 4px;
}

.confirmContext {
  .message {
    :global {
      .next-icon {
        line-height: 24px;
        float: left;
        color: #333333;

        &:before {
          vertical-align: unset;
        }
      }
    }

    .tips {
      margin-bottom: 20px;
      padding-left: 24px;
      line-height: 24px;
      font-size: 14px;

      .title {
        font-weight: bold;
        margin-bottom: 6px;
        color: rgba(0, 0, 0, 0.88);
        display: block;
      }

      .tip {
        color: #1a1a1a;
        display: block;
        text-align: left;
      }
    }
  }

  .actionBtn {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    :global {
      .next-btn {
        min-width: 56px;
      }

      .next-btn:nth-child(even) {
        margin-left: 8px;
      }
    }
  }
}

.triggerEle {
  display: inline-block;
}

.btn.primaryBtn {
  background-color: $primary-color;
  color: #ffffff;

  &:hover {
    background-color: $primary-color;
  }
}
