/** 收藏相关接口 */
import { request } from 'ice';
import <PERSON><PERSON><PERSON>elper from '@/help-fe-common/utils/global/cookie';
import { getReqBaseURL } from '@/help-fe-common/utils/node/getContext';

const cr_token = CookieHelper.get('cr_token') || '';

export default {
  async myFavorites(productId, page, website = 'cn', language = 'zh') {
    const rst = await request({
      url: '/help/json/myFavorite.json',
      params: {
        productId,
        page,
        website,
        language,
      },
    });
    return rst;
  },

  // 详情页收藏
  async saveMyFavorites({ nodeId }) {
    const rst = await request({
      url: '/json/SaveMyFavorites2.json',
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
      },
      params: {
        nodeId,
        cr_token,
      },
    });
    return rst;
  },

  // 取消详情页收藏
  async cancelMyFavorites({ myFavoriteId, id }) {
    const rst = await request({
      url: '/json/CancelMyFavorites.json',
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
      },
      params: {
        myFavoriteId: myFavoriteId || '',
        nodeId: id,
        cr_token,
      },
    });
    return rst;
  },

  // 点赞
  async addLike({ nodeId }) {
    const rst = await request({
      baseURL: getReqBaseURL(),
      url: 'help/json/addLike.json',
      params: {
        nodeId,
      },
    });
    return rst;
  },

  // 取消点赞
  async cancelLike({ nodeId }) {
    const rst = await request({
      baseURL: getReqBaseURL(),
      url: 'help/json/cancelLike.json',
      params: {
        nodeId,
      },
    });
    return rst;
  },

  // 是否点赞
  async isLiked({ nodeId }) {
    const rst = await request({
      baseURL: getReqBaseURL(),
      url: 'help/json/isLiked.json',
      params: {
        nodeId,
      },
    });
    return rst;
  },
};
