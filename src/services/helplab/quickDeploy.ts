import { request } from 'ice';
import <PERSON><PERSON>Helper from '@/help-fe-common/utils/global/cookie';
import { EQuickDeployType } from '@/components/QuickDeploy/constant';

const cr_token = CookieHelper.get('cr_token') || '';

export interface DeployFormCfgParams {
  template: string;
}

export interface SaveDeployParams {
  applicationId: string;
  applicationType: EQuickDeployType;
  applicationName?: string;
  regionId?: string;
  deployId: string;
  deployedNodeId: string;
  deployedNodeTitle: string;
  testStepAnchor?: string;
}

export default {
  /**
   * 查询用户部署记录
   * @param params
   * @returns
   */
  async getUserDeployStatus(params) {
    const { deployId } = params;
    const res = await request({
      url: '/help/json/application/deploy/check.json',
      params: {
        deployId,
      },
    });
    return res;
  },
  /**
   * 获取表单配置信息
   * @param params
   */
  async getDeployFormConfig(params: DeployFormCfgParams) {
    const { template } = params;
    const res = await request({
      url: '/help/json/application/template.json',
      params: {
        template,
      },
    });
    return res;
  },

  /**
   * 保存成功部署的应用
   * @param params
   */
  async saveDeployRecord(params: SaveDeployParams) {
    const {
      applicationId,
      applicationName,
      deployId,
      applicationType,
      deployedNodeId,
      deployedNodeTitle,
      regionId,
      testStepAnchor } = params;
    const deployTags = JSON.stringify({ regionId, testStepAnchor });
    const res = await request({
      url: '/help/json/application/deploy/record.json',
      method: 'POST',
      data: {
        applicationName: applicationName || applicationId,
        applicationId,
        applicationType,
        deployedNodeId,
        deployedNodeTitle,
        deployTags,
        deployId,
      },
      params: {
        cr_token,
      },
    });
    return res;
  },

  /**
   * 获取用户应用列表
   *
   * @param params
   * @returns
   */
  async getDeployList(params) {
    const { pageNum, pageSize = 10, searchValue, sortBy, reverse, ...reset } = params;
    const res = await request({
      url: '/help/json/application/deploy/list.json',
      params: {
        pageNum,
        pageSize,
        searchValue,
        sortBy,
        reverse,
        ...reset,
      },
    });
    return res?.data;
  },

  /**
   * 获取模版默认事件
   * @param template
   */
  async getEventJson(template: string) {
    return request({
      url: 'help/json/application/template/event.json',
      params: {
        template,
      },
    });
  },
  /**
   * 删除一键部署
   * @param params
   */
  async deleteDeploy(params) {
    const { applicationId, applicationType } = params;
    const res = await request({
      url: '/help/json/application/deploy/delete.json',
      method: 'POST',
      data: {
        applicationId,
        applicationType,
      },
      params: {
        cr_token,
      },
    });
    return res;
  },
};
