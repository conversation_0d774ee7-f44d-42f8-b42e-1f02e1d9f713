import { request } from 'ice';
import { generateFecsUrl } from '@/help-fe-common/utils/global/request';

export default {
  /**
   * 获取ros region信息集合
   * @param language
   */
  async getRosListRegion(language = 'zh-CN') {
    const res = await request({
      url: generateFecsUrl('/data/api.json'),
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      withCredentials: true,
      params: {
        product: 'ros20190910',
        action: 'DescribeRegions',
        params: { AcceptLanguage: language },
      },
    });
    return res;
  },

  /**
   * 获取资源栈创建信息
   * @param RegionId 地域id
   * @param StackId 资源栈id
   * @returns
   */
  async getRosStack({ RegionId, StackId }) {
    const res = await request({
      url: generateFecsUrl('/data/api.json'),
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      withCredentials: true,
      params: {
        product: 'ros20190910',
        action: 'GetStack',
        params: {
          RegionId,
          StackId,
          OutputOption: 'Enabled',
          ShowResourceProgress: 'PercentageOnly',
        },
      },
    });
    return res?.data;
  },

  /**
   * 新建ros资源栈
   * @param params
   */
  async createRosStack(params: any) {
    const { RegionId, StackName, ...rest } = params;
    const res = await request({
      url: generateFecsUrl('/data/api.json'),
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      withCredentials: true,
      params: {
        product: 'ros20190910',
        action: 'CreateStack',
        params: {
          RegionId,
          StackName,
          ...rest,
        },
      },
    });
    return res;
  },
  /**
   * 查询资源栈的资源列表
   * @param params
   */
  async listStackResources(params: Record<string, any>) {
    const { StackId, RegionId } = params;
    const res = await request({
      url: generateFecsUrl('/data/api.json'),
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      withCredentials: true,
      params: {
        product: 'ros20190910',
        action: 'ListStackResources',
        params: { StackId, RegionId },
      },
    });
    return res;
  },
};

