/** helpLab相关接口 */
import { request } from 'ice';

export default {
  /**
   * 获取文档对应jupyter文件链接
   * @string id 节点ID
   * @string docHtml 文档html
   * @boolean returnOssUrl 是否返回Oss链接
   * @returns
   */
  async getJupyterUrl({
    nodeId = null,
    docHtml = null,
    returnOssUrl = true,
  }) {
    const rst = await request({
      method: 'POST',
      url: 'help/json/doc/getJupyterCodeBlocks.json',
      data: {
        nodeId,
        returnOssUrl,
        html: docHtml,
      },
    });
    return rst?.data;
  },
};
