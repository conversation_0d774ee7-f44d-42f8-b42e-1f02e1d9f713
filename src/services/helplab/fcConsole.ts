import { request } from 'ice';
import { generateFecsUrl } from '@/help-fe-common/utils/global/request';

export interface RequiredProductRecord {
  serviceName: string;
  serviceCode: string;
  openUrl: string;
  serviceBillUrl: string;
  freeUrl?: string;
  description: string;
}

export interface CheckAppNameParams {
  regionId: string;
  name: string;
}

export interface XRoleCustomValues {
  authorities: string[];
  name: string;
  service: string;
}

export default {
  /**
   * 获取云产品开通情况
   * @param params
   */
  async getProductOpenStatus(params: string) {
    const res = await request({
      url: generateFecsUrl('/data/innerApi.json'),
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      withCredentials: true,
      params: {
        product: 'Ubsms-inner',
        action: 'DescribeUserBusinessStatus',
        params: JSON.stringify({ ServiceCode: params }),
      },
    });
    return res;
  },

  /**
   * 校验应用名称是否重名
   * @param params
   */
  async checkDuplicateName(params: CheckAppNameParams) {
    const { regionId, name } = params;
    const res = await request({
      url: generateFecsUrl('/data/call.json'),
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      withCredentials: true,
      params: {
        product: 'one-console-app-fc',
        action: 'RunFunction',
        params: {
          data: {
            regionId,
            functionName: 'application/describeApplication',
            params: JSON.stringify({ name }),
          },
        },
      },
    });
    return res;
  },

  /**
   * 获取用户所拥有的角色列表
   * @param service
   */
  async getUserRoleList(service: string) {
    const res = await request({
      url: generateFecsUrl('/data/api.json'),
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      withCredentials: true,
      params: {
        product: 'resourcegroup',
        action: 'ListRolesForService',
        params: { Service: `${service}.aliyuncs.com` },
      },
    });
    return res;
  },

  /**
   * 获取当前用户角色信息
   * @param roleName
   */
  async getUserRole(roleName: string) {
    const res = await request({
      url: generateFecsUrl('/data/api.json'),
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      withCredentials: true,
      params: {
        product: 'resourcegroup',
        action: 'GetRole',
        params: { RoleName: roleName },
      },
    });
    return res;
  },

  /**
   * 获取用户对某个角色所拥护的权限集合
   * @param principalName
   */
  async getListPolicyAttachments(principalName: string) {
    const res = await request({
      url: generateFecsUrl('/data/api.json'),
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      withCredentials: true,
      params: {
        product: 'resourcegroup',
        action: 'ListPolicyAttachments',
        params: { PrincipalType: 'ServiceRole', PrincipalName: principalName, PageSize: 100 },
      },
    });
    return res;
  },

  /**
   * 创建应用表单
   * @param params
   */
  async createApplication(params: any) {
    const { name, description, template, ...reset } = params;
    const res = await request({
      url: generateFecsUrl('/data/call.json'),
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      withCredentials: true,
      params: {
        product: 'one-console-app-fc',
        action: 'RunFunction',
        params: {
          data: {
            regionId: 'cn-hangzhou',
            functionName: 'application/createApplication',
            params: JSON.stringify({
              appType: 'document',
              autoDeploy: true,
              name,
              description,
              template,
              lang: 'zh',
              parameters: reset,
            }),
          },
        },
      },
    });
    return res;
  },

  /**
   * 获取当前模板已部署的应用
   * @param template
   */
  async getTemplateApp(template: string) {
    const res = await request({
      url: generateFecsUrl('/data/call.json'),
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      withCredentials: true,
      params: {
        product: 'one-console-app-fc',
        action: 'RunFunction',
        params: {
          data: {
            regionId: 'cn-hangzhou',
            functionName: 'application/listApplication',
            params: JSON.stringify({ template }),
          },
        },
      },
    });
    return res;
  },

  /**
   * 获取用户bucket列表
   */
  async getUserBuckets() {
    const res = await request({
      url: generateFecsUrl('/data/api.json'),
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      withCredentials: true,
      params: {
        product: 'oss',
        action: 'GetService',
        params: { 'max-keys': 1000 },
      },
    });
    return res;
  },
  /**
   * 发起fc相关请求
   * @param functionName 请求方法名
   * @param data 入参
   * @returns 结果
   */
  async runFcApp(functionName: string, data: Record<string, any>) {
    const res = await request({
      url: generateFecsUrl('/data/call.json'),
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      withCredentials: true,
      params: {
        product: 'one-console-app-fc',
        action: 'RunFunction',
        params: {
          data: {
            regionId: 'cn-hangzhou',
            functionName: `application/${functionName}`,
            params: JSON.stringify(data),
          },
        },
      },
    });
    return res;
  },
};
