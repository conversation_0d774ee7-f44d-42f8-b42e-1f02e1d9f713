import request from '@/help-fe-common/services/apiRequest';

export interface ResponseResult<T> {
  success: boolean;
  data?: T;
  code: number;
}

export interface RecommendData {
  channels: Array<{ title: string; url: string }>;
  products: Array<{ title: string; url: string; desc: string }>;
}

export default {
  async getNotFoundRecommend(
    params: { },
    options?: { [key: string]: any },
  ) {
    const rst: ResponseResult<RecommendData> = await request('/help/json/exception/recommend.json', {
      method: 'GET',
      params,
      responseType: '*/*',
      ...(options || {}),
    });
    return rst?.data;
  },
};
