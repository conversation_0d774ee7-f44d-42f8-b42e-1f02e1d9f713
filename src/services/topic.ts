import request from '@/help-fe-common/services/apiRequest';

export default {
  async getTopicRecommend(
    params: {
      nodeId: string;
      alias: string|null|undefined;
      channelType: string;
    },
    options?: { [key: string]: any },
  ) {
    const rst = await request('/help/json/channel/recommend.json', {
      method: 'GET',
      params,
      responseType: '*/*',
      ...(options || {}),
    });
    return rst?.data;
  },

};
