// 引入默认全局样式
@import '@alifd/next/reset.scss';
// 帮助中心iconfont库
@import 'https://at.alicdn.com/t/a/font_2602970_lr9p7b0ame.css';

// 云产品iconfont库
@import 'https://at.alicdn.com/t/a/font_274588_djkbd8469q.css';

@import './styles/nav.scss';
// css变量文件
@import './help-fe-common/styles/variables.scss';

html {

  // mobile端设置基础字号
  @media only screen and (max-width: 1055px) {
    font-size: 100px;
  }
}

body {
  -webkit-font-smoothing: antialiased;
  color: $text-color;
  font-size: 14px;
  font-family: helvetica neue, pingfang SC, arial, hiragino sans gb, microsoft yahei ui, microsoft yahei, simsun, sans-serif;
  line-height: unset;
  overscroll-behavior: none;

  div#app {
    // 页面默认一屏最小高度
    font-size: 14px;
    min-height: 100vh;
    font-family: helvetica neue, pingfang SC, arial, hiragino sans gb, microsoft yahei ui, microsoft yahei, simsun, sans-serif;
  }
}

* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

ul {
  list-style: none;
}

p {
  padding: 0;
  margin: 0;
  font-size: 14px;
}

a {
  text-decoration: none;
}

[class*=help-icon-],
[class^=help-icon-] {
  font-family: help-iconfont !important;
}


[class*=dbl-icon-product-],
[class^=dbl-icon-product-] {
  font-family: dbl-icon-product !important;
}